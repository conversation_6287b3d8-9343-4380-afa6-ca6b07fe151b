package org.gof.demo.worldsrv.activity.data;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.vertx.core.json.JsonObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.Port;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityControlType;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.activity.EActivityType;
import org.gof.demo.worldsrv.activity.data.controldata.*;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.StringZipUtils;
import org.gof.demo.worldsrv.task.type.activityData.ActivityTaskVO;

import java.io.IOException;
import java.util.*;

public class ActivityControlObjectData implements ISerilizable {
    private static final String NEW_COMPRESSION_PREFIX = "NCM:";
    private ActControlData actControlData;//数据库数据
    private IControlData controlData;//vo数据
    private Map<Integer, ActivityTaskVO> snTaskMap = new HashMap<>();
    public int type = 0;

    public ActivityControlObjectData() {
    }

    public ActivityControlObjectData(int type, ActControlData actControlData, HumanObject humanObj) {
        this.type = type;
        this.actControlData = actControlData;
        controlData = getControlData(actControlData.getData(), type);
        if(controlData != null) {
            if(controlData instanceof AbstractControlData){
                AbstractControlData data = (AbstractControlData) controlData;
                data.setObjectData(this);
                data.setHumanObject(humanObj);
                data.init();
            }
        }
        initActivityTask(actControlData.getTaskJSON());
    }

    public ActivityControlObjectData(HumanObject humanObj, int type, ActivityVo vo) {
        this.type = type;
        actControlData = new ActControlData();
        actControlData.setActivitySn(vo.activitySn);
        actControlData.setActivityType(type);
        actControlData.setCloseTime(vo.closeTime);
        actControlData.setRound(vo.round);
        actControlData.setHumanId(humanObj.id);
        actControlData.setOpenTime(vo.openTime);
        ConfActivityControl conf = ConfActivityControl.get(vo.activitySn);
        if (conf.showTime > 0) {
            if (Port.getTime() >= vo.closeTime * 1000L) {
                actControlData.setState(EActivityType.STATE_ENDSHOW);
            }
        }
        controlData = getControlData("{}", type);
        if (controlData != null) {
            controlData.create(humanObj, vo);
            if(controlData instanceof AbstractControlData){
                AbstractControlData data = (AbstractControlData) controlData;
                data.setObjectData(this);
                data.setHumanObject(humanObj);
            }
            actControlData.setData(toMapsJson());
        }
    }

    public ActControlData getActControlData() {
        return actControlData;
    }

    public IControlData getControlData(){
        return controlData;
    }

    public <T extends AbstractControlData> T getControlDataNew(Class<T> clazz) {
        return clazz.cast(controlData);
    }

    public Map<Integer, ActivityTaskVO> getActivityTask(){
        return snTaskMap;
    }

    public ActivityTaskVO getTaskVo(int sn){
        return snTaskMap.get(sn);
    }

    public void updateControlData(){
        updateControlData(false);
    }

    public void updateControlData(boolean isSave){
        if(actControlData == null){
            Log.temp.error("===actControlData=null,id={}",actControlData.getId());
            return;
        }
        actControlData.setData(toMapsJson());
    }

    public void persist(){
        actControlData.persist();
    }

    public IControlData getControlData(String json, int type) {
        if (Utils.isEmptyJSONString(json)) {
            return ActivityControlTypeFactory.getTypeClass(type, null);
        }

        String decompressedJson;
        if (json.startsWith(NEW_COMPRESSION_PREFIX)) {
            // 新压缩方法
            decompressedJson = Utils.decompressJson(json.substring(NEW_COMPRESSION_PREFIX.length()));
        } else{
            decompressedJson = StringZipUtils.unzip(json);
        }

        return ActivityControlTypeFactory.getTypeClass(type, decompressedJson);
    }

    public String toMapsJson() {
        if(controlData == null){
            return "";
        }
        String json = controlData.toJSON();
        String compressedJson = Utils.compressJson(json);
        return compressedJson != null ? Utils.createStr("{}{}",NEW_COMPRESSION_PREFIX,compressedJson) : json;
    }


    private void initActivityTask(String json){
        if (json.startsWith(NEW_COMPRESSION_PREFIX)) {
            json = Utils.decompressJson(json.substring(NEW_COMPRESSION_PREFIX.length()));
        } else {
            json = StringZipUtils.unzip(json);
        }
        JSONArray ja = Utils.toJSONArray(json);
        for(int i = 0; i < ja.size(); i++){
            JSONObject jo = Utils.toJSONObject(ja.getString(i));
            ActivityTaskVO vo = new ActivityTaskVO(jo);
            addActivityTask(vo, false);
        }
    }
    public void addActivityTask(ActivityTaskVO vo, boolean isSave){
        snTaskMap.put(vo.taskSn, vo);
        if(isSave){
            saveActivityTask();
        }
    }

    public void setActivityTask(){
        JSONArray ja = new JSONArray();
        for(ActivityTaskVO vo : snTaskMap.values()){
            ja.add(vo.toString());
        }
        String json = ja.toJSONString();
        String compressedJson = Utils.compressJson(json);
        if(compressedJson != null && compressedJson.length() < json.length()){
            json = Utils.createStr("{}{}",NEW_COMPRESSION_PREFIX,compressedJson);
        }
        actControlData.setTaskJSON(json);
    }

    public void saveActivityTask(){
        setActivityTask();
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(actControlData);
        out.write(controlData);
        out.write(snTaskMap);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        actControlData = in.read();
        controlData = in.read();
        snTaskMap = in.read();
    }
}
