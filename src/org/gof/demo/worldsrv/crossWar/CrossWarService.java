package org.gof.demo.worldsrv.crossWar;

import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import org.gof.core.Port;
import org.gof.core.RemoteNode;
import org.gof.core.dbsrv.redis.*;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleMethod;
import org.gof.core.support.*;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.distr.DistrKit;
import org.gof.demo.distr.admin.AdminCenterManager;
import org.gof.demo.distr.admin.AdminCenterServiceProxy;
import org.gof.demo.distr.cross.CrossHumanLoader;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.worldsrv.arena.ArenaManager;
import org.gof.demo.worldsrv.common.DataResetService;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.config.ConfCrossWarKv;
import org.gof.demo.worldsrv.config.ConfCrossWarRankReward;
import org.gof.demo.worldsrv.config.ConfRanktype;
import org.gof.demo.worldsrv.config.ConfScene;
import org.gof.demo.worldsrv.crossWar.obj.CrossWarPlayer;
import org.gof.demo.worldsrv.crossWar.scene.CrossWarScene;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.GuildServiceProxy;
import org.gof.demo.worldsrv.human.HumanBriefLoadType;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgCrossWar;
import org.gof.demo.worldsrv.msg.MsgRank;
import org.gof.demo.worldsrv.rank.RankInfo;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.*;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 跨服战
 * <AUTHOR>
 * @Param
 */

@DistrClass(
		servId = D.SERV_CROSS,
		importClass = {List.class, Param.class, Vector2D.class},
		localOnly = false
)
public class CrossWarService extends GameServiceBase {
	// 跨服战数据是否加载完成
	private boolean m_loadDone = false;
	// 匹配id， 匹配信息
	private final Map<Integer, List<Integer>> m_matchMap = new HashMap<>();
	// 服务器id, 匹配id = groupId*1000+groupIdx
	private final Map<Integer, Integer> m_serverIdMatchIdMap = new HashMap<>();
	// 上周的，服务器id, 匹配id = groupId*1000+groupIdx
	private final Map<Integer, Integer> m_lastWeekServerIdMatchIdMap = new HashMap<>();

	// 跨服战场景集合 <serverId*10000+sceneBaseId, CrossWarScene>
	private final Map<Integer, CrossWarScene> m_sceneMap = new HashMap<>();
	// 跨服战玩家集合
	private final Map<Long, CrossWarPlayer> m_playerMap = new HashMap<>();
	// 跨服统计计时器
	private final TickTimer m_ttCrossWarCheck = new TickTimer(30 * Time.SEC);
	// 跨服战开启计时器
	private final TickTimer m_ttCrossWarOpen = new TickTimer();
	// 跨服战结算计时器
	private final TickTimer m_ttCrossWarReward = new TickTimer();
	// 跨服战光明主城关闭计时器
	private final TickTimer m_ttCrossLightCity = new TickTimer();
	// 周日结算时间戳
	private long m_settleTime = 0;
	// 发送奖励计数器
	private volatile transient AtomicInteger m_sendRewardNum = new AtomicInteger(0);
	private boolean m_isSendMail = false;
	// 跨服战结算奖励VOMap
	private final Map<Integer, Map<Long, CrossWarRewardVO>> m_serverCrossWarRewardVOMap = new ConcurrentHashMap<>();

	// 跨服战通缉列表 Map<serverId, Set<playerId>>
	private final Map<Integer, Set<Long>> m_killerListMap = new ConcurrentHashMap<>();

	/**
	 * 构造函数
	 *
	 * @param port
	 */
	public CrossWarService(GamePort port) {
		super(port);
	}

	@Override
	protected void init() {
		//由于需要admin的分组配置，尽量在initAfterConnectAdmin中初始化
	}

	/**
	 * 连上admin后的初始化
	 */
	@DistrMethod
	public void initAfterConnectAdmin() {
		if(m_loadDone){
			// 已经初始化过，再进入，一般是调时间导致，不重复初始化
			return;
		}
		initCrossWarTickTimer();
		// 加载跨服战数据
		loadData();
		// 加载上周的跨服战数据
		loadLastWeekData();
	}

	@ScheduleMethod(DataResetService.CRON_WEEK_ZERO)
	public void _CRON_WEEK_ZERO() {
		// 每周0点重新加载跨服战数据
		loadData();
		// 加载上周的跨服战数据
		loadLastWeekData();
	}

	public void addMatch(int groupId, int groupIndex, List<Integer> serverIdList) {
		int matchId = groupId*1000+groupIndex;
		List<Integer> fullServerIdList = new ArrayList<>();
		for(Integer serverId : serverIdList){
			int fullServerId = Config.GAME_SERVER_PREFIX_VALUE+serverId;
			fullServerIdList.add(fullServerId);
			m_serverIdMatchIdMap.put(fullServerId, matchId);
			long openServerTime = Util.getOpenServerTime(fullServerId);
			long timeNow = Port.getTime();
			int day = Utils.getDaysBetween(timeNow, openServerTime);
//			Log.crossWar.info("serverId={} getOpenServerTime={} offsetDay={}", fullServerId, openServerTime, day);
		}
		m_matchMap.put(matchId, fullServerIdList);
//		Log.crossWar.info("addMatch matchId={} matchMap={} serverIdMatchIdMap={}", matchId, m_matchMap.size(), m_serverIdMatchIdMap.size());
	}

	public void addLastWeekMatch(int groupId, int groupIndex, List<Integer> serverIdList) {
		int matchId = groupId*1000+groupIndex;
		for(Integer serverId : serverIdList){
			int fullServerId = Config.GAME_SERVER_PREFIX_VALUE+serverId;
			m_lastWeekServerIdMatchIdMap.put(fullServerId, matchId);
		}
//		Log.crossWar.info("addLastWeekMatch matchId={} m_lastWeekServerIdMatchIdMap={}", matchId, m_lastWeekServerIdMatchIdMap.size());
	}

	/**
	 * 获取本周指定serverId的跨服战唯一id
	 * @param serverId
	 * @return
	 */
	public String getCurrentWeekUniqueId(int serverId) {
		String date = Utils.formatTime(Utils.getTimeOfWeek(Port.getTime(), 6, 0), "yyyyMMdd");
		return Utils.createStr("{}:{}", date, getMatchId(serverId));
	}

	/**
	 * 获取上周指定serverId的跨服战唯一id，活动时间内则显示本周数据
	 * @param serverId
	 * @param isHistoryReport
	 * @return
	 */
	public String getLastWeekUniqueId(int serverId, boolean isHistoryReport) {
		long timeNow = Port.getTime();
		// 活动开放时间，如果取上周战报是周六10点刷新，取其他是周六0点刷新
		long timeOpen = Utils.getTimeOfWeek(timeNow, 6, isHistoryReport ? 10 : 0);
		if(timeNow >= timeOpen){
			// 如果在活动时间内，取本周数据
			String date = Utils.formatTime(Utils.getTimeOfWeek(timeNow, 6, 0), "yyyyMMdd");
			return Utils.createStr("{}:{}", date, getMatchId(serverId));
		}
		String date = Utils.formatTime(Utils.getTimeOfWeek(timeNow - Time.WEEK, 6, 0), "yyyyMMdd");
		return Utils.createStr("{}:{}", date, getLastWeekMatchId(serverId));
	}

	public int getMatchId(int serverId){
		return m_serverIdMatchIdMap.getOrDefault(serverId, 0);
	}

	public List<Integer> getMatch(int serverId){
		return m_matchMap.getOrDefault(getMatchId(serverId), null);
	}

	public int getLastWeekMatchId(int serverId){
		return m_lastWeekServerIdMatchIdMap.getOrDefault(serverId, 0);
	}

	/**
	 * 初始化跨服战倒计时
	 */
	private void initCrossWarTickTimer() {
		// 初始化开启结算倒计时
		initOpenAndRewardTickTimer();
		// 初始化光明主城关闭倒计时
		initCloseLightCityTickTimer();
	}

	/**
	 * 初始化开启结算倒计时
	 */
	private void initOpenAndRewardTickTimer() {
		m_ttCrossWarOpen.stop();
		m_ttCrossWarReward.stop();
		long timeNow = Port.getTime();
		ConfCrossWarKv confKv23 = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_23);
		int end = confKv23 != null ? confKv23.value[1] : 23;
		ConfCrossWarKv confKv110 = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_110);
		long timeOpen = Utils.getTimeOfWeek(timeNow, 6, 0);
		long timeEnd = Utils.getTimeOfWeek(timeNow, 7, end) + (confKv110 != null ? confKv110.value[0] * Time.SEC : 600 * Time.SEC);
		m_ttCrossWarOpen.start(timeOpen, Time.WEEK, timeNow < timeEnd);
		m_ttCrossWarReward.start(timeEnd, Time.WEEK, timeNow < timeEnd);
		Log.crossWar.info("init m_ttCrossWarOpen={} m_ttCrossWarReward={}", m_ttCrossWarOpen, m_ttCrossWarReward);
	}

	/**
	 * 初始化光明主城关闭倒计时
	 */
	private void initCloseLightCityTickTimer() {
		m_ttCrossLightCity.stop();
		long timeNow = Port.getTime();
		ConfCrossWarKv confKv = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_109);
		int end = confKv != null ? confKv.value[1] : 23;
		long timeEnd = Utils.getTimeOfWeek(timeNow, 7, end);
		m_ttCrossLightCity.start(timeEnd, Time.WEEK, timeNow < timeEnd);
		Log.crossWar.info("init m_ttCrossLightCity={}", m_ttCrossLightCity);
	}

	/**
	 * 跨服战清除内存旧数据
	 */
	public void clearAll(){
		Log.crossWar.info("===跨服战清除内存旧数据");
		m_matchMap.clear();
		m_serverIdMatchIdMap.clear();
		m_loadDone = false;
		m_sceneMap.clear();
		m_playerMap.clear();
		m_sendRewardNum = new AtomicInteger(0);
		m_isSendMail = false;
	}

	public void initRank(){
		// 活动开始时，初始化未获得积分的服务器积分为0
		m_matchMap.values().forEach(serverIdList->{
			serverIdList.forEach(serverId->{
				String serverKey = getCrossWarRankKey(serverId, CrossWarConst.rank_1032);
				RedisTools.updateRankWithTimestamp(EntityManager.redisClient, serverKey, serverId, 0, res2->{
					if(res2.failed()){
						return;
					}
					RedisTools.expire(EntityManager.redisClient, serverKey, CrossWarConst.crossWarRank_expireTime);
				});
			});
		});
		Log.crossWar.info("initRank success!");
	}

	/**
	 * 加载跨服战数据
	 */
	private void loadData(){
		// 跨服战清除内存旧数据
		clearAll();
		Log.crossWar.info("===加载跨服战数据");
		AdminRedis.get(RedisKeys.arenaCrossSeason,  res -> {
			if(res.failed()){
				Log.crossWar.error("获取赛季失败");
				return;
			}
			int season = Utils.intValue(res.result());
			AdminCenterServiceProxy proxy = AdminCenterManager.createAdminProxy();
			proxy.getCrossGroup(CrossType.cross_war.getType(), Config.SERVER_ID);
			proxy.listenResult((results, context) -> {
				Set<Integer> groupSet = results.get("groupSet");
				Log.crossWar.info("loadGroupData!! groupSet={}", groupSet);
				AtomicInteger loadNum = new AtomicInteger(0);
				for(int groupId : groupSet){
					loadNum.incrementAndGet();
					AdminRedis.get(RedisKeys.admin_server_zone_group_max + season + groupId, res2 -> {
						if(res2.failed()){
							Log.crossWar.error("获取组最大数量失败season={}, groupId={}", season, groupId);
							return;
						}
						AtomicInteger loadNum2 = new AtomicInteger(0);
						int groupMax = Utils.intValue(res2.result());
						if(groupMax == 0){
							loadNum.decrementAndGet();
							if(loadNum.get() <= 0){
								m_loadDone = true;
								Log.crossWar.info("===serverId={},跨服战当前赛季={}, groupSet={} 数据加载完成 matchMap={} serverIdMatchIdMap={}",
										Config.SERVER_ID, season, groupSet, m_matchMap.size(), m_serverIdMatchIdMap.size());
							}
							return;
						}
						for(int groupIndex = 1; groupIndex <= groupMax; groupIndex++){
							String redisKey = ArenaManager.inst().getArenaCrossGroupServerListKey(season, groupId, groupIndex);
							int finalGroupIndex = groupIndex;
							loadNum2.incrementAndGet();
							AdminRedis.get(redisKey, res3 -> {
								if(res3.failed()){
									Log.crossWar.error("获取组内信息失败season={}, groupId={} groupIndex={}", season, groupId, finalGroupIndex);
									return;
								}
								String result = res3.result();
								if(result == null){
									Log.crossWar.error("获取组内信息失败season={}, groupId={} groupIndex={}", season, groupId, finalGroupIndex);
									return;
								}
								List<Integer> serverIdList = Utils.strToIntList(result);
								addMatch(groupId, finalGroupIndex, serverIdList);
								loadNum2.decrementAndGet();
								if(loadNum2.get() <= 0){
									loadNum.decrementAndGet();
									if(loadNum.get() <= 0){
										m_loadDone = true;
										Log.crossWar.info("===serverId={},跨服战当前赛季={}, groupSet={} 数据加载完成 matchMap={} serverIdMatchIdMap={}",
												Config.SERVER_ID, season, groupSet, m_matchMap.size(), m_serverIdMatchIdMap.size());
									}
								}
							});
						}
					});
				}
			});
		});
	}

	/**
	 * 加载上周的跨服战数据
	 */
	private void loadLastWeekData(){
		m_lastWeekServerIdMatchIdMap.clear();
		Log.crossWar.info("===加载上周的跨服战数据");
		AdminRedis.get(RedisKeys.arenaCrossSeason,  res -> {
			if(res.failed()){
				Log.crossWar.error("获取赛季失败");
				return;
			}
			int lastSeason = Utils.intValue(res.result()) - 1;
			if(lastSeason <= 0){
				Log.crossWar.error("跨服战没有上周数据，lastSeason={}", lastSeason);
				return;
			}
			AdminCenterServiceProxy proxy = AdminCenterManager.createAdminProxy();
			proxy.getCrossGroup(CrossType.cross_war.getType(), Config.SERVER_ID);
			proxy.listenResult((results, context) -> {
				Set<Integer> groupSet = results.get("groupSet");
				Log.crossWar.info("loadGroupData!! groupSet={}", groupSet);
				AtomicInteger loadNum = new AtomicInteger(0);
				for(int groupId : groupSet){
					loadNum.incrementAndGet();
					AdminRedis.get(RedisKeys.admin_server_zone_group_max + lastSeason + groupId, res2 -> {
						if(res2.failed()){
							Log.crossWar.error("获取组最大数量失败season={}, groupId={}", lastSeason, groupId);
							return;
						}
						AtomicInteger loadNum2 = new AtomicInteger(0);
						int groupMax = Utils.intValue(res2.result());
						if(groupMax == 0){
							loadNum.decrementAndGet();
							if(loadNum.get() <= 0){
								Log.crossWar.info("===serverId={},跨服战上一个赛季={}, groupSet={} 数据加载完成 m_lastWeekServerIdMatchIdMap={}",
										Config.SERVER_ID, lastSeason, groupSet, m_lastWeekServerIdMatchIdMap.size());
							}
							return;
						}
						for(int groupIndex = 1; groupIndex <= groupMax; groupIndex++){
							String redisKey = ArenaManager.inst().getArenaCrossGroupServerListKey(lastSeason, groupId, groupIndex);
							int finalGroupIndex = groupIndex;
							loadNum2.incrementAndGet();
							AdminRedis.get(redisKey, res3 -> {
								if(res3.failed()){
									Log.crossWar.error("获取组内信息失败season={}, groupId={} groupIndex={}", lastSeason, groupId, finalGroupIndex);
									return;
								}
								String result = res3.result();
								if(result == null){
									Log.crossWar.error("获取组内信息失败season={}, groupId={} groupIndex={}", lastSeason, groupId, finalGroupIndex);
									return;
								}
								List<Integer> serverIdList = Utils.strToIntList(result);
								addLastWeekMatch(groupId, finalGroupIndex, serverIdList);
								loadNum2.decrementAndGet();
								if(loadNum2.get() <= 0){
									loadNum.decrementAndGet();
									if(loadNum.get() <= 0){
										Log.crossWar.info("===serverId={},跨服战上一个赛季={}, groupSet={} 数据加载完成 m_lastWeekServerIdMatchIdMap={}",
												Config.SERVER_ID, lastSeason, groupSet, m_lastWeekServerIdMatchIdMap.size());
									}
								}
							});
						}
					});
				}
			});
		});
	}

	/**
	 * 加载跨服战场景
	 */
	private void loadScene(){
		List<ConfScene> confSceneList = ConfScene.findAll().stream().filter(v->v.type == 1 || v.type == 2).collect(Collectors.toList());
		m_matchMap.values().forEach(serverIdList->{
			serverIdList.forEach(serverId->{
				confSceneList.forEach(confScene->{
					CrossWarScene scene = new CrossWarScene(this, confScene.sn, serverId);
					if(!scene.initScene()){
						Log.crossWar.warn("initScene失败={}", scene);
						return;
					}
					addScene(scene);
				});
			});
		});
		Log.crossWar.info("loadScene success! sceneNum={}", m_sceneMap.size());
	}

	@Override
	public void pulseOverride() {
		if(!m_loadDone){
			return;
		}
		long timeNow = Port.getTime();
		if (m_ttCrossWarOpen.isPeriod(timeNow, true)) {
			// 跨服战开始
			crossWarStart();
		}
		if (m_ttCrossLightCity.isPeriod(timeNow, true)) {
			// 关闭光明主城
			closeLightCity();
		}
		if (m_ttCrossWarReward.isPeriod(timeNow, true)) {
			// 周日22点踢人
			kickAllPlayer();
			// 踢出所有玩家5秒后再结算，避免踢出时个人积分创新高，会覆盖服务器积分
			m_settleTime = timeNow + 5 * Time.SEC;
		}
		if(m_settleTime > 0 && timeNow > m_settleTime){
			m_settleTime = 0;
			// 跨服战结算
			crossWarReward();
		}
		// 玩家心跳
		List<CrossWarPlayer> playerList = new ArrayList<>(m_playerMap.values());
		for(CrossWarPlayer player : playerList){
			player.pulse();
		}
		// 场景心跳
		List<CrossWarScene> sceneList = new ArrayList<>(m_sceneMap.values());
		for(CrossWarScene scene : sceneList){
			scene.pulse();
		}
		if(m_ttCrossWarCheck.isPeriod(timeNow)){
			Log.crossWar.info("===跨服 playerNum={} sceneNum={} killerListMap={}", m_playerMap.size(), m_sceneMap.size(), m_killerListMap);
		}
	}

	@DistrMethod
	public void getCrossWarInfo(long humanId, int serverId){
		List<Integer> serverIdList = getMatch(serverId);
		if(serverIdList == null){
			Log.crossWar.warn("跨服战匹配记录不存在 serverId={}", serverId);
			port.returns("result", false);
			return;
		}
		long pid = port.createReturnAsync();
		getServerList(serverIdList, res->{
			if (!res.succeeded()) {
				port.returnsAsync(pid, "result", false);
				return;
			}
			List<Define.p_cross_war_serv> list = res.result();
			getHistoryReport(humanId, serverId, res2->{
				if (!res2.succeeded()) {
					port.returnsAsync(pid, "result", false);
					return;
				}
				Define.p_cross_war_history_report historyReport = res2.result();
				port.returnsAsync(pid, "result", true, "serverList", list, "historyReport", historyReport,
						"hasPlayer", getPlayer(humanId) != null);
			});
		});
	}

	/**
	 * 获取跨服战总览界面的服务器列表
	 * @param serverIdList
	 * @param onComplete
	 */
	private void getServerList(List<Integer> serverIdList, Handler<AsyncResult<List<Define.p_cross_war_serv>>> onComplete){
		List<Define.p_cross_war_serv> serverList = new ArrayList<>();
		AtomicInteger loadNum = new AtomicInteger(0);
		for(Integer servId : serverIdList){
			// 服务器列表每周一0点刷新，根据servId取本周的唯一id
			String uniqueId = getCurrentWeekUniqueId(servId);
			Define.p_cross_war_serv.Builder dInfo = Define.p_cross_war_serv.newBuilder();
			dInfo.setServId(Utils.getServerIdTo(servId));
			CrossWarScene lightCity = getScene(servId, CrossWarConst.SCENE_SN_LIGHT_CITY);
			if(lightCity != null){
				dInfo.setHolyNum(lightCity.getMonsterMap().size());
			}
			loadNum.incrementAndGet();
			String serverKey = RedisKeys.cross_war_1032_rank + uniqueId;
			RedisTools.getMyScore(EntityManager.redisClient, serverKey, servId, ret->{
				if (!ret.succeeded()) {
					Log.crossWar.error("获取score失败, key={}", serverKey);
					AsyncActionResult.fail(port, onComplete, ret.cause());
					return;
				}
				int score = Utils.intValue(ret.result());
				int finalScore = CrossWarUtils.getServerFinalScore(score, lightCity != null ? lightCity.getMonsterMap().size() : 0);
				RedisTools.getMyRank(EntityManager.redisClient, serverKey, String.valueOf(servId), ret2->{
					if (!ret2.succeeded()) {
						Log.crossWar.error("获取rank失败, key={}", serverKey);
						AsyncActionResult.fail(port, onComplete, ret2.cause());
						return;
					}
					int rank = Utils.intValue(ret2.result());
					dInfo.setPoint(finalScore);
					dInfo.setRank(rank);
					String worldNodeId = DistrKit.getWorldNodeID(servId);
					RemoteNode rn = Port.getCurrent().getNode().getRemoteNode(worldNodeId);
					if(rn == null) {
						// 游戏服务器没连上跨服，就不取这个服的最强公会旗帜
						serverList.add(dInfo.build());
						loadNum.decrementAndGet();
						if(loadNum.get() <= 0){
							AsyncActionResult.success(port, onComplete, serverList);
						}
						return;
					}
					GuildServiceProxy proxy = GuildServiceProxy.newInstance(worldNodeId);
					proxy.getTopCombatGuildFlagList(servId);
					proxy.listenResult((results, context) -> {
						boolean bResult = Utils.getParamValue(results, "result", false);
						if (bResult) {
							List<Define.p_key_value_string> flagList = Utils.getParamValue(results, "flagList", null);
							if(flagList != null){
								dInfo.addAllGuildFlag(flagList);
							}
						}
						serverList.add(dInfo.build());
						loadNum.decrementAndGet();
						if(loadNum.get() <= 0){
							AsyncActionResult.success(port, onComplete, serverList);
						}
					});
				});
			});
		}
	}

	/**
	 * 获取跨服战周战报
	 * @param humanId
	 * @param serverId
	 * @param onComplete
	 */
	private void getHistoryReport(long humanId, int serverId, Handler<AsyncResult<Define.p_cross_war_history_report>> onComplete){
		Define.p_cross_war_history_report.Builder dInfo = Define.p_cross_war_history_report.newBuilder();
		String uniqueId = getLastWeekUniqueId(serverId, true);
		String defPlayerKey = RedisKeys.cross_war_1028_rank + uniqueId;
		String killMonsterKey = RedisKeys.cross_war_1042_rank + uniqueId;
		String personalKey = RedisKeys.cross_war_1031_rank + uniqueId;
		String serverKey = RedisKeys.cross_war_1032_rank + uniqueId;
		RedisTools.getMyRank(EntityManager.redisClient, serverKey, String.valueOf(serverId), ret->{
			if (!ret.succeeded()) {
				Log.crossWar.error("获取rank失败, key={}", serverKey);
				AsyncActionResult.fail(port, onComplete, ret.cause());
				return;
			}
			int serverRank = Utils.intValue(ret.result());
			RedisTools.getMyRank(EntityManager.redisClient, personalKey, String.valueOf(humanId), ret2->{
				if (!ret2.succeeded()) {
					Log.crossWar.error("获取rank失败, key={}", personalKey);
					AsyncActionResult.fail(port, onComplete, ret2.cause());
					return;
				}
				int personalRank = Utils.intValue(ret2.result());
				RedisTools.getMyScore(EntityManager.redisClient, personalKey, humanId, ret3->{
					if (!ret3.succeeded()) {
						Log.crossWar.error("获取score失败, key={}", personalKey);
						AsyncActionResult.fail(port, onComplete, ret3.cause());
						return;
					}
					int personalScore = Utils.intValue(ret3.result());
					RedisTools.getMyScore(EntityManager.redisClient, killMonsterKey, humanId, ret4->{
						if (!ret4.succeeded()) {
							Log.crossWar.error("获取score失败, key={}", killMonsterKey);
							AsyncActionResult.fail(port, onComplete, ret4.cause());
							return;
						}
						int killMonsterNum = Utils.intValue(ret4.result());
						RedisTools.getMyScore(EntityManager.redisClient, defPlayerKey, humanId, ret5->{
							if (!ret5.succeeded()) {
								Log.crossWar.error("获取score失败, key={}", defPlayerKey);
								AsyncActionResult.fail(port, onComplete, ret5.cause());
								return;
							}
							int defPlayerNum = Utils.intValue(ret5.result());
							dInfo.setServRank(serverRank);
							dInfo.setSelfRank(personalRank);
							dInfo.setSelfPoint(personalScore);
							dInfo.setSelfKill(defPlayerNum);
							dInfo.setSelfMonster(killMonsterNum);
							AsyncActionResult.success(port, onComplete, dInfo.build());
						});
					});
				});
			});
		});
	}

	/**
	 * 跨服战开始
	 */
	private void crossWarStart() {
		Log.crossWar.info("跨服战开始!!! m_ttCrossWarOpen={}", m_ttCrossWarOpen);
		// 初始化排行榜
		initRank();
		// 加载场景
		loadScene();
	}

	/**
	 * 玩家登录完成
	 * @param humanId
	 */
	@DistrMethod
	public void onPlayerLoginFinish(long humanId){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		// 登录时如果在战斗中，则结束战斗
		if(player.getBattleId() > 0){
			Log.crossWar.warn("onPlayerLoginFinish endBattle! player={} battleId={}", humanId, player.getBattleId());
			player.endBattle();
		}
		if(player.isInvade()){
			// 登录后，如果玩家在场景中，记录返回的sceneBaseId和Pos，让客户端可以把玩家拉进地图
			player.backupSceneBaseIdAndPos();
			// 如果在入侵，则清空当前视野，并停止视野更新，发送sceneInfo
			player.clearAndStopUpdateView();
			player.sendSceneInfo();
		}
		else{
			// 登录时如果在本服防御，退出本次跨服战
			player.exitCrossWar();
		}
	}

	/**
	 * 玩家离线
	 * @param humanId
	 */
	@DistrMethod
	public void onPlayerLogout(long humanId){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		// 离线时如果在战斗中，则结束战斗
		if(player.getBattleId() > 0){
			Log.crossWar.warn("onPlayerLogout endBattle! player={} battleId={}", humanId, player.getBattleId());
			player.endBattle();
		}
		if(player.isInvade()){
			// 如果在入侵，则清空当前视野，并停止视野更新
			player.clearAndStopUpdateView();
		}
		else{
			// 离线时如果在本服防御，退出本次跨服战
			player.exitCrossWar();
		}
	}

	/**
	 * 玩家重连
	 * @param humanId
	 */
	@DistrMethod
	public void onPlayerReconnect(long humanId){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		Log.crossWar.info("===玩家{}重连，重新进入场景{}", player.getObjId(), player.getSceneId());
		int sceneBaseId = player.getSceneBaseId();
		Vector2D pos = player.getNowPos();
		player.enterScene(sceneBaseId, pos);
	}

	/**
	 * 选择服务器
	 * @param humanId
	 * @param param
	 */
	@DistrMethod
	public void selectServer(long humanId, Param param){
		int serverId = param.get("serverId");
		int myServerId = param.get("myServerId");
		int job = param.get("job");
		boolean isFirstInvade = param.get("isFirstInvade");
		long matchId = getMatchId(serverId);
		if(matchId == 0){
			Log.crossWar.warn("跨服战匹配记录不存在 serverId={}", serverId);
			port.returns("result", new ReasonResult(false));
			return;
		}
		if(myServerId != serverId){
			// 入侵
			long myMatchId = getMatchId(myServerId);
			if(myMatchId == 0){
				Log.crossWar.warn("跨服战匹配记录不存在 serverId={}", myServerId);
				port.returns("result", new ReasonResult(false));
				return;
			}
			if(matchId != myMatchId){
				Log.crossWar.warn("跨服战服务器选择失败，serverId={} myServerId={} 不在一个匹配组中", serverId, myServerId);
				port.returns("result", new ReasonResult(false));
				return;
			}
		}
		CrossWarPlayer oldPlayer = getPlayer(humanId);
		if(oldPlayer != null && oldPlayer.getServerId() == myServerId){
			Log.crossWar.info("player={}目前在本服={}，选择服务器={}，先退出本服", humanId, myServerId, serverId);
			oldPlayer.exitCrossWar();
		}
		long pid = port.createReturnAsync();
		CrossWarPlayer player = new CrossWarPlayer(this, humanId, serverId, myServerId, job, isFirstInvade);
		player.initPlayer(res->{
			if (!res.succeeded()) {
				// 获取humanBrief失败，则去游戏服拉取
				HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(myServerId));
				proxy.getSyncHumanBrief(Collections.singletonList(humanId));
				port.returnsAsync(pid, "result", new ReasonResult(false));
				return;
			}
			if(!addPlayer(player)){
				port.returnsAsync(pid, "result", new ReasonResult(false, String.valueOf(ErrorTip.CrossWarAlreadyInServer)));
				return;
			}
			// 发送玩家的场景信息
			player.sendSceneInfo();
			port.returnsAsync(pid, "result", new ReasonResult(true));
		});
	}

	/**
	 * 传送并关闭
	 * @param humanId
	 */
	@DistrMethod
	public void transferClose(long humanId){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		// 退出本次跨服战
		player.exitCrossWar();
	}

	public CrossWarPlayer getPlayer(long humanId) {
		return m_playerMap.get(humanId);
	}

	/**
	 * 添加跨服玩家
	 * @param player
	 * @return
	 */
	public boolean addPlayer(CrossWarPlayer player){
		CrossWarPlayer exist = m_playerMap.get(player.getObjId());
		if(exist != null){
			Log.crossWar.warn("添加跨服玩家失败 player={}已存在", exist);
			return false;
		}
		m_playerMap.put(player.getObjId(), player);
		Log.crossWar.info("添加跨服玩家成功！ player={}", player);
		return true;
	}

	/**
	 * 移除跨服玩家
	 *
	 * @param player
	 */
	public void removePlayer(CrossWarPlayer player){
		m_playerMap.remove(player.getObjId());
		removeFromKillerList(player);
		Log.crossWar.info("移除跨服玩家成功! player={}", player);
	}

	/**
	 * 获取玩家的场景信息
	 * @param humanId
	 */
	@DistrMethod
	public void getSceneInfo(long humanId){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		player.sendSceneInfo();
	}

	/**
	 * 添加场景
	 *
	 * @param scene
	 */
	public void addScene(CrossWarScene scene){
		CrossWarScene exist = m_sceneMap.get(scene.getSceneId());
		if(exist != null){
			Log.crossWar.warn("添加跨服场景失败 scene={}已存在", exist);
			return;
		}
		m_sceneMap.put(scene.getSceneId(), scene);
//		Log.crossWar.info("添加跨服场景成功！ scene={}", scene);
	}

	public CrossWarScene getScene(int serverId, int sceneBaseId) {
		int sceneId = CrossWarScene.generateSceneId(serverId, sceneBaseId);
		CrossWarScene scene = m_sceneMap.get(sceneId);
		if(scene == null){
			if (CrossWarUtils.canInvade()) {
				Log.crossWar.warn("===sceneId={} is not exist", sceneId);
			}
			return null;
		}
		return scene;
	}

	/**
	 * 进入大地图
	 * @param humanId
	 * @param sceneBaseId
	 * @param pos
	 */
	@DistrMethod
	public void enterBigMap(long humanId, int sceneBaseId, Vector2D pos){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		if(!Config.DATA_DEBUG && CrossWarUtils.isHolyCity(sceneBaseId) && !CrossWarUtils.isHolyOpen()){
			Log.crossWar.info("无法进入大地图，光明主城未开放");
			return;
		}
		player.enterBigMap(sceneBaseId, pos);
	}

	/**
	 * 大地图滑动
	 * @param humanId
	 * @param pos
	 */
	@DistrMethod
	public void bigMapSlide(long humanId, Vector2D pos){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		player.bigMapSlide(pos);
	}

	/**
	 * 进入场景
	 * @param humanId
	 * @param type
	 * @param pos
	 */
	@DistrMethod
	public void enterScene(long humanId, int type, Vector2D pos){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		player.tryEnterScene(type, pos);
	}

	/**
	 * 离开场景
	 * @param humanId
	 */
	@DistrMethod
	public void leaveScene(long humanId){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		player.exitCrossWar();
	}

	/**
	 * 移动
	 * @param humanId
	 * @param param
	 */
	@DistrMethod
	public void moveTo(long humanId, Param param){
		Define.p_move_info moveInfo = Utils.getParamValue(param, "moveInfo", null);
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		player.doMoveTo(moveInfo);
	}

	/**
	 * 结束战斗
	 * @param humanId
	 */
	@DistrMethod
	public void endBattle(long humanId){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		player.endBattle();
	}

	/**
	 * 复活
	 * @param humanId
	 * @param type
	 * @param costItemEnough
	 */
	@DistrMethod
	public void revive(long humanId, int type, boolean costItemEnough){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			port.returns("result", false);
			return;
		}
		boolean result = player.revive(type, costItemEnough);
		port.returns("result", result, "needCost", type == CrossWarConst.REVIVE_TYPE_TRANSFER && player.isInvade());
	}

	/**
	 * 加速
	 * @param humanId
	 */
	@DistrMethod
	public void speedUp(long humanId){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		player.speedUp();
	}

	public void addToKillerList(CrossWarPlayer killer){
		// 取杀人者正在入侵的服务器id
		int serverId = killer.getServerId();
		long killerId = killer.getObjId();
		Log.crossWar.info("addToKillerList serverId={}, killerId={}", serverId, killerId);
		m_killerListMap.computeIfAbsent(serverId, k -> new HashSet<>()).add(killerId);

		CrossWarScene scene = killer.getScene();
		if(scene != null){
			// 发送通缉令跑马灯
			Define.p_lang_info.Builder content = Define.p_lang_info.newBuilder();
			content.addArgList(Define.p_key_value_name.newBuilder().setName(killer.getHumanBriefVO().name));
			content.addArgList(Define.p_key_value_name.newBuilder().setK(1).setV(scene.getSceneBaseId()));
			sendSystemMarquee(killer.getServerId(), CrossWarConst.crossWar_marquee_killList, content);
		}
	}

	public void removeFromKillerList(CrossWarPlayer killer){
		// 取杀人者正在入侵的服务器id
		int serverId = killer.getServerId();
		long killerId = killer.getObjId();
		Set<Long> killerList = m_killerListMap.get(serverId);
		if(killerList != null){
			boolean isRemoved = killerList.remove(killerId);
			if(isRemoved){
				Log.crossWar.info("removeFromKillerList serverId={}, killerId={}", serverId, killerId);
			}
		}
	}

	/**
	 * 获取通缉列表
	 * @param humanId
	 */
	@DistrMethod
	public void getKillList(long humanId){
		CrossWarPlayer player = getPlayer(humanId);
		if(player == null){
			return;
		}
		if(player.isInvade()){
			Log.crossWar.warn("玩家{}正在入侵，无法获取追杀列表", humanId);
			return;
		}
		int serverId = player.getServerId();
		Set<Long> killerSet = m_killerListMap.getOrDefault(serverId, new HashSet<>());
		List<Long> killList = new ArrayList<>(killerSet);
		MsgCrossWar.cross_war_kill_list_s2c.Builder msg = MsgCrossWar.cross_war_kill_list_s2c.newBuilder();
		for(long killerId : killList){
			CrossWarPlayer killer = getPlayer(killerId);
			if(killer == null){
				Log.crossWar.warn("get Killer{} failed", killerId);
				continue;
			}
			if(killer.getSceneBaseId() == 0){
				// 过滤不在任何场景中的，可能他正在大地图
				continue;
			}
			msg.addList(killer.to_p_cross_war_kill());
		}
		player.sendMsg(msg.build());
	}

	public String getCrossWarRankKey(int serverId, int rankType){
		// 排行榜数据，活动时间内取本周，否则取上周
		String uniqueId = getLastWeekUniqueId(serverId, false);
		switch(rankType) {
			case CrossWarConst.rank_1028:
				return RedisKeys.cross_war_1028_rank + uniqueId;
			case CrossWarConst.rank_1042:
				return RedisKeys.cross_war_1042_rank + uniqueId;
			case CrossWarConst.rank_1031:
				return RedisKeys.cross_war_1031_rank + uniqueId;
			case CrossWarConst.rank_1032:
				return RedisKeys.cross_war_1032_rank + uniqueId;
		}
		return "";
	}

	/**
	 * 获取跨服战排行榜
	 * @param humanId
	 * @param serverId
	 * @param rankType
	 * @param page
	 */
	@DistrMethod
	public void getCrossWarRank(long humanId, int serverId, int rankType, int page){
		long pid = port.createReturnAsync();
		if (page <= 0 || page > 100000) {
			// 数据异常，不处理
			Log.crossWar.info("===请求的页码异常，page={}, humanId={}", page, humanId);
			return;
		}
		ConfRanktype confRanktype = ConfRanktype.get(rankType);
		if(confRanktype == null){
			Log.crossWar.error("===没有找到排行榜类型，rankSn={}", rankType);
			return;
		}
		int pageOpen = (page - 1) * RankParamKey.pageNum;
		int pageEnd = page * RankParamKey.pageNum - 1;
		if (pageOpen > pageEnd) {
			return;
		}
		if (pageEnd >= confRanktype.show_num) {
			pageEnd = confRanktype.show_num - 1;
		}
		MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();
		Define.p_rank_data.Builder dInfo = Define.p_rank_data.newBuilder();
		dInfo.setType(rankType);
		dInfo.setServer(Utils.getServerIdTo(serverId));
		dInfo.setPage(page);
		long timeNow = Port.getTime();
		int hour = Utils.getHourOfTime(timeNow) + 1;
		int freshTime = (int) (Utils.getDayTime(timeNow, hour, 0, 0) / Time.SEC);
		dInfo.setNextFreshTime(freshTime);

		String redisKey = getCrossWarRankKey(serverId, rankType);
		int finalPageEnd = pageEnd;
		RedisTools.getRankLen(EntityManager.getRedisClient(), redisKey, rest -> {
			if (rest.failed()) {
				Log.crossWar.error("获取失败，redisKey={}", redisKey);
				return;
			}
			long total = rest.result();
			int totalNum = Utils.intValue(Math.min(total, confRanktype.show_num));
			int pageMax = Utils.intValue(Math.ceil((double) totalNum / RankParamKey.pageNum));
			dInfo.setMaxPage(pageMax);
			dInfo.setTotalNum(totalNum);
			// 填充rankData
			fillRankData(humanId, serverId, confRanktype, redisKey, pageOpen, finalPageEnd, dInfo, res->{
				if(res.failed()){
					return;
				}
//				Log.crossWar.info("getCrossWarRank pageOpen={} pageEnd={}, redisKey={} size={} maxPage={} totalNum={}",
//						pageOpen, finalPageEnd, redisKey, dInfo.getRankInfoList().size(), dInfo.getMaxPage(), dInfo.getTotalNum());
				msg.addRankDataList(dInfo);
				HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
				proxy.sendMsg(humanId, msg.build());
			});
		});
	}

	/**
	 * 填充rankData
	 * @param humanId
	 * @param serverId
	 * @param confRanktype
	 * @param redisKey
	 * @param pageOpen
	 * @param pageEnd
	 * @param dInfo
	 * @param onComplete
	 */
	private void fillRankData(long humanId, int serverId, ConfRanktype confRanktype, String redisKey, int pageOpen, int pageEnd,
							  Define.p_rank_data.Builder dInfo, Handler<AsyncResult<Void>> onComplete){
		CrossRedis.getRankListByIndex(redisKey, pageOpen, pageEnd, true, res -> {
			if(res.failed()){
				AsyncActionResult.fail(port, onComplete, res.cause());
				return;
			}
			List<String> strList = res.result().getList();
			int size = strList.size();
			int rank = pageOpen;
			Map<Long, Integer> idRankMap = new HashMap<>();
			Map<Long, Integer> idScoreMap = new HashMap<>();
			List<Long> idList = new ArrayList<>();
			for (int m = 0; m < size; m+=2) {
				++rank;
				long idTemp = Utils.longValue(strList.get(m));
				idRankMap.put(idTemp, rank);
				idScoreMap.put(idTemp, Utils.intValue(strList.get(m + 1)));
				idList.add(idTemp);
			}
			if(confRanktype.rank_type == RankParamKey.rankTypeHuman){
				if(idList.contains(humanId)){
					// 已上榜，直接调用
					fillHumanRankData(humanId, idList, idRankMap, idScoreMap, dInfo, true, onComplete);
				}
				else{
					// 未上榜，查询自己的排行信息后再调用
					CrossRedis.getMyRank(redisKey, String.valueOf(humanId), res2 -> {
						if(res2.failed()){
							AsyncActionResult.fail(port, onComplete, res2.cause());
							return;
						}
						int myRank = Utils.intValue(res2.result());
						CrossRedis.getMyScore(redisKey, humanId, res3 -> {
							if(res3.failed()){
								AsyncActionResult.fail(port, onComplete, res3.cause());
								return;
							}
							int myScore = Utils.intValue(res3.result());
							idRankMap.put(humanId, myRank);
							idScoreMap.put(humanId, myScore);
							idList.add(humanId);
							fillHumanRankData(humanId, idList, idRankMap, idScoreMap, dInfo, false, onComplete);
						});
					});
				}
			}
			else if(confRanktype.rank_type == RankParamKey.rankTypeServer){
				for(Map.Entry<Long, Integer> entry : idScoreMap.entrySet()){
					int key = Utils.intValue(entry.getKey());
					int value = entry.getValue();
					CrossWarScene lightCity = getScene(key, CrossWarConst.SCENE_SN_LIGHT_CITY);
					int finalScore = CrossWarUtils.getServerFinalScore(value, lightCity != null ? lightCity.getMonsterMap().size() : 0);
					idScoreMap.put(entry.getKey(), finalScore);
				}
				// 根据idScoreMap得到idRankMap和idList
				CrossWarUtils.reSortByIdScoreMap(idScoreMap, idRankMap, idList);
				fillServerRankData(serverId, idList, idRankMap, idScoreMap, dInfo, onComplete);
			}
		});
	}

	private void fillHumanRankData(long humanId, List<Long> idList, Map<Long, Integer> idRankMap, Map<Long, Integer> idScoreMap,
								   Define.p_rank_data.Builder dInfo, boolean isSelfOnTheList, Handler<AsyncResult<Void>> onComplete){
		CrossHumanLoader.getList(idList, HumanBriefLoadType.SHOW_INFO, res->{
			if(res.failed()){
				AsyncActionResult.fail(port, onComplete, res.cause());
				return;
			}
			List<Define.p_rank_info> list = new ArrayList<>();
			List<HumanBrief> humanBriefList = res.result();
			for(HumanBrief humanBrief : humanBriefList) {
				if (humanBrief == null) {
					continue;
				}
				try{
					RankInfo rankInfoTemp = new RankInfo();
					final int topNum = 3;
					Define.p_rank_info.Builder rankInfo = rankInfoTemp.toRankInfo(humanBrief,
							idRankMap.get(humanBrief.getId()), idScoreMap.get(humanBrief.getId()), topNum).toBuilder();
					if(humanId == humanBrief.getId()){
						dInfo.setMyRankInfo(rankInfo);
						if(isSelfOnTheList){
							// 自己不在榜上，则不加到list，只加到myRankInfo
							list.add(rankInfo.build());
						}
					}
					else{
						list.add(rankInfo.build());
					}
				} catch (Exception e) {
					Log.crossWar.error("===构建数据失败，humanId={}", humanBrief.getId(), e);
				}
			}
			if (!list.isEmpty()) {
				dInfo.addAllRankInfo(list);
			}
			AsyncActionResult.success(port, onComplete, null);
		});
	}

	private void fillServerRankData(long serverId, List<Long> idList, Map<Long, Integer> idRankMap, Map<Long, Integer> idScoreMap,
									Define.p_rank_data.Builder dInfo, Handler<AsyncResult<Void>> onComplete){
		List<Define.p_rank_info> list = new ArrayList<>();
		AtomicInteger loadNum = new AtomicInteger(0);
		for(long servId : idList) {
			Define.p_rank_info.Builder rankInfo = Define.p_rank_info.newBuilder();
			rankInfo.setScore(idScoreMap.get(servId));
			rankInfo.setRank(idRankMap.get(servId));
			rankInfo.setName(String.valueOf(servId));
			rankInfo.setServId(Utils.getServerIdTo(Utils.intValue(servId)));
			String worldNodeId = DistrKit.getWorldNodeID(Utils.intValue(servId));
			RemoteNode rn = Port.getCurrent().getNode().getRemoteNode(worldNodeId);
			if(rn == null) {
				// 游戏服务器没连上跨服，就不取这个服的最强公会旗帜
				list.add(rankInfo.build());
				if(serverId == servId){
					dInfo.setMyRankInfo(rankInfo);
				}
				continue;
			}
			loadNum.incrementAndGet();
            GuildServiceProxy proxy = GuildServiceProxy.newInstance(worldNodeId);
			proxy.getTopCombatGuildFlagList(Utils.intValue(servId));
			proxy.listenResult((results, context) -> {
				boolean bResult = Utils.getParamValue(results, "result", false);
				if (bResult) {
					List<Define.p_key_value_string> flagList = Utils.getParamValue(results, "flagList", null);
					if(flagList != null){
						rankInfo.addAllGuildFlag(flagList);
					}
				}
				list.add(rankInfo.build());
				if(serverId == servId){
					dInfo.setMyRankInfo(rankInfo);
				}
				loadNum.decrementAndGet();
				if(loadNum.get() <= 0){
					dInfo.addAllRankInfo(list);
					AsyncActionResult.success(port, onComplete, null);
				}
			});
		}
		// 有可能都取不到最强公会旗帜，会走到这里
		if(loadNum.get() <= 0){
			dInfo.addAllRankInfo(list);
			AsyncActionResult.success(port, onComplete, null);
		}
	}

	public void sendSystemMarquee(int serverId, int marqueeSn, Define.p_lang_info.Builder content){
		List<Integer> serverIdList = getMatch(serverId);
		serverIdList.forEach(servId->{
			String worldNodeId = DistrKit.getWorldNodeID(servId);
			RemoteNode rn = Port.getCurrent().getNode().getRemoteNode(worldNodeId);
			if(rn == null) {
				// 游戏服务器没连上跨服，不广播跑马灯
				return;
			}
			HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(worldNodeId);
			Param param = new Param();
			param.put("cfgId", marqueeSn);
			param.put("content", content.build());
            param.put("serverId", servId);
			proxy.sendSystemMarquee(param);
		});
	}

	/**
	 * 踢出所有玩家
	 */
	private void kickAllPlayer() {
		Log.crossWar.info("踢出所有玩家 playerNum={} m_ttCrossWarReward={}", m_playerMap.size(), m_ttCrossWarReward);
		List<CrossWarPlayer> playerList = new ArrayList<>(m_playerMap.values());
		for(CrossWarPlayer player : playerList){
			player.exitCrossWar();
		}
	}

	/**
	 * 关闭光明主城
	 */
	private void closeLightCity() {
		Log.crossWar.info("跨服战关闭光明主城!!! 踢出所有在光明主城的玩家 m_ttCrossLightCity={}", m_ttCrossLightCity);
		for(CrossWarScene scene : m_sceneMap.values()){
			if(scene.getSceneType() != CrossWarConst.SCENE_TYPE_LIGHT_CITY){
				continue;
			}
			List<CrossWarPlayer> scenePlayerList = new ArrayList<>(scene.getPlayerMap().values());
			for(CrossWarPlayer player : scenePlayerList){
				// 通知光明主城关闭
				MsgCrossWar.cross_war_is_holy_open_s2c.Builder msg = MsgCrossWar.cross_war_is_holy_open_s2c.newBuilder();
				msg.setIsHolyOpen(0);
				player.sendMsg(msg.build());
				// 离开场景
				player.leaveScene(true);
			}
		}
	}

	/**
	 * 跨服战结算
	 */
	private void crossWarReward() {
		Log.crossWar.info("跨服战结算!!! matchNum={}", m_matchMap.size());
		updateServerScoreRank(res->{
			if(res.failed()){
				return;
			}
			Log.crossWar.info("跨服战发结算邮件奖励");
			settleReward();
			Log.crossWar.info("移除所有场景");
			m_sceneMap.clear();
		});
	}

	/**
	 * 根据光明之子数量，更新本周服务器积分排名
	 */
	private void updateServerScoreRank(Handler<AsyncResult<Void>> onComplete){
		Log.crossWar.info("根据光明之子数量，更新本周服务器积分排名");
		AtomicInteger loadNum = new AtomicInteger(0);
		for(List<Integer> serverList : m_matchMap.values()){
			// 每组只取一个serverId，就不会重复
			int serverId = serverList.get(0);
			loadNum.incrementAndGet();
			String redisKey = getCrossWarRankKey(serverId, CrossWarConst.rank_1032);
			CrossRedis.getRankListByIndex(redisKey, 0, RankParamKey.pageNum, true, res -> {
				if(!res.succeeded()){
					// 更新失败，记录日志，不打断循环
					Log.crossWar.error("getRankListByIndex fail. redisKey={}", redisKey);
					loadNum.decrementAndGet();
					if(loadNum.get() <= 0){
						AsyncActionResult.success(port, onComplete, null);
					}
					return;
				}
				List<String> strList = res.result().getList();
				int size = strList.size();
				AtomicInteger loadNum2 = new AtomicInteger(0);
				for (int m = 0; m < size; m+=2) {
					int servId = Utils.intValue(strList.get(m));
					int value = Utils.intValue(strList.get(m + 1));
					CrossWarScene lightCity = getScene(servId, CrossWarConst.SCENE_SN_LIGHT_CITY);
					if(lightCity != null){
						loadNum2.incrementAndGet();
						int newValue = CrossWarUtils.getServerFinalScore(value, lightCity.getMonsterMap().size());
						RedisTools.addRankWithTimestamp(EntityManager.redisClient, redisKey, servId, newValue, res2->{
							if(res.failed()){
								// 更新失败，记录日志，不打断循环
								Log.crossWar.error("addRankWithTimestamp fail. redisKey={} servId={} newValue={}", redisKey, servId, newValue);
							}
							else{
								Log.crossWar.info("更新本周服务器积分排名. redisKey={} servId={} newValue={} oldValue={}", redisKey, servId, newValue, value);
							}
							loadNum2.decrementAndGet();
							if(loadNum2.get() <= 0){
								loadNum.decrementAndGet();
								if(loadNum.get() <= 0){
									AsyncActionResult.success(port, onComplete, null);
								}
							}
						});
					}
				}
				// 有可能都取不到光明之子数量，会走到这里
				if(loadNum2.get() <= 0){
					loadNum.decrementAndGet();
					if(loadNum.get() <= 0){
						AsyncActionResult.success(port, onComplete, null);
					}
				}
			});
		}
	}

	/**
	 * 跨服战发结算邮件奖励
	 */
	private void settleReward(){
		for(List<Integer> serverList : m_matchMap.values()){
			// 每组只取一个serverId，就不会重复
			String uniqueId = getCurrentWeekUniqueId(serverList.get(0));
			sendRankRewardToGroup(uniqueId, CrossWarConst.rank_1028, CrossWarConst.rankType_1028);
			sendRankRewardToGroup(uniqueId, CrossWarConst.rank_1042, CrossWarConst.rankType_1042);
			sendRankRewardToGroup(uniqueId, CrossWarConst.rank_1031, CrossWarConst.rankType_1031);
			sendRankRewardToGroup(uniqueId, CrossWarConst.rank_1032, CrossWarConst.rankType_1032);
		}
	}

	private void sendRankRewardToGroup(String uniqueId, int rankSn, int rankType){
		String key = "";
		int mailSn = 0;
		if (rankSn == CrossWarConst.rank_1028) {
			key = RedisKeys.cross_war_1028_rank + uniqueId;
			mailSn = CrossWarConst.crossWarRankMailSn_1028;
		} else if (rankSn ==  CrossWarConst.rank_1042) {
			key = RedisKeys.cross_war_1042_rank + uniqueId;
			mailSn = CrossWarConst.crossWarRankMailSn_1042;
		} else if (rankSn ==   CrossWarConst.rank_1031) {
			key = RedisKeys.cross_war_1031_rank + uniqueId;
			mailSn = CrossWarConst.crossWarRankMailSn_1031;
		} else if (rankSn ==   CrossWarConst.rank_1032) {
			key = RedisKeys.cross_war_1032_rank + uniqueId;
			mailSn = CrossWarConst.crossWarRankMailSn_1032;
		}
		int finalMailSn = mailSn;
		m_sendRewardNum.incrementAndGet();
		RedisTools.getRankListByIndex(EntityManager.redisClient, key, 0, -1, false, f -> {
			if (f.failed()) {
				Log.crossWar.error("getRankListByIndex fail. rankSn={}", rankSn);
				m_sendRewardNum.decrementAndGet();
				checkSendMail();
				return;
			}
			int rank = 0;
			List<String> strList = f.result().getList();
			for(String str : strList){
				long humanId = Utils.longValue(str);
				rank ++;
				int confSn = GlobalConfVal.getCrossRankSn(rankType, rank);
				ConfCrossWarRankReward conf = ConfCrossWarRankReward.get(confSn);
				if(conf == null){
					Log.crossWar.error("===ConfCrossWarRankReward配置不存在，confSn={}", confSn);
					continue;
				}
				int serverId = Utils.getServerIdByHumanId(humanId);
				if (rankSn == CrossWarConst.rank_1032){
					serverId = Utils.intValue(humanId);
				}
				CrossWarRewardVO vo  = m_serverCrossWarRewardVOMap.computeIfAbsent(serverId, k -> new ConcurrentHashMap<>()).computeIfAbsent(humanId, k -> new CrossWarRewardVO());
				vo.voId = humanId;
				vo.param.put(CrossWarUtils.getCrossWarRankTypeKey(rankSn), rankSn);
				vo.param.put(CrossWarUtils.getCrossWarRankSnConfSnKey(rankSn), confSn);
				vo.param.put(CrossWarUtils.getCrossWarRankKey(rankSn), rank);
				vo.param.put(CrossWarUtils.getCrossWarMailSnKey(rankSn), finalMailSn);
				Log.crossWar.info("跨服战humanId={}， rankSn={}, confSn={}, mailSn={}", humanId, rankSn, confSn, finalMailSn);
			}
			m_sendRewardNum.decrementAndGet();
			checkSendMail();
		});
	}

	private void checkSendMail(){
		if(m_sendRewardNum.get() > 0){
			return;
		}
		if(m_isSendMail){
			return;
		}
		m_isSendMail = true;
		for(Map.Entry<Integer, Map<Long, CrossWarRewardVO>> entry : m_serverCrossWarRewardVOMap.entrySet()){
			int serverId = entry.getKey();
			Map<Long, CrossWarRewardVO> rewardVOMap = entry.getValue();
			String worldNodeId = DistrKit.getWorldNodeID(serverId);
			RemoteNode rn = Port.getCurrent().getNode().getRemoteNode(worldNodeId);
			if(rn == null) {
				Log.crossWar.warn("===checkSendMail fail. worldNodeId={}未连接", worldNodeId);
				// 跨服战结算发送失败备份
				saveErrorBackup(false, serverId, rewardVOMap);
				continue;
			}
			try{
				HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(worldNodeId);
				proxy.sendCrossWarReward(rewardVOMap);
				proxy.listenResult((timeout, results, context) -> {
					boolean bResult = Utils.getParamValue(results, "result", false);
					if (bResult) {
						// 返回正常
						return;
					}
					// 跨服战结算发送失败备份
					saveErrorBackup(timeout, serverId, rewardVOMap);
				});
			}catch (Exception e){
				Log.crossWar.error("===跨服战结算发送奖励失败，serverId={}", serverId, e);
				// 跨服战结算发送失败备份
				saveErrorBackup(false, serverId, rewardVOMap);
			}
		}
	}

	/**
	 * 跨服战结算发送失败备份
	 * @param timeout
	 * @param serverId
	 * @param rewardVOMap
	 */
	private void saveErrorBackup(boolean timeout, int serverId, Map<Long, CrossWarRewardVO> rewardVOMap){
		Log.crossWar.warn("===timeout ={}, serverId={}, 跨服战结算发送失败备份={}", timeout, serverId, rewardVOMap.size());
		List<String> keyList = new ArrayList<>();
		keyList.add(RedisKeys.cross_war_error + serverId);
		for(CrossWarRewardVO vo : rewardVOMap.values()){
			keyList.add(vo.toString());
		}
		CrossRedis.sadd(keyList);
	}

	@DistrMethod
	public void gmCommand(String type, Param param) {
		Log.crossWar.info("===跨服战gm命令，type={}, param={}", type, param);
        if (type.equals("addRank")) {
			// 增加排行榜
			int serverId = param.get("serverId");
			List<Long> humanIdList = param.get("humanIdList");
			List<String> keyList = new ArrayList<>();
			keyList.add(getCrossWarRankKey(serverId, CrossWarConst.rank_1028));
			keyList.add(getCrossWarRankKey(serverId, CrossWarConst.rank_1042));
			keyList.add(getCrossWarRankKey(serverId, CrossWarConst.rank_1031));
			keyList.forEach(redisKey->{
				humanIdList.forEach(humanId->{
					RedisTools.updateRankWithTimestamp(EntityManager.redisClient, redisKey, humanId, Utils.randomBetween(1,10000));
				});
				RedisTools.expire(EntityManager.redisClient, redisKey, CrossWarConst.crossWarRank_expireTime);
			});
        }
		else if (type.equals("settleReward")) {
			// 跨服战结算
			m_sendRewardNum = new AtomicInteger(0);
			settleReward();
		}

	}

	@DistrMethod
	public void update(String jo) {

	}

	@DistrMethod
	public void update1(Object... objs) {

	}

	@DistrMethod
	public void update2(String str) {

	}
}