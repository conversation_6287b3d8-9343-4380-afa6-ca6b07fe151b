package org.gof.demo.worldsrv.rank;

import com.alibaba.fastjson.JSONObject;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.redis.client.RedisAPI;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.*;
import org.gof.core.support.*;
import org.gof.core.support.function.GofFunction0;
import org.gof.core.utils.StrUtil;
import org.gof.demo.distr.admin.AdminCenterManager;
import org.gof.demo.distr.admin.AdminCenterServiceProxy;
import org.gof.demo.distr.cross.CrossHumanLoader;
import org.gof.demo.distr.cross.CrossManager;
import org.gof.demo.distr.cross.domain.CrossPoint;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.worldsrv.arena.ArenaManager;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueRecord;
import org.gof.demo.worldsrv.character.HumanDailyResetInfo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityRankReward;
import org.gof.demo.worldsrv.config.ConfRanktype;
import org.gof.demo.worldsrv.config.ConfServerGroup;
import org.gof.demo.worldsrv.crossWar.CrossWarConst;
import org.gof.demo.worldsrv.crossWar.CrossWarManager;
import org.gof.demo.worldsrv.entity.Guild;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.GuildManager;
import org.gof.demo.worldsrv.guild.GuildServiceProxy;
import org.gof.demo.worldsrv.guild.league.GuildLeagueServiceProxy;
import org.gof.demo.worldsrv.guild.league.GuildLeagueUtils;
import org.gof.demo.worldsrv.human.HumanBriefLoadType;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgGvg;
import org.gof.demo.worldsrv.msg.MsgIds;
import org.gof.demo.worldsrv.msg.MsgRank;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.C;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.DailyResetTypeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.worldBoss.WorldBossServiceProxy;

import java.util.*;


public class RankManager extends ManagerBase {

	private boolean ret2;

	/**
	 * 获取实例
	 *
	 * @return
	 */
	public static RankManager inst() {
		return inst(RankManager.class);
	}

	public void handleRankDataListC2S(HumanObject humanObj, List<Define.p_rank_request> requestList) {
		for (Define.p_rank_request pRankRequest : requestList) {
			if (pRankRequest.getType() == RankParamKey.rankTypeWorldBoss) {
				// 世界boss
				WorldBossServiceProxy proxy = WorldBossServiceProxy.newInstance();
				proxy.getWorldRank(humanObj.id, pRankRequest.getServer(), humanObj.getHuman().getServerId(), pRankRequest.getPage());
				proxy.listenResult(this::_result_getWorldRank, "humanObj", humanObj, "page", pRankRequest.getPage());
			} else if (pRankRequest.getType() == RankParamKey.rankTypeCrossPvpTop1) {
				ArenaManager.inst().getArenaPvpTopGradeRankList(humanObj, pRankRequest.getPage());// 新需求，巅峰榜
			} else if (pRankRequest.getType() == RankParamKey.rankTypeBridgeArena_1037) {
				sendBridgeArenaRank(humanObj, pRankRequest.getType(), pRankRequest.getPage(), 0);
			} else if (pRankRequest.getType() == CrossWarConst.rank_1028 || pRankRequest.getType() == CrossWarConst.rank_1042
					|| pRankRequest.getType() == CrossWarConst.rank_1031 || pRankRequest.getType() == CrossWarConst.rank_1032) {
				CrossWarManager.inst().getCrossWarRank(humanObj, pRankRequest.getType(), pRankRequest.getPage());
			} else {
				int rankSn = pRankRequest.getType();
				ConfRanktype confRank = ConfRanktype.get(rankSn);
				if (confRank != null && confRank.crossType == CrossType.cross_activity.getType()) {
					sendCrossActivityRank(humanObj, confRank, pRankRequest.getPage());
				} else {
					sendRankListAll(humanObj, pRankRequest.getType(), pRankRequest.getPage(), 0);
				}
			}
		}
	}

	private void sendCrossActivityRank(HumanObject humanObj, ConfRanktype conf, int page) {
		int serverId = humanObj.getHuman().getServerId();
		int groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_activity.getType(), serverId);
		if (groupIndex == 0) {
			Log.rank.error("取不到跨服分组, type={}, serverId={}", CrossType.cross_activity.getType(), serverId);
			return;
		}
		CrossManager.getInstance().callCrossFunc(CrossType.cross_activity, serverId, handler ->  {
			if (handler.failed()) {
				Log.rank.error("查询跨服节点报错, serverId={}", serverId, handler.cause());
				return;
			}
			CrossPoint point = handler.result();
			RankCrossServiceProxy prx = RankCrossServiceProxy.newInstance(point.getNodeId());
			prx.getRankList(CrossType.cross_activity, humanObj.getHumanId(), serverId, conf.sn, page);
			prx.listenResult((result, context) -> {
				ReasonResult rr = result.get("result");
				if (!rr.success) {
					return;
				}
				long rank = result.get("rank");
				long score = result.get("score");
				MsgRank.rank_data_list_s2c msg = result.get("msg");
				MsgRank.rank_data_list_s2c.Builder msgBuilder = msg.toBuilder();
				Define.p_rank_data.Builder rankDataBuilder = msgBuilder.getRankDataList(0).toBuilder();
				Define.p_rank_info.Builder myInfo = new RankInfo().toRankInfo(humanObj.operation.getBrief(), (int) rank, (int) score).toBuilder();
				myInfo.setScore(score);
				rankDataBuilder.setMyRankInfo(myInfo);
				msgBuilder.setRankDataList(0, rankDataBuilder);
				humanObj.sendMsg(msgBuilder.build());
			});
		});
	}

	private void sendBridgeArenaRank(HumanObject humanObj, int rankSn, int page,  int refreshTime){
		if(page <= 0 || page > 100000){
			// 数据异常，不处理
			Log.temp.info("===请求的页码异常，page={}, humanId={}, rankSn={}", page, humanObj.id, rankSn);
			return;
		}
		int serverId= humanObj.getHuman().getServerId();
		long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
		String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
		int zone = GlobalConfVal.getZone(serverId);
		int groupIndex = Utils.intValue(Utils.getRedisScore(RedisKeys.admin_pvp_group, String.valueOf(serverId)));
		String redisKey = RedisKeys.arenaBridgeList + Utils.getRedisStrValue(RedisKeys.admin_server_date_zone_group + dateStr + zone + groupIndex);
		ConfRanktype confRanktype = ConfRanktype.get(rankSn);
		if(confRanktype == null){
			Log.temp.error("===没有找到排行榜类型，rankSn={}", rankSn);
			return;
		}

		int startIndex = (page - 1) * RankParamKey.pageNum;
		int endIndex = startIndex + RankParamKey.pageNum;
		List<String> strList = Utils.getRedisZaddStrValueList(redisKey, startIndex, endIndex, true);

		Define.p_rank_data.Builder rankData = Define.p_rank_data.newBuilder();
		int rank = startIndex;
		int myRrank = 0;
		int size = strList.size();
		int sumNum = Utils.getRankSumNum(redisKey);
		int maxPage = sumNum / RankParamKey.pageNum;
		Define.p_rank_info myRankInfo = null;
		for(int i = 0; i < size; i+=2){
			++rank;
			long humanId = Utils.longValue(strList.get(i));
			int score = Utils.intValue(strList.get(i+1));
			if(ArenaManager.inst().isRobot(humanId)){
				String keyRobot = ArenaManager.inst().getRobotKey(humanObj, humanId);
				String json = Utils.getRedisStrValue(keyRobot);
				if(json != null && !json.isEmpty()){
					JSONObject jo = Utils.toJSONObject(json);
					rankData.addRankInfo(ArenaManager.inst().robot_p_rank_info(jo, rank, score));
				}
				continue;
			}

			RankInfo rankInfo = new RankInfo(humanId, score);
			rankInfo.rank = rank;
			Define.p_rank_info rank_info = rankInfo.rank_info.toBuilder().setRank(rank).build();
			rankData.addRankInfo(rank_info);
			if(humanId == humanObj.id){
				myRrank = rank;
				myRankInfo = rank_info;
			}
		}

		rankData.setType(rankSn);
		rankData.setServer(humanObj.getHuman().getServerId());
		if(myRankInfo != null){
			rankData.setMyRankInfo(myRankInfo);
		} else {
			long score = Utils.longValue(Utils.getRedisScore(redisKey, String.valueOf(humanObj.id)));
			myRankInfo = new RankInfo(humanObj, score).rank_info.toBuilder().setRank(myRrank).build();
			rankData.setMyRankInfo(myRankInfo);
		}

		rankData.setPage(page);
		if(maxPage <= 0){
			maxPage = 1;
		}
		rankData.setMaxPage(maxPage);
		rankData.setTotalNum(sumNum);
		rankData.setNextFreshTime(refreshTime);

		MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();
		msg.addRankDataList(rankData);
		humanObj.sendMsg(msg);
	}

	private void sendRankListAll(HumanObject humanObj, int type, int page, int refreshTime) {
		if(page <= 0 || page > 100000){
			// 数据异常，不处理
			Log.temp.info("===请求的页码异常，page={}, humanId={}, type={}", page, humanObj.id, type);
			return;
		}

		ConfRanktype confRanktype = ConfRanktype.get(type);
		if (confRanktype == null) {
			return;
		}
		if(confRanktype.rank_type == RankParamKey.rankTypeHuman){
			Log.game.info("批量查询排行榜sendHuamRankHumanId={}",humanObj.id);
			if(type == RankParamKey.rankTypeArena_1004){// 竞技场特殊处理，本服要直接
				sendArenaRank(humanObj, type, page);
				return;
			}
			if(type == RankParamKey.rankTypeGuildBattleHuman_1033 || type == RankParamKey.rankTypeLeague_1016 ){// 跨服特殊处理
				if(GuildLeagueUtils.isBridge(Config.SERVER_ID)) {
					sendBridgeHumanRank(humanObj, type, page);
					return;
				}
			}
			sendHuamRank(humanObj, type, page);
		}else if(confRanktype.rank_type == RankParamKey.rankTypeGuild) {
			Log.game.info("批量查询排行榜sendGuildRankHumanId={}",humanObj.id);
			if(type == RankParamKey.rankTypeGuildBattleGuild_1034 || type == RankParamKey.rankTypeLeague_1017){
				if(GuildLeagueUtils.isBridge(Config.SERVER_ID)){
					sendBridgeGuildRank(humanObj, type, page);
					return;
				}
			}
			sendGuildRank(humanObj, type, page);
		}else if(confRanktype.rank_type == RankParamKey.rankTypeServer){
			Log.game.info("批量查询排行榜sendServerRankHumanId={}",humanObj.id);
			sendServerRank(humanObj, type, page, refreshTime);
		}
	}

	public void sendBridgeHumanRank(HumanObject humanObj, int type, int page){
		int groupIndex = humanObj.getHuman().getServerId();
		if(isRankBridge(type)){
			groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_chaos_battle.getType(), Config.SERVER_ID);
		}

		String redisKey = RedisKeys.bridge_pvp_group_human_rank + groupIndex;
		CrossRedis.getRankLen(redisKey, rest -> {
			if (rest.failed()) {
				Log.friend.error("获取失败，i={}", redisKey);
				return;
			}
			long total = rest.result();
			int maxPage = (int) Math.max(1, Math.ceil(total / RankParamKey.pageNum));

			int openIndex = (page - 1) * RankParamKey.pageNum;
			int closeIndex = page * RankParamKey.pageNum;
			openIndex = Math.max(openIndex, 0);
			closeIndex = Math.max(closeIndex, RankParamKey.pageNum);
			int open = openIndex;
			CrossRedis.getRankListByIndex(redisKey, openIndex, closeIndex - 1, true, ret -> {
				if (ret.failed()) {
					Log.friend.error("获取失败，i={}", redisKey);
					return;
				}
				JsonArray json = ret.result();
				if(json == null || json.isEmpty()){
					return;
				}
				int size = json.getList().size();
				List<Long> idList = new ArrayList<>();
				Map<Long, Integer> idScoreMap = new HashMap<>();
				for (int i = 0; i < size; i += 2) {
					long id = Utils.longValue(json.getList().get(i));
					int score = Utils.intValue(json.getList().get(i + 1));
					idScoreMap.put(id, score);
					idList.add(id);
				}
				CrossRedis.getList(idList, HumanBriefLoadType.SHOW_INFO, ret2 -> {
					if (ret2.failed()) {
						Log.temp.error("===ret2.cause={}", ret2.cause());
						return;
					}
					List<HumanBrief> humanBriefList = ret2.result();
					Map<Long, HumanBrief> humanBriefMap = new HashMap<>();
					for(HumanBrief brief : humanBriefList){
						if(brief == null){
							continue;
						}
						humanBriefMap.put(brief.getId(), brief);
					}
					int rank = open;
					List<Define.p_rank_info> rankList = new ArrayList<>();
					Define.p_rank_info myRankInfo = null;
					List<Long> loadIdList = new ArrayList<>();
					for (long id : idList) {
						rank++;
						HumanBrief brief = humanBriefMap.get(id);
						if(brief == null){
							loadIdList.add(id);
							continue;
						}
						RankInfo rankInfo = new RankInfo();
						Define.p_rank_info dInfo = rankInfo.toRankInfo(brief, rank, idScoreMap.get(id));
						rankList.add(dInfo);
						if (id == humanObj.id) {
							myRankInfo = dInfo;
						}
					}
					if(!loadIdList.isEmpty()){
						HumanManager.inst().loadHumanIdSyncCross(loadIdList);
					}

					MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();
					Define.p_rank_data.Builder rankData = Define.p_rank_data.newBuilder();
					rankData.setType(type);
					rankData.setPage(page);
					rankData.setMaxPage(maxPage);
					rankData.setTotalNum(Utils.intValue(total));
					rankData.addAllRankInfo(rankList);
					rankData.setMyRankInfo(Define.p_rank_info.newBuilder().build());
					if(myRankInfo != null){
						rankData.setMyRankInfo(myRankInfo);
						msg.addRankDataList(rankData);
						humanObj.sendMsg(msg);
						return;
					}
					getMyRankAndScore(CrossRedis.getClient(), redisKey, humanObj.id, res -> {
						if(res.failed()){
							msg.addRankDataList(rankData);
							humanObj.sendMsg(msg);
							return;
						}
						List<String> strList = res.result();
						if(strList == null || strList.size() != 2){
							msg.addRankDataList(rankData);
							humanObj.sendMsg(msg);
							return;
						}
						int rankMy = Utils.intValue(strList.get(0));
						long scoreMy = (long) Utils.doubleValue(strList.get(1));
						Define.p_rank_info myInfo = new RankInfo(humanObj, scoreMy).rank_info.toBuilder().setRank(rankMy).build();
						rankData.setMyRankInfo(myInfo);
						msg.addRankDataList(rankData);
						humanObj.sendMsg(msg);
					});
				});
			});
		});
	}

	public void sendBridgeGuildRank(HumanObject humanObj, int type, int page){
		try {
			CrossManager.getInstance().callCrossFunc(CrossType.cross_chaos_battle, Config.SERVER_ID, res -> {
				try {
					if(res.failed()){
						Log.temp.error("==跨服获取数据出问题 {}", res.cause());
						return;
					}
					CrossPoint result = res.result();
					GuildLeagueServiceProxy proxy = GuildLeagueServiceProxy.newInstance(result.getNodeId(), D.SERV_GUILD_LEAGUE);
					proxy.sendBridgeGuildRank(humanObj.getHuman().getServerId(), type, page, humanObj.getHuman2().getGuildId());
					proxy.listenResult(this::_result_getBridgeGuildRank_cross, "humanObj", humanObj, "page", page);
				} catch (Exception e){
					Log.temp.error("==跨服链接出问题 ", e);
				}
			});
		} catch (Exception e){
			Log.temp.error("====跨服获取数据出问题", e);
		}
	}

	public void _result_getBridgeGuildRank_cross(Param results, Param context){
		HumanObject humanObj = context.get("humanObj");
		MsgRank.rank_data_list_s2c msg = results.get("msg");
		if(humanObj != null && msg != null){
			humanObj.sendMsg(msg);
		}
	}

	public String getRedisRankTypeKey(int serverId, ConfRanktype conf) {
		if (conf.sn == RankParamKey.rankTypeGuildBattleHuman_1033 || conf.sn == RankParamKey.rankTypeLeague_1016) {
			// 乱斗的个人排行榜
			int groupIndex = serverId;
			if (isRankBridge(conf.sn)) {
				groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_chaos_battle.getType(), serverId);
			}
			return RedisKeys.bridge_pvp_group_human_rank + groupIndex;
		} else if (conf.sn == RankParamKey.rankTypeGuildBattleGuild_1034 || conf.sn == RankParamKey.rankTypeLeague_1017) {
			// 乱斗的家族排行榜
			int groupIndex = serverId;
			if (isRankBridge(conf.sn)) {
				groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_chaos_battle.getType(), serverId);
			}
			return RedisKeys.bridge_pvp_group_guild_rank + groupIndex;
		} else if (conf.crossType > 0) {
			if (conf.crossType == CrossType.cross_activity.getType()) {
				int groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_activity.getType(), serverId);
				String prefix = conf.rank_type == RankParamKey.rankTypeHuman ?  RedisKeys.bridge_activity_human_rank : RedisKeys.bridge_activity_guild_rank;
				return Utils.createStr("{}_{}_{}", prefix, conf.sn, groupIndex);
			}
		}

		if (isRankBridge(conf.sn)) {
			int groupIndex = Utils.intValue(Utils.getRedisScore(RedisKeys.admin_pvp_group, String.valueOf(serverId)));
			return Utils.createStr("{}_{}_{}", RedisKeys.rankSn_list, conf.sn, groupIndex);
		}
		return Utils.createStr("{}_{}_{}", RedisKeys.rankSn_list, conf.sn, serverId);
	}

	private boolean isRankBridge(int rankSn){
		if(GuildLeagueUtils.isBridge(Config.SERVER_ID)){
			if(rankSn == RankParamKey.rankTypeGuildBattleHuman_1033 || rankSn == RankParamKey.rankTypeGuildBattleGuild_1034
					|| rankSn == RankParamKey.rankTypeBridgeRep_1036 || rankSn == RankParamKey.rankTypeBridgeArena_1037
					|| rankSn == RankParamKey.rankTypeBridgeGuildCombat_1038){
				return true;
			}
		}
		return false;
	}
	public String getRedisRankTypeLockKey(int serverId, ConfRanktype conf) {
		return Utils.createStr("{}_{}_{}", RedisKeys.rankSn_list_lock, conf.sn, serverId);
	}

	public String getRedisRankTypeKey(int rankType, int serverId) {
		if(rankType == RankParamKey.rankTypeGuildBattleHuman_1033 || rankType == RankParamKey.rankTypeLeague_1016){
			int groupIndex = serverId;
			if(isRankBridge(rankType)){
				groupIndex = Utils.intValue(Utils.getRedisScore(RedisKeys.admin_pvp_group, String.valueOf(serverId)));
			}
			return RedisKeys.bridge_pvp_group_human_rank + groupIndex;
		} else if(rankType == RankParamKey.rankTypeGuildBattleGuild_1034 || rankType == RankParamKey.rankTypeLeague_1017){
			int groupIndex = serverId;
			if(isRankBridge(rankType)){
				groupIndex = Utils.intValue(Utils.getRedisScore(RedisKeys.admin_pvp_group, String.valueOf(serverId)));
			}
			return RedisKeys.bridge_pvp_group_guild_rank + groupIndex;
		}
		if(isRankBridge(rankType)){
			int groupIndex = Utils.intValue(Utils.getRedisScore(RedisKeys.admin_pvp_group, String.valueOf(serverId)));
			return Utils.createStr("{}_{}_{}", RedisKeys.rankSn_list, rankType, groupIndex);
		}
		return Utils.createStr("{}_{}_{}", RedisKeys.rankSn_list, rankType, serverId);
//		return getRedisRankTypeKey(serverId, ConfRanktype.get(rankType));
	}

	public String getBridgeRedisRankTypeKey(int rankType, int groupIndex) {
		return Utils.createStr("{}_{}_{}", RedisKeys.rankSn_list, rankType, groupIndex);
	}

	private void _result_getWorldRank(Param results, Param context){
		MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();
		HumanObject humanObj =  Utils.getParamValue(context, "humanObj", null);
		int page = Utils.getParamValue(context, "page", 1);
		int pageMax = Utils.getParamValue(results, "pageMax", 1);
		int totalHurt = Utils.getParamValue(results, "totalHurt", 1);
		Define.p_rank_info myInfo = Utils.getParamValue(results, "myInfo", null);
		List<Define.p_rank_info> list = Utils.getParamValue(results, "list", new ArrayList<>());

		Human human = humanObj.getHuman();
		Define.p_rank_data.Builder dInfo = Define.p_rank_data.newBuilder();
		dInfo.setType(RankParamKey.rankTypeWorldBoss);
		dInfo.setServer(human.getServerId());
		dInfo.setPage(page);
		dInfo.setMaxPage(pageMax);
		dInfo.setTotalNum(totalHurt);

		long timeNow = Port.getTime();
		int hour = Utils.getHourOfTime(timeNow) + 1;
		int freshTime = (int)(Utils.getDayTime(timeNow, hour, 0, 0) / Time.SEC);
		dInfo.setNextFreshTime(freshTime);
		if(myInfo == null){
			RankInfo rankInfo = new RankInfo(humanObj, 0);
			dInfo.setMyRankInfo(rankInfo.rank_info);
		} else {
			dInfo.setMyRankInfo(myInfo);
		}

		if(list != null && !list.isEmpty()){
			dInfo.addAllRankInfo(list);
		}
		msg.addRankDataList(dInfo);
		humanObj.sendMsg(msg);
	}

	/**
	 * 添加玩家排行榜数据
	 * @param humanObj
	 * @return
	 */
	public void addRankScore(HumanObject humanObj, String rankKey,ConfRanktype conf, long addScore) {
		long id = humanObj.id;
		if (conf.rank_type == RankParamKey.rankTypeGuild) {
			id = humanObj.getHuman2().getGuildId();
		} else if (conf.rank_type == RankParamKey.rankTypeServer) {
			id = humanObj.getHuman().getServerId();
		}
		if (id == 0 || addScore ==0) {
			return;
		}
		RedisAPI client = conf.crossType > 0 ? CrossRedis.getClient() : EntityManager.getRedisClient();
		RedisTools.incrRankWithTime(client, rankKey, id, addScore);
	}

	public void updateRankWithTime(HumanObject humanObj, String rankKey,ConfRanktype conf, long addScore) {
		long id = humanObj.id;
		if (conf.rank_type == RankParamKey.rankTypeGuild) {
			id = humanObj.getHuman2().getGuildId();
		} else if (conf.rank_type == RankParamKey.rankTypeServer) {
			id = humanObj.getHuman().getServerId();
		}
		if (id == 0 || addScore ==0) {
			return;
		}
		RedisAPI client = conf.crossType > 0 ? CrossRedis.getClient() : EntityManager.getRedisClient();
		RedisTools.addRankWithTime(client, rankKey, id, addScore);
	}

	/**
	 * 更新玩家排行榜数据
	 * @param humanObj
	 * @return
	 */
	public void updateRankScore(HumanObject humanObj, String rankKey, ConfRanktype conf, long score, GofFunction0 successCallBack) {
		long id;
		if (conf.rank_type == RankParamKey.rankTypeGuild) {
			id = humanObj.getHuman2().getGuildId();
		} else if(conf.rank_type == RankParamKey.rankTypeServer) {
			id = humanObj.getHuman().getServerId();
		} else {
			id = humanObj.id;
		}
		RedisTools.addRank(EntityManager.getRedisClient(), rankKey, id, score, handler -> {
			if (handler.failed()) {
				Log.temp.error("===排行榜新增保存失败，rankKey={}，id={}，score={}", rankKey, id, score);
			}
			if (successCallBack != null) {
				successCallBack.apply();
			}
		});
	}

	public void updateRankScore(HumanObject humanObj, String rankKey, ConfRanktype conf, long score) {
		updateRankScore(humanObj, rankKey, conf, score, null);
	}

	public void updateRankScore(HumanObject humanObj, ConfRanktype conf, long score) {
		String rankKey = RankManager.inst().getRedisRankTypeKey(humanObj.getHuman().getServerId(), conf);
		updateRankScore(humanObj, rankKey, conf, score, null);
	}


	/**
	 * 发送排行榜所有数据
	 * <AUTHOR>
	 * @Date 2024/3/11
	 */
	public void sendHuamRank(HumanObject humanObj, int rankSn, int page){
		ConfRanktype confRanktype = ConfRanktype.get(rankSn);
		if(confRanktype == null){
			Log.temp.error("===没有找到排行榜类型，rankSn={}", rankSn);
			return;
		}
		String redisKey = RankManager.inst().getRedisRankTypeKey(humanObj.getHuman().getServerId(), confRanktype);
		RankServiceProxy rankServiceProxy = RankServiceProxy.newInstance();
		rankServiceProxy.getHumanRank(rankSn,humanObj.getHuman().getServerId(),page);
		rankServiceProxy.listenResult(this::_result_getHumanRank, "humanObj", humanObj,"redisKey",redisKey);
	}

	private void _result_getHumanRank(Param results, Param context) {
		HumanObject humanObj =  Utils.getParamValue(context, "humanObj", null);
		String redisKey = Utils.getParamValue(context, "redisKey", "");

		Define.p_rank_data.Builder rankData = results.get();
		getMyRankAndScore(EntityManager.getRedisClient(), redisKey, humanObj.id, res -> {
			if(res.succeeded()){
                List<String> strList = res.result();
                if(strList == null || strList.size() != 2){
					return;
                }
				int rank = Utils.intValue(strList.get(0));
				long score = (long) Utils.doubleValue(strList.get(1));
				Define.p_rank_info myRankInfo = new RankInfo(humanObj, score).rank_info.toBuilder().setRank(rank).build();
				rankData.setMyRankInfo(myRankInfo);
				MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();
				msg.addRankDataList(rankData);
				humanObj.sendMsg(msg);
            }

		});
	}

	public void getMyRankAndScore(int rankSn, int serverId, long id, Handler<AsyncResult<List<String>>> onComplete) {
		ConfRanktype confRank = ConfRanktype.get(rankSn);
		if (confRank == null) {
			Log.rank.error("===没有找到排行榜类型，rankSn={}", rankSn);
			return;
		}
		Port port = Port.getCurrent();
		RedisAPI client = confRank.crossType > 0 ? CrossRedis.getClient() : EntityManager.getRedisClient();
		String rankKey = RankManager.inst().getRedisRankTypeKey(serverId, confRank);
		getMyRankAndScore(client, rankKey, id, ret -> {
			if (ret.failed()) {
				Log.rank.error("===获取排行榜数据失败，rankKey={}", rankKey);
				return;
			}
			List<String> result = ret.result();
			if (result == null || result.size() < 2) {
				result = new ArrayList<>(2);
				result.add("0");
				result.add("0");
			}
			AsyncActionResult.success(port, onComplete, result);
		});
	}

	public void getMyRank(int rankSn, int serverId, long id, Handler<AsyncResult<Long>> onComplete) {
		ConfRanktype confRank = ConfRanktype.get(rankSn);
		if (confRank == null) {
			Log.rank.error("===没有找到排行榜类型，rankSn={}", rankSn);
			return;
		}
		Port port = Port.getCurrent();
		RedisAPI client = confRank.crossType > 0 ? CrossRedis.getClient() : EntityManager.getRedisClient();
		String rankKey = RankManager.inst().getRedisRankTypeKey(serverId, confRank);
		RedisTools.getMyRank(client, rankKey, String.valueOf(id), ret -> {
			if (ret.failed()) {
				AsyncActionResult.fail(port, onComplete, ret.cause());
				Log.rank.error("===获取排行榜数据失败，rankKey={}", rankKey);
				return;
			}
			AsyncActionResult.success(port, onComplete, ret.result());
		}
		);
	}

	/**
	 * 同时获取我的排名和分数
	 * @param client Redis客户端
	 * @param key Redis键
	 * @param id 玩家ID
	 * @param onComplete 回调函数，返回List<String> [排名, 分数]
	 */
	public void getMyRankAndScore(RedisAPI client, String key, long id, Handler<AsyncResult<List<String>>> onComplete) {
		// 先获取排名
		Port port = Port.getCurrent();
		RedisTools.getMyRank(client, key, String.valueOf(id), rankRes -> {
			if (rankRes.succeeded()) {
				long rank = rankRes.result();
				// 获取到排名后，继续获取分数
				RedisTools.getMyScore(client, key, id, scoreRes -> {
					if (scoreRes.succeeded()) {
						double score = scoreRes.result();

						// 同时获取到排名和分数，返回结果
						List<String> result = new ArrayList<>();
						result.add(String.valueOf(rank));
						result.add(String.valueOf(score));
						AsyncActionResult.success(port, onComplete,result);
					} else {
						Log.game.error("redis error getMyScore key:{} id:{} exception:{}",
								key, id, Tool.getException(scoreRes.cause()));
						AsyncActionResult.fail(port,onComplete,scoreRes.cause());
					}
				});
			} else {
				Log.game.error("redis error getMyRank key:{} id:{} exception:{}",
						key, id, Tool.getException(rankRes.cause()));
				AsyncActionResult.fail(port,onComplete,rankRes.cause());
			}
		});
	}

	public void sendGuildRank(HumanObject humanObj, int rankSn, int page){
		ConfRanktype confRanktype = ConfRanktype.get(rankSn);
		if(confRanktype == null){
			Log.temp.error("===没有找到排行榜类型，rankSn={}", rankSn);
			return;
		}
		if(page > confRanktype.show_num/RankParamKey.pageNum){
			return;
		}
		String redisKey = RankManager.inst().getRedisRankTypeKey(humanObj.getHuman().getServerId(), confRanktype);

		int serverId = humanObj.getHuman().getServerId();
		long guildId = humanObj.getHuman2().getGuildId();


		GuildServiceProxy proxy = GuildServiceProxy.newInstance();
		proxy.getRankList(serverId, page, guildId, rankSn);
		proxy.listenResult(this::_result_getRankList,"humanObj", humanObj, "redisKey", redisKey, "rankSn",rankSn, "page",page);

		// 使用RankService的缓存机制
//		RankServiceProxy rankServiceProxy = RankServiceProxy.newInstance();
//		rankServiceProxy.getGuildRank(rankSn, serverId, page);
//		rankServiceProxy.listenResult(this::_result_getGuildRank, "humanObj", humanObj, "redisKey", redisKey);
	}
	private void _result_getRankList(Param results, Param context) {
		HumanObject humanObj =  Utils.getParamValue(context, "humanObj", null);
		MsgRank.rank_data_list_s2c msg = Utils.getParamValue(results, "rankList", null);
		if(msg != null){
			humanObj.sendMsg(msg);
		}
	}

	private void _result_getGuildRank(Param results, Param context) {
		HumanObject humanObj =  Utils.getParamValue(context, "humanObj", null);
		String redisKey = Utils.getParamValue(context, "redisKey", "");
		Define.p_rank_data.Builder rankData = results.get();
		long guildId = humanObj.getHuman2().getGuildId();
		if(guildId == 0){
			rankData.setMyRankInfo(to_p_rank_info(null,0,0,humanObj.getHuman().getServerId(),HumanData.getHumanData(humanObj)));
			MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();
			msg.addRankDataList(rankData);
			humanObj.sendMsg(msg);
			return;
		}
		GuildServiceProxy proxy = GuildServiceProxy.newInstance();
		proxy.getGuild(guildId);
		proxy.listenResult(this::_result_getGuild,"humanObj", humanObj, "redisKey", redisKey, "rankData",rankData);
	}

	private void _result_getGuild(Param results, Param context){
		HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
		String redisKey = Utils.getParamValue(context, "redisKey", "");
		Define.p_rank_data.Builder rankData = Utils.getParamValue(context, "rankData", null);
		Guild guild = results.get("guild");
		if(guild == null){
			rankData.setMyRankInfo(to_p_rank_info(null,0,0,humanObj.getHuman().getServerId(),HumanData.getHumanData(humanObj)));
			MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();
			msg.addRankDataList(rankData);
			humanObj.sendMsg(msg);
			return;
		}
		getMyRankAndScore(EntityManager.getRedisClient(), redisKey, humanObj.getHuman2().getGuildId(), res -> {
			if(res.succeeded()){
				int rank = 0;
				long score = 0;
				List<String> strList = res.result();
				if(strList != null && strList.size() == 2){
					rank = Utils.intValue(strList.get(0));
					score = (long) Utils.doubleValue(strList.get(1));
				}
				rankData.setMyRankInfo(to_p_rank_info(guild,score,rank,humanObj.getHuman().getServerId(),HumanData.getHumanData(humanObj)));
				MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();
				msg.addRankDataList(rankData);
				humanObj.sendMsg(msg);
			}
		});

	}



	public Define.p_rank_info to_p_rank_info(Guild guild, long score, int rank, int serverId, HumanData humanDefault){
		Define.p_rank_info.Builder dInfo = Define.p_rank_info.newBuilder();
		dInfo.setRank(rank);
		if(guild == null){
			dInfo.setName("");
			dInfo.setGuildName("");
			dInfo.setBelongId(0);
			Define.p_key_value_string.Builder kvBuilder = Define.p_key_value_string.newBuilder();
			kvBuilder.setK(0);
			kvBuilder.setV(0);
			kvBuilder.setS("0");
			dInfo.addGuildFlag(kvBuilder.build());
		} else {
			dInfo.setName(guild.getName());
			dInfo.setGuildName(guild.getName());
			dInfo.setBelongId(guild.getLeaderId());
			String flagJSON = guild.getFlagJSON();
			if (!Utils.isEmptyJSONString(flagJSON)) {
				List<Define.p_key_value_string> pkvsList = GuildManager.inst().to_p_guild_flag(flagJSON);
				dInfo.addAllGuildFlag(pkvsList);
			}

//			HumanData humanData = humanDefault == null ? HumanData.getHumanData(guild.getLeaderId()) : humanDefault;
			if(humanDefault != null){
				dInfo.setHead(HumanManager.inst().to_p_head(humanDefault.human));
				dInfo.setFigure(HumanManager.inst().to_p_role_figure(humanDefault.human,humanDefault.human2));
			}
		}

		dInfo.setScore(score);
		// 组装 p_key_value
//		if (extraMap.size() != 0) {
//			msg.addAllExtra(MapUtil.toKeyValueMsg(extraMap));
//		}
//		// 组装 p_key_string
//		if (extraStrMap.size() != 0) {
//			msg.addAllExtraStr(MapUtil.toKeyStringMsg(extraStrMap));
//		}

		dInfo.setServId(Utils.getServerIdTo(serverId));
		return dInfo.build();
	}

	public Define.p_rank_info to_p_rank_info(Guild guild, long score, int rank, int serverId, HumanBrief humanBrief){
		Define.p_rank_info.Builder dInfo = Define.p_rank_info.newBuilder();
		try{
			dInfo.setRank(rank);
			if(guild == null){
				dInfo.setName("");
				dInfo.setGuildName("");
				dInfo.setBelongId(0);
				Define.p_key_value_string.Builder kvBuilder = Define.p_key_value_string.newBuilder();
				kvBuilder.setK(0);
				kvBuilder.setV(0);
				kvBuilder.setS("0");
				dInfo.addGuildFlag(kvBuilder.build());
			} else {
				dInfo.setName(guild.getName());
				dInfo.setGuildName(guild.getName());
				dInfo.setBelongId(guild.getLeaderId());
				String flagJSON = guild.getFlagJSON();
				if (!Utils.isEmptyJSONString(flagJSON)) {
					List<Define.p_key_value_string> pkvsList = GuildManager.inst().to_p_guild_flag(flagJSON);
					dInfo.addAllGuildFlag(pkvsList);
				}
				if(humanBrief != null){
					Define.p_head.Builder head = Define.p_head.newBuilder();
					head.setId(humanBrief.getHeadSn());
					head.setFrameId(humanBrief.getCurrentHeadFrameSn());
					head.setUrl("");
					dInfo.setHead(head);
					try{
						dInfo.setFigure(Define.p_role_figure.parseFrom(humanBrief.getRoleFigure()));
					} catch (Exception e) {
						Log.temp.error("to_p_rank_info error", e);
					}
				}
			}
			dInfo.setScore(score);
			// 组装 p_key_value
//		if (extraMap.size() != 0) {
//			msg.addAllExtra(MapUtil.toKeyValueMsg(extraMap));
//		}
//		// 组装 p_key_string
//		if (extraStrMap.size() != 0) {
//			msg.addAllExtraStr(MapUtil.toKeyStringMsg(extraStrMap));
//		}

			dInfo.setServId(Utils.getServerIdTo(serverId));
		} catch (Exception e) {
			Log.temp.error("to_p_rank_info error", e);
		}
		return dInfo.build();
	}

	public Define.p_rank_info to_p_rank_info(GuildLeagueRecord guildLeague, long score, int rank, int serverId, HumanBrief humanBrief){
		Define.p_rank_info.Builder dInfo = Define.p_rank_info.newBuilder();
		try{
			dInfo.setRank(rank);
			if(guildLeague == null){
				dInfo.setName("");
				dInfo.setGuildName("");
				dInfo.setBelongId(0);
				Define.p_key_value_string.Builder kvBuilder = Define.p_key_value_string.newBuilder();
				kvBuilder.setK(0);
				kvBuilder.setV(0);
				kvBuilder.setS("0");
				dInfo.addGuildFlag(kvBuilder.build());
			} else {
				dInfo.setName(guildLeague.getGuildName());
				dInfo.setGuildName(guildLeague.getGuildName());
				dInfo.setBelongId(guildLeague.getLeaderId());
				String flagJSON = guildLeague.getFlagJSON();
				if (!Utils.isEmptyJSONString(flagJSON)) {
					List<Define.p_key_value_string> pkvsList = GuildManager.inst().to_p_guild_flag(flagJSON);
					dInfo.addAllGuildFlag(pkvsList);
				}
				if(humanBrief != null){
					Define.p_head.Builder head = Define.p_head.newBuilder();
					head.setId(humanBrief.getHeadSn());
					head.setFrameId(humanBrief.getCurrentHeadFrameSn());
					head.setUrl("");
					dInfo.setHead(head);
					try{
						dInfo.setFigure(Define.p_role_figure.parseFrom(humanBrief.getRoleFigure()));
					} catch (Exception e) {
						Log.temp.error("to_p_rank_info error", e);
					}
				}
			}
			dInfo.setScore(score);
			// 组装 p_key_value
//		if (extraMap.size() != 0) {
//			msg.addAllExtra(MapUtil.toKeyValueMsg(extraMap));
//		}
//		// 组装 p_key_string
//		if (extraStrMap.size() != 0) {
//			msg.addAllExtraStr(MapUtil.toKeyStringMsg(extraStrMap));
//		}

			if(guildLeague.getServerId() != 0){
				dInfo.setServId(Utils.getServerIdTo(guildLeague.getServerId()));
			} else {
				dInfo.setServId(Utils.getServerIdTo(serverId));
			}

		} catch (Exception e) {
			Log.temp.error("to_p_rank_info error", e);
		}
		return dInfo.build();
	}

	public void sendServerRank(HumanObject humanObj, int rankSn, int page, int freshTime){

	}

	public void distributeReward(String s, int currentRank, int activitySn, ConfRanktype confRanktype, ConfActivityRankReward confReward) {
		long id = Utils.longValue(s);
		JSONObject jo = new JSONObject();

		JSONObject jo1 = new JSONObject();
		jo1.put(MailManager.MAIL_K_8, activitySn);
		JSONObject jo2 = new JSONObject();
		jo2.put(MailManager.MAIL_K_4, currentRank);

		jo.put(MailManager.MAIL_PARAM_1,jo1);
		jo.put(MailManager.MAIL_PARAM_2,jo2);

		if (confRanktype.rank_type == RankParamKey.rankTypeHuman){
			String itemJSON = Utils.mapIntIntToJSON(Utils.intArrToIntMap(new HashMap<>(), confReward.rank_reward));
			MailManager.inst().sendMail(id, MailManager.SYS_SENDER, 10112, "", jo.toJSONString(), itemJSON, new Param());
		}else if (confRanktype.rank_type == RankParamKey.rankTypeGuild){
			String itemJSON = Utils.mapIntIntToJSON(Utils.intArrToIntMap(new HashMap<>(), confReward.rank_reward));
			MailManager.inst().sendMailToGuild(id, true, MailManager.SYS_SENDER, 10112, "", jo.toJSONString(), itemJSON, new Param());
			if(confReward.rank_reward_spec != null && confReward.rank_reward_spec.length > 0){
				itemJSON = Utils.mapIntIntToJSON(Utils.intArrToIntMap(new HashMap<>(), confReward.rank_reward_spec));
				MailManager.inst().sendMailToGuildLeader(id, MailManager.SYS_SENDER, 10112, "", jo.toJSONString(), itemJSON, new Param());
			}
		}
	}

	public void repRankUpdate(HumanObject humanObj){
		Human human = humanObj.getHuman();

		String scoreStr = String.valueOf(humanObj.getHuman2().getRepSn());
		String memberStr = String.valueOf(humanObj.id);
		// 本服排行榜 key=RedisKeys.rankSn_list + rankSn + serverId, score=关卡sn, value=玩家id
		rankUpdate(getRedisRankTypeKey(RankParamKey.rankTypeRep_1003, human.getServerId()), scoreStr, memberStr);

		// 跨服排行榜
//		int groupIndex = Utils.intValue(Utils.getRedisScore(RedisKeys.admin_pvp_group, String.valueOf(human.getServerId())));
//		if(groupIndex != 0){
//			// key=RedisKeys.rankSn_list + rankSn + group, score=关卡sn, value=玩家id
//			rankUpdate(getBridgeRedisRankTypeKey(RankParamKey.rankTypeBridgeRep_1036, groupIndex), scoreStr, memberStr);
//		}
	}

	public void guildRankUpdate(long guildId, long combat, int serverId){
		String combatStr = String.valueOf(combat);
		String guildIdStr = String.valueOf(guildId);
		// 本服排行榜
		rankUpdate(getRedisRankTypeKey(RankParamKey.rankTypeGuildCombat_1014, serverId), combatStr, guildIdStr);

		// 跨服排行榜
//		int groupIndex = Utils.intValue(Utils.getRedisScore(RedisKeys.admin_pvp_group, String.valueOf(serverId)));
//		if(groupIndex != 0){
//			rankUpdate(getBridgeRedisRankTypeKey(RankParamKey.rankTypeBridgeGuildCombat_1038, groupIndex), combatStr, guildIdStr);
//		}
	}

	public void rankUpdate(int rankType, int serverId, String score, String member){
		rankUpdate(getRedisRankTypeKey(rankType, serverId), score, member);
	}

	public void rankUpdateBridge(int rankType, int groupIndex, String score, String member){
		rankUpdate(getBridgeRedisRankTypeKey(rankType, groupIndex), score, member);
	}

	public void rankUpdate(String rankKey, String score, String member){
		// 本服排行榜
		List<String> keyList = new ArrayList<>();
		keyList.add(rankKey);
		keyList.add(score);
		keyList.add(member);
		EntityManager.redisClient.zadd(keyList, r->{
			if(!r.succeeded()){
				Log.temp.error("===保存数据失败keys={}", keyList);
			}
		});
	}

	public void _msg_rank_serv_list_c2s(HumanObject humanObj, int type) {
		ConfRanktype confRanktype = ConfRanktype.get(type);
		if (confRanktype == null) {
			return;
		}
		MsgRank.rank_serv_list_s2c.Builder msg = MsgRank.rank_serv_list_s2c.newBuilder();
		if (confRanktype.crossType == CrossType.cross_activity.getType()) {
			// 跨服活动
			CrossManager.getInstance().callCrossFunc(CrossType.cross_activity, Config.SERVER_ID, res -> {
				if (res.failed()) {
					Log.temp.error("==跨服获取数据出问题", res.cause());
					return;
				}
				CrossPoint result = res.result();
				int group = Utils.intValue(result.getGroupId());
				ConfServerGroup conf = ConfServerGroup.get(group);
				if (conf == null) {
					Log.temp.error("==ConfServerGroup找不到组配置 {}", group);
					return;
				}
				List<Integer> serverIds = StrUtil.parseRangeCfg(conf.sever_range);
				msg.addAllServList(serverIds);
				humanObj.sendMsg(msg);
			});
			return;
		} else {
			msg.addServList(humanObj.getHuman().getServerId());
		}
		humanObj.sendMsg(msg);
	}

	public void _msg_rank_cross_status_c2s(HumanObject humanObj) {
		int groupIndex = Utils.intValue(Utils.getRedisScore(RedisKeys.admin_pvp_group, String.valueOf(humanObj.getHuman().getServerId())));
		MsgRank.rank_cross_status_s2c.Builder msg = MsgRank.rank_cross_status_s2c.newBuilder();
		msg.setStatus(groupIndex == 0 ? 0 : 1);
		humanObj.sendMsg(msg);
	}

	public void removeRank(long humanId){
		for(ConfRanktype conf : ConfRanktype.findAll()){
			String rankKey = getRedisRankTypeKey(conf.sn, C.GAME_SERVER_ID);
			RedisTools.removeFromRank(EntityManager.redisClient, rankKey, humanId);
		}
	}

	private void sendArenaRank(HumanObject humanObj, int rankSn, int page){
		if(!humanObj.isMsgIdCD(MsgIds.rank_data_list_c2s,1)){
			return;
		}
		if(humanObj.isArenaBridge){
			// 跨服后，本服只管真人
			arenaRankBridge(humanObj, page);
//			ArenaManager.inst().sendArenaRank(humanObj, true, page);
			return;
		}
		ArenaManager.inst().sendArenaRank(humanObj, true, page);
//		ArenaServiceProxy proxy = ArenaServiceProxy.newInstance();
//		proxy.getAreanRobotMap(humanObj.getHuman().getServerId());
//		proxy.listenResult(this::_result_getArenaRobotMap, "humanObj", humanObj, "rankSn", rankSn, "page", page);
	}

	private void arenaRankBridge(HumanObject humanObj, int page){
		if(page <= 0){
			page = 1;
		}
		String redisKey = RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeArena_1004, humanObj.getHuman().getServerId());
		Port port = Port.getCurrent();
		int rankOpenIndex = (page - 1) * RankParamKey.pageNum;
		int finalPage = page;
		ConfRanktype confRanktype = ConfRanktype.get(RankParamKey.rankTypeArena_1004);
		int refreshTime = Utils.getTimeSec() + confRanktype.refresh_time;

		RedisTools.getRankLen(EntityManager.redisClient, redisKey, f-> {
			if (f.failed()) {
				return;
			}
			int rankLen = Utils.intValue(f.result());
			int maxPage = rankLen / RankParamKey.pageNum + (rankLen % RankParamKey.pageNum == 0? 0 : 1);
			int finalMaxPage = maxPage == 0 ? 1 : maxPage;
			RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, rankOpenIndex < 0 ? 0 : rankOpenIndex,
				finalPage * RankParamKey.pageNum - 1, true, ret -> {
				if(!ret.succeeded()) {
					Log.temp.error("获取排行榜失败, rankSn={}", RankParamKey.rankTypeArena_1004);
					return;
				}
				JsonArray json = ret.result();
				List<Long> humanIdList = new ArrayList<>();
				Map<Long, Integer> idScoreMap = new HashMap<>();
				for(int i = 0; i < json.getList().size(); i+=2) {
					long id = Utils.longValue(json.getList().get(i));
					int score = Utils.intValue(json.getList().get(i+1));
					humanIdList.add(id);
					idScoreMap.put(id, score);
				}

				Define.p_rank_data.Builder rankData = Define.p_rank_data.newBuilder();
				rankData.setType(RankParamKey.rankTypeArena_1004);
				rankData.setServer(Utils.getServerIdTo(humanObj.getHuman().getServerId()));
				rankData.setPage(finalPage);
				rankData.setMaxPage(finalMaxPage);
				rankData.setTotalNum(rankLen);
				rankData.setNextFreshTime(refreshTime);

				if(humanIdList.isEmpty()){
					RedisTools.getMyRank(EntityManager.redisClient, redisKey, String.valueOf(humanObj.id), ret2 -> {
						if(ret2.failed()){
							Log.temp.error("获取排行榜失败, rankSn={}, humanId={}", RankParamKey.rankTypeArena_1004, humanObj.id);
						}
						int rank = Utils.intValue(ret2.result()) <= 0 ? rankLen + 1 : Utils.intValue(ret2.result());
						RedisTools.getMyScore(EntityManager.getRedisClient(), redisKey, humanObj.id, ret3 -> {
							if(ret3.failed()){
								Log.temp.error("获取排行榜失败, rankSn={}, humanId={}", RankParamKey.rankTypeArena_1004, humanObj.id);
							}
							int score = Utils.intValue(ret3.result()) <= 0 ? ParamKey.initArenaScore : Utils.intValue(ret3.result());
							MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();

							Define.p_rank_info myRankInfo = new RankInfo(humanObj, score).rank_info.toBuilder().setRank(rank).build();
							rankData.setMyRankInfo(myRankInfo);
							msg.addRankDataList(rankData);
							port.doAction(() -> {
								humanObj.sendMsg(msg);
							});
						});
					});
					return;
				}

				CrossHumanLoader.getList(humanIdList, HumanBriefLoadType.BATTLE_INFO, res->{
					if(!res.succeeded()){
						Log.human.error("MemberCallback getMemberAsync error", res.cause());
						return;
					}
					MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();

					List<HumanBrief> humanBriefList = res.result();
					Map<Long, HumanBrief> idDataMap = new HashMap<>();
					for(HumanBrief humanBrief : humanBriefList) {
						if (humanBrief == null) continue;
						idDataMap.put(humanBrief.getId(), humanBrief);
					}
					humanBriefList.clear();
					int rank = rankOpenIndex < 0 ? 0 : rankOpenIndex;
					boolean isMy = true;
					for(long id :humanIdList){
						rank++;
						int score = idScoreMap.get(id);
						// 玩家
						if(id == humanObj.id){
							// 自己排名
							Define.p_rank_info myRankInfo = new RankInfo(humanObj, score).rank_info.toBuilder().setRank(rank).build();
							rankData.setMyRankInfo(myRankInfo);
							isMy = false;
						}
						HumanBrief humanBrief = idDataMap.get(id);
						if(humanBrief == null) continue;
						if(humanBrief.getId() == id){
							RankInfo rankInfo = new RankInfo();
							rankData.addRankInfo(rankInfo.toRankInfo(humanBrief, rank, score));
						}
					}
					if(!isMy){
						msg.addRankDataList(rankData);
						port.doAction(() -> {
							humanObj.sendMsg(msg);
						});
						return;
					}
					RedisTools.getMyRank(EntityManager.redisClient, redisKey, String.valueOf(humanObj.id), ret2 -> {
						if(ret2.failed()){
							Log.temp.error("获取排行榜失败, rankSn={}, humanId={}", RankParamKey.rankTypeArena_1004, humanObj.id);
						}
						int rankMy = Utils.intValue(ret2.result()) <= 0 ? rankLen + 1 : Utils.intValue(ret2.result());
						RedisTools.getMyScore(EntityManager.getRedisClient(), redisKey, humanObj.id, ret3 -> {
							if(ret3.failed()){
								Log.temp.error("获取排行榜失败, rankSn={}, humanId={}", RankParamKey.rankTypeArena_1004, humanObj.id);
							}
							int score = Utils.intValue(ret3.result()) <= 0 ? ParamKey.initArenaScore : Utils.intValue(ret3.result());
							Define.p_rank_info myRankInfo = new RankInfo(humanObj, score).rank_info.toBuilder().setRank(rankMy).build();
							rankData.setMyRankInfo(myRankInfo);
							msg.addRankDataList(rankData);
							port.doAction(() -> {
								humanObj.sendMsg(msg);
							});
						});
					});
				});
			});
		});
	}

	private void _result_getArenaRobotMap(Param result, Param context){
		HumanObject humanObj = context.get("humanObj");
		int page = context.getInt("page");
		Map<Long, Define.p_rank_info> robotMap = result.get("robotMap");
		if (robotMap == null || robotMap.isEmpty()) {
			Log.temp.error("获取排行榜失败, rankSn={}", RankParamKey.rankTypeArena_1004);
			return;
		}
		int serverId = humanObj.getHuman().getServerId();
		String redisKey = ArenaManager.inst().getArenaRankKey(serverId, S.isBridge);
		int min = (page - 1) * RankParamKey.pageNum;
		int max = page * RankParamKey.pageNum;
		ConfRanktype confRanktype = ConfRanktype.get(RankParamKey.rankTypeArena_1004);
		if(max > confRanktype.show_num){
			min = confRanktype.show_num - RankParamKey.pageNum;
			min = min < 0 ? 0 : min;
			max = confRanktype.show_num;
		}

		Port port = Port.getCurrent();
		int rankOpenIndex = min;
		RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, min, max - 1, true, ret -> {
			if(ret.failed()) {
				Log.temp.error("获取排行榜失败, rankSn={}, page={}", RankParamKey.rankTypeArena_1004, page);
				return;
			}

			JsonArray json = ret.result();
			int size = json.getList().size();
			MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();
			Define.p_rank_data.Builder rankData = Define.p_rank_data.newBuilder();
			rankData.setType(RankParamKey.rankTypeArena_1004);
			rankData.setPage(page);
			rankData.setMaxPage(confRanktype.show_num / RankParamKey.pageNum);
			rankData.setTotalNum(size < page * RankParamKey.pageNum ? size : confRanktype.show_num);
			rankData.setServer(serverId);

			List<Long> humanIdList = new ArrayList<>();
			for(int i = 0; i < json.getList().size(); i+=2) {
				long id = Utils.longValue(json.getList().get(i));
				if(!ArenaManager.inst().isRobot(id)){
					humanIdList.add(id);
					if(humanIdList.size() >= 3){
						break;
					}
				}
			}
			// 没有真人
			if(humanIdList.isEmpty()){
				int rank = rankOpenIndex;
				// TODO 机器人加自己
				for(int i = 0; i < json.getList().size(); i+=2) {
					rank++;
					long id = Utils.longValue(json.getList().get(i));
					int score = Utils.intValue(json.getList().get(i + 1));
					if (ArenaManager.inst().isRobot(id)) {// 机器人
						Define.p_rank_info info = robotMap.get(id);
						if (info == null) {
							continue;
						}
						rankData.addRankInfo(info.toBuilder().setRank(rank).setScore(score).build());
					}
				}
				RedisTools.getMyScore(EntityManager.redisClient, redisKey, humanObj.id, ret2 -> {
					if(!ret2.succeeded()){
						Log.temp.error("获取排行榜失败, rankSn={}", RankParamKey.rankTypeArena_1004);
					}
					int score = Utils.intValue(ret2.result());
					if(score == 0){
						score = ParamKey.initArenaScore;
					}
					Define.p_rank_info myRankInfo = new RankInfo(humanObj, score).rank_info.toBuilder().setRank(0).build();
					rankData.setMyRankInfo(myRankInfo);
					msg.addRankDataList(rankData);
					port.doAction(() -> {
						humanObj.sendMsg(msg);
					});
				});
				return;
			}

			// 机器人排行榜+玩家
			// 查玩家
			HumanData.getList(humanIdList, HumanManager.inst().humanClasses, res->{
				if(!res.succeeded()){
					Log.human.error("MemberCallback getMemberAsync error", res.cause());
					return;
				}
				List<HumanData> humanDataList = res.result();
				Map<Long, HumanData> humanIdDataMap = new HashMap<>();
				for(HumanData humanData : humanDataList){
					if(humanData == null) continue;
					humanIdDataMap.put(humanData.human.getId(), humanData);
				}
				int rank = rankOpenIndex;
				boolean isMy = true;
				HumanData defaultHumanData = null;
				for(int i = 0; i < json.getList().size(); i+=2){
					rank++;
					long id = Utils.longValue(json.getList().get(i));
					int score = Utils.intValue(json.getList().get(i + 1));
					if(ArenaManager.inst().isRobot(id)){// 机器人
						Define.p_rank_info info = robotMap.get(id);
						if(info == null){
							continue;
						}
						rankData.addRankInfo(info.toBuilder().setRank(rank).setScore(score).build());
					} else {// 玩家
						if(id == humanObj.id){
							// 自己排名
							Define.p_rank_info myRankInfo = new RankInfo(humanObj, score).rank_info.toBuilder().setRank(rank).build();
							rankData.setMyRankInfo(myRankInfo);
							isMy = false;
						}
						if(defaultHumanData == null){
							HumanData humanData = humanIdDataMap.get(id);
							if(humanData == null){
								humanData = humanDataList.get(0);
							}
							if(defaultHumanData == null){
								defaultHumanData = humanData;
							}
							RankInfo rankInfo = new RankInfo(humanData.human, humanData.human2, score);
							rankInfo.rank = rank;
							Define.p_rank_info rank_info = rankInfo.rank_info.toBuilder().setRank(rankInfo.rank).setBelongId(id).build();
							rankData.addRankInfo(rank_info);
						} else {
							HumanData humanData = humanIdDataMap.get(id);
							if(humanData == null){
								humanData = defaultHumanData;
							}
							RankInfo rankInfo = new RankInfo(humanData.human, defaultHumanData.human2, score);
							rankInfo.rank = rank;
							Define.p_rank_info rank_info = rankInfo.rank_info.toBuilder().setRank(rankInfo.rank).setBelongId(id).build();
							rankData.addRankInfo(rank_info);
						}
					}
				}
				if(!isMy){
					msg.addRankDataList(rankData);
					port.doAction(() -> {
						humanObj.sendMsg(msg);
					});
					return;
				}
				RedisTools.getMyScore(EntityManager.redisClient, redisKey, humanObj.id, ret2 -> {
					if (!ret2.succeeded()) {
						Log.temp.error("获取排行榜失败, rankSn={}", RankParamKey.rankTypeArena_1004);
					}
					int score = Utils.intValue(ret2.result());
					if(score == 0){
						score = ParamKey.initArenaScore;
					}
					Define.p_rank_info myRankInfo = new RankInfo(humanObj, score).rank_info.toBuilder().setRank(0).build();
					rankData.setMyRankInfo(myRankInfo);
					msg.addRankDataList(rankData);
					port.doAction(() -> {
						humanObj.sendMsg(msg);
					});
				});
			});
		});
	}

	/**
	 * 排行榜点赞信息
	 * @param humanObj
	 * @param rankSn
	 */
	public void _msg_rank_like_info_c2s(HumanObject humanObj, int rankSn) {
		ConfRanktype conf = ConfRanktype.get(rankSn);
		if(conf == null || conf.if_like == 0){
			// 排行榜没开启点赞，直接return
			Log.rank.warn("_msg_rank_like_info_c2s fail. rankSn={} if_like == 0", rankSn);
			return;
		}
		HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyRankLikeInfo.getType());
		Map<Integer, Map<Integer, Integer>> likeInfoMap = Utils.jsonToIntMapIntInt(info.getParam());
		Map<Integer, Integer> likeInfo = likeInfoMap.getOrDefault(rankSn, new HashMap<>());
		// 发送排行榜点赞信息
		sendRankLikeInfo(humanObj, rankSn, likeInfo);
//		Log.rank.info("_msg_rank_like_info_c2s humanObj={} rankSn={}", humanObj.id, rankSn);
	}

	/**
	 * 发送排行榜点赞信息
	 * @param humanObj
	 * @param rankSn
	 * @param likeInfo
	 */
	public void sendRankLikeInfo(HumanObject humanObj, int rankSn, Map<Integer, Integer> likeInfo) {
		MsgRank.rank_like_info_s2c.Builder msg = MsgRank.rank_like_info_s2c.newBuilder();
		msg.setRankSn(rankSn);
		List<Integer> likeList = new ArrayList<>();
		for(int i=1; i<=RankParamKey.likeTopNum; i++){
			likeList.add(likeInfo.getOrDefault(i, 0));
		}
		msg.addAllLikeStatusList(likeList);
		humanObj.sendMsg(msg);
//		Log.rank.info("sendRankLikeInfo humanObj={} msg={}", humanObj.id, msg);
	}

	/**
	 * 排行榜点赞
	 * @param humanObj
	 * @param rankSn
	 * @param likeRank
	 */
	public void _msg_rank_like_c2s(HumanObject humanObj, int rankSn, int likeRank) {
		ConfRanktype conf = ConfRanktype.get(rankSn);
		if(conf == null || conf.if_like == 0){
			// 排行榜没开启点赞，直接return
			Log.rank.warn("_msg_rank_like_c2s fail. rankSn={} if_like == 0", rankSn);
			return;
		}
		if(likeRank < 1 || likeRank > RankParamKey.likeTopNum){
			Log.rank.warn("_msg_rank_like_c2s fail. likeRank={} out of range", likeRank);
			return;
		}
		HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyRankLikeInfo.getType());
		Map<Integer, Map<Integer, Integer>> likeInfoMap = Utils.jsonToIntMapIntInt(info.getParam());
		Map<Integer, Integer> likeInfo = likeInfoMap.getOrDefault(rankSn, new HashMap<>());
		int alreadyLike = likeInfo.getOrDefault(likeRank, 0);
		if(alreadyLike == 1){
			Log.rank.warn("今日rankSn={} likeRank={} 已点赞 humanId={}", rankSn, likeRank, humanObj.id);
			return;
		}
		likeInfo.put(likeRank, 1);
		likeInfoMap.put(rankSn, likeInfo);
		info.setParam(Utils.mapIntMapIntIntToJSON(likeInfoMap));
		humanObj.saveDailyResetRecord();
		// 点赞奖励
		int[] rewards = conf.like_reward;
		ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.排行榜点赞奖励);
		InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewards);
		// 发送排行榜点赞信息
		sendRankLikeInfo(humanObj, rankSn, likeInfo);
//		Log.rank.info("_msg_rank_like_c2s humanObj={} rankSn={} likeRank={}", humanObj.id, rankSn, likeRank);
	}
}
