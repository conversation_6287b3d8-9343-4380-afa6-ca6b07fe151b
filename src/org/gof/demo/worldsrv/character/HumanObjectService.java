package org.gof.demo.worldsrv.character;

import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.Message;
import org.apache.commons.lang3.StringUtils;
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.PortPulseQueue;
import org.gof.core.Service;
import org.gof.core.connsrv.ConnectionProxy;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.support.*;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.seam.msg.HumanExtendMsgHandler;
import org.gof.demo.worldsrv.activity.ActivityControlType;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.calculator.ActivityControlGuildPay;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.activity.calculator.IActivityControl;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.botHelper.DebugHumanVo;
import org.gof.demo.worldsrv.captureSlave.CaptureSlaveManager;
import org.gof.demo.worldsrv.carPark.CarParkManager;
import org.gof.demo.worldsrv.carPark.MountVo;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfCrossWarKv;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.config.ConfPayMall;
import org.gof.demo.worldsrv.crossWar.CrossWarConst;
import org.gof.demo.worldsrv.crossWar.CrossWarManager;
import org.gof.demo.worldsrv.doubleChapter.DoubleChapterManager;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.flyPet.FlyPetManager;
import org.gof.demo.worldsrv.friend.FavorabilityVo;
import org.gof.demo.worldsrv.friend.FriendManager;
import org.gof.demo.worldsrv.friend.FriendType;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.GuildManager;
import org.gof.demo.worldsrv.guild.HumanBriefVO;
import org.gof.demo.worldsrv.home.*;
import org.gof.demo.worldsrv.human.*;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.inform.InformManager;
import org.gof.demo.worldsrv.inform.LanguageKey;
import org.gof.demo.worldsrv.instance.BridgeInstanceAidVO;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.intergration.PF_PAY_Manager;
import org.gof.demo.worldsrv.kungFuRace.KungFuRaceConst;
import org.gof.demo.worldsrv.kungFuRace.KungFuRaceManager;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.mall.MallManager;
import org.gof.demo.worldsrv.match.MatchMember;
import org.gof.demo.worldsrv.monster.DeadMonsterInfo;
import org.gof.demo.worldsrv.msg.*;
import org.gof.demo.worldsrv.pocketLine.PocketLineManager;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.TokenItemType;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.DailyResetTypeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.team.TeamMember;
import org.gof.demo.worldsrv.team.TeamServiceProxy;
import org.gof.platform.sdk.PlaceOurpalm;

import java.util.*;

@DistrClass(
        importClass = {Mail.class, FillMail.class, Set.class, Map.class, List.class,BridgeInstanceAidVO.class,
                MatchMember.class, DeadMonsterInfo.class, Vector2D.class, MoneyItemLogKey.class, PocketLine.class, FarmVo.class, LandVo.class, RobberVo.class,
                Guild.class, ActivityVo.class,PayGift.class,Define.p_car_park_record.class,Define.p_double_chapter_help_player.class,Define.p_battle_role.class,
                Define.p_double_chapter_teammate.class,Define.p_double_chapter_teammate_detail.class
        }
)
public class HumanObjectService extends Service {
    private HumanExtendMsgHandler msgHandler = MsgHandler.getInstance(HumanExtendMsgHandler.class);

    //对应的玩家对象
    private final HumanObject humanObj;
    private long currTime;
    private long currLastTime;
    private int deltaTime;

    public TickTimer checkMailFive = new TickTimer(1*Time.MIN);

    public void startup() {
//		super.startup();
    }

    @Override
    public void pulseOverride() {
        humanObj.pulse();

        if(checkMailFive.isPeriod(Port.getTime())){
//            Log.temp.info("humanId={}, name= {}", humanObj.id, humanObj.name);
        }
    }

    /**
     * 构造函数
     *
     * @param
     */
    public HumanObjectService(HumanObject humanObj, Port port) {
        super(port);
        this.humanObj = humanObj;
    }

    /**
     * 获取所属Port
     *
     * @return
     */
    public Port getPort() {
        return Port.getCurrent();
    }

    /**
     * 离开地图
     *
     * @param stageTargetId
     */
    @DistrMethod
    public void leave(long stageTargetId) {
    }

    @DistrMethod(argsImmutable = true)
    public void ChangeConnPoint(CallPoint point) {
        if(S.isBridge){
            // 跨服重连处理
            humanObj.connPoint.portId = point.portId;
            humanObj.connPoint.servId = point.servId;
        } else {
            humanObj.connPoint = point;
        }
        humanObj.clearCloseStatus();
        humanObj.iosQuitTime = 0;
        HumanManager.inst().syncHumanGlobal(humanObj);
    }

    @DistrMethod
    public void connLoadPocketLine() {
        Log.temp.info("===重新加载待办事件===humanId={}, name={}, account={}", humanObj.id, humanObj.name, humanObj.account);
        // 待办事件
        EntityManager.getEntityListAsync(PocketLine.class, humanObj.id, (res) -> {
            if (!res.succeeded()) {
                //加载失败
                Log.temp.error("===加载PocketLine失败，humanId={}", humanObj.id);
                return;
            }
            List<PocketLine> lineList = res.result();
            for (PocketLine pocketLine : lineList) {
                humanObj.dataPers.pocketLine.add(pocketLine);
            }
            MallManager.inst().pocketLine(humanObj);
        });
    }
    @DistrMethod
    public void moveToLeader(long leaderId, long stageId, int mapSn, int repSn, double posX, double posY) {
        if (humanObj.getStageNowId() != stageId) {
            return;
        }

        moveTo(stageId, mapSn, posX, posY);
    }

    /**
     * 移动到地图指定位置
     */
    private void moveTo(long stageId, int mapSn, double posX, double posY) {
        Vector2D destPos = new Vector2D(posX, posY);
        if (humanObj.getStageNowId() == stageId) {
            Vector2D posNow = humanObj.getPosNow();
            if (!posNow.equals(destPos) || posNow.distance(destPos) <= 2) {
            }
        } else {
            if (!humanObj.isStageSwitching()) {
                doSwitch(stageId, mapSn, destPos);
            }

        }
    }

    /**
     * 切换到指定地图
     *
     * @param stageId
     */
    @DistrMethod
    public void switchTo(long stageId, Object ... params) {
//        Log.game.info("switchTo isBridge {}", S.isBridge);
        doSwitch(stageId,params);
    }

    /**
     * 由于业务逻辑调用过多，所以暂时保留，以后都使用可变参数方法
     * @param stageId
     * @param mapSn
     * @param posAppear
     */
    private void doSwitch(long stageId, int mapSn, Vector2D posAppear) {
        doSwitch(stageId, "posAppear" , posAppear);
    }

    private void doSwitch(long stageId, Object ... params) {
		if(!humanObj.isClientStageReady){
			// 登录切跨服允许
            Param param = new Param(params);
            boolean isLogin = Utils.getParamValue(param,"isLogin" , false);
		    if(!isLogin)
			    return;
		}

//        StageManager.inst().switchTo(humanObj, stageId, params);

    }

    @DistrMethod
    public void memberSwitchToMirror(long leaderId, Vector2D leaderPos, long stageId, int mapSn) {
        if (leaderId == humanObj.id) {
            return;
        }

        doSwitch(stageId, mapSn, humanObj.getPosNow());

    }


    /**
     * 接受并转发通信消息
     *
     * @param connId
     * @param chunk
     */
    @DistrMethod
    public void msgHandler(long connId, byte[] chunk) {
        //忽略错误连接ID的请求
        long humanConnId = (long) humanObj.connPoint.servId;
        if (humanConnId != connId) {
            Log.game.error("玩家链接变更， humanId={}, name={}, connId={}, old ={} ", humanObj.id, humanObj.name,connId, humanConnId);
            //将发送错误连接的请求连接关了
            CallPoint connPoint = new CallPoint();
            connPoint.nodeId = port.getCallFromNodeId();
            connPoint.portId = port.getCallFromPortId();
            connPoint.servId = connId;

            ConnectionProxy prx = ConnectionProxy.newInstance(connPoint);
            prx.close();
            return;
        }

//        Log.game.info("msgHandler nodeId {} portId {} servId {}", port.getCallFromNodeId(), port.getCallFromPortId(), connId);
        msgHandler.handle(chunk, "humanObj", humanObj);
    }

    /**
     * 连接关闭
     *
     * @param connId
     */
    @DistrMethod
    public void connClosed(long connId) {
        //忽略错误连接ID的请求
        long humanConnId = (long) humanObj.connPoint.servId;
		Log.temp.error("连接关闭connClosed 正在关闭的connId={},human当前正常connId={}, 当前时间戳：{}, account={}, name={} humanId={}", connId,humanConnId, Port.getTime(), humanObj.account, humanObj.name, humanObj.id);
        if (humanConnId != connId) {
            return;
        }
        humanObj.connDelayCloseClear();
    }

    @DistrMethod
    public void kickClosed() {
        //直接T人
        humanObj.connCloseClear();
    }

    /**
     * 连接存活验证
     *
     * @param connId
     */
    @DistrMethod
    public void connCheck(long connId) {
//        Log.temp.error("====链接存在 connId={}", connId);
        //HumanManager.inst().syncHumanGlobal(humanObj);
        port.returns(true);
    }

    /**
     * 调度事件的处理 ， 来自于 humanGlobalSerivce
     *
     * @param key
     */
    @DistrMethod
    public void onSchedule(int key, long timeLast) {
        HumanManager.inst().onScheduleEvent(humanObj, key, timeLast);
    }


    /**
     * 接收邮件
     */
    @DistrMethod
    public void mailAccept(Mail mail) {
        MailManager.inst().addNew(humanObj, mail);

    }

    @DistrMethod
    public void getHuman() {
        getPort().returns(humanObj.getHuman());
    }

    @Override
    public Object getId() {
        return humanObj.id;
    }

    public HumanObject getHumanObj() {
        return humanObj;
    }

    @DistrMethod
    public void eventFire(int eventKey, Param param) {
        param.put("humanObj", humanObj);
        Event.fire(eventKey, param.toArray());
    }

    /**
     * 接收补偿邮件
     *
     * @param fillMail
     */
    @DistrMethod
    public void acceptFillMail(FillMail fillMail) {
        if(humanObj.operation.fillMailIdList.contains(fillMail.getId())){
            return;
        }
        // 玩家能否接收全服邮件
        if(!MailManager.canReceivedFillMail(humanObj.id, fillMail)){
            return;
        }
        String redisKey = HumanManager.getFillMailReceivedKey(humanObj.id);
        Port port = Port.getCurrent();
        RedisTools.get(EntityManager.redisClient, redisKey,getRes->{
            if(getRes.failed()){
                Log.game.error("get redisKey failed! key={}",redisKey, getRes.cause());
                return;
            }
            port.doAction(()->{
                String fillMailIdJSON = getRes.result();
                List<Long> fillMailIdList = Utils.strToLongList(fillMailIdJSON);
                if(fillMailIdList.contains(fillMail.getId())){
                    return;
                }
                if (humanObj.operation.fillMailIdList.contains(fillMail.getId())) {
                    return;
                }
                humanObj.operation.fillMailIdList.add(fillMail.getId());
                //领取记录
                fillMailIdList.add(fillMail.getId());
                RedisTools.set(EntityManager.redisClient, redisKey, Utils.listToString(fillMailIdList),res->{
                    if(res.failed()){
                        Log.game.error("set redisKey failed! key={}",redisKey, res.cause());
                        return;
                    }
                    port.doAction(()->{
                        if ("".equals(fillMail.getItemSn()) && "".equals(fillMail.getItemNum())) {
                            MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, MailManager.MAIL_SN_COMMON, fillMail.getTitle(), fillMail.getContent(), null, null);
                        } else {
                            int[] sns = Util.strToIntArray(fillMail.getItemSn());
                            int[] nums = Util.strToIntArray(fillMail.getItemNum());
                            MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, MailManager.MAIL_SN_ATTACHMENT, fillMail.getTitle(), fillMail.getContent(), sns, nums);
                        }
                    });
                });
            });
        });

    }


    /**
     * 击杀帮会BOSS事件
     */
    @DistrMethod
    public void factionBossEndEvent() {
        Event.fire(EventKey.FACTION_BOSS_END, "humanObj", humanObj);
    }

    /**
     * 击杀世界BOSS事件
     */
    @DistrMethod
    public void worldBossEndEvent() {
        Event.fire(EventKey.WORLD_BOSS_END, "humanObj", humanObj);
    }


    @DistrMethod
    public void onHumanJoinTeam(long teamId) {
        long oldTeamId = humanObj.getTeamBundleId();
        if (oldTeamId <= 0 && teamId > 0) {
            humanObj.setTeamBundleId(teamId);
        }
    }

    @DistrMethod
    public void onHumanLeaveTeam() {
        long oldTeamId = humanObj.getTeamBundleId();
        if (oldTeamId > 0) {
            humanObj.setTeamBundleId(0L);
        }
    }

    /**
     * 变更teamPlayer的人数
     */
    @DistrMethod
    public void teamChange(String leaderName, long leaderId) {
        humanObj.leaderName = leaderName;
        humanObj.leaderId = leaderId;
    }

    @DistrMethod
    public void setTeamId(long teamId) {
        if(teamId == humanObj.getTeamBundleId() && teamId == humanObj.getHuman2().getTeamId()){
            return;
        }

        humanObj.setTeamBundleId(teamId);
    }


    @DistrMethod
    public void kickFactionMemberOut() {
        if (this.humanObj == null) {
            return;
        }
        Event.fire(EventKey.SCENE_EVENT_43, "humanObj", this.humanObj);
    }

    /**
     * 获取玩家信息
     */
    @DistrMethod
    public void getDebugHumanInfo() {
        HumanObject humanObj = this.getHumanObj();
        DebugHumanVo humanVo = creatDebugHuman(humanObj);

        port.returns("debugHuman", humanVo);
    }

    private DebugHumanVo creatDebugHuman(HumanObject humanObj){
        DebugHumanVo debugHumanVo = new DebugHumanVo();
//        debugHumanVo.humanId = humanObj.id;
//        debugHumanVo.name = humanObj.name;
//        debugHumanVo.soul = humanObj.getHuman().getSoul();
//        debugHumanVo.level = humanObj.getHuman().getLevel();
//        debugHumanVo.combat = humanObj.getHuman().getCombat();
//        debugHumanVo.hpCur = humanObj.getHuman().getHpCur();
//        debugHumanVo.hpMax = humanObj.getHuman().getHpMax();
//        debugHumanVo.speed = humanObj.getHuman().getSpeed();
//        debugHumanVo.buffs = new ArrayList<>();
//        for (Buff buff : humanObj.dataPers.buffs.values()) {
//            DebugBuffVo buffVo = new DebugBuffVo();
//            buffVo.buffId = buff.getId();
//            buffVo.timeLeft = buff.getTimeEnd() - humanObj.getTime();
//            debugHumanVo.buffs.add(buffVo);
//        }
//        debugHumanVo.stageSn = humanObj.stageObj.sn;
//        debugHumanVo.pos = humanObj.getPosNow();
//        for(UnitObjectStateKey state : humanObj.state.keySet()){
//            debugHumanVo.stateList.add(state.getType());
//        }
//        debugHumanVo.createTime = humanObj.getHuman().getTimeCreate();
//        debugHumanVo.loginTime = humanObj.getHuman().getTimeLogin();
//        debugHumanVo.teamID = humanObj.getTeamBundleId();
//        debugHumanVo.factionID = humanObj.getHuman().getFactionId();
//        debugHumanVo.currency = humanObj.currency;
//        debugHumanVo.ip = humanObj.clientIP;
        return debugHumanVo;
    }

    /**
     * 禁言/封号
     *
     * @param type    1:禁言   2:封号   3:禁言喇叭
     * @param timeEnd
     */
    @DistrMethod
    public void sealAccount(int type, long timeEnd) {
        String title = "禁言";
        if (type == 1) {
            title = "禁言";
            humanObj.getHuman3().setSilenceEndTime(timeEnd);
            humanObj.getHuman3().update();
        } else if (type == 2) {
            title = "封号";
            EntityManager.getEntityListAsync(Account.class, humanObj.account, res-> {
                if (res.failed()) {
                    Log.temp.error("accountManager.loadRedis failed: account={}, serverId={}", humanObj.account, res.cause());
                    return;
                }
                List<Account> accountList = res.result();
                long nowTime = Port.getTime();
                for(Account accountTemp : accountList) {
                    if(accountTemp.getId() == humanObj.id){
                        accountTemp.setSealEndTime(timeEnd);
                        accountTemp.update();
                        RankManager.inst().removeRank(humanObj.id);
                        break;
                    }
                }
                if (System.currentTimeMillis() < timeEnd) {
                    HumanGlobalServiceProxy hprx = HumanGlobalServiceProxy.newInstance();
                    hprx.kick(humanObj.id, 8505, Utils.formatTime(timeEnd, "yyyy-MM-dd HH:mm:ss"));
                }
            });
        } else if (type == 3) {
            title = "禁言喇叭";
            humanObj.getHuman3().setTrumpetEndTime(timeEnd);
            humanObj.getHuman3().update();
        } else {
            Log.temp.error("===未实现此类型规则代码type={}", type);
        }
        Log.temp.error("禁用操作={}  humanId={} name={} type={}  禁用截止时间={}", title, humanObj.id, humanObj.name, type, timeEnd);
    }

    /**
     * 禁言
     *
     * @param keepTime 持续时间（单位：毫秒）
     */
    @DistrMethod
    public void silence(long keepTime) {

        humanObj.silence(keepTime);
    }

    /**
     * 充值
     *
     * @return
     */
    @DistrMethod
    public void pay(String param) {
        boolean result = false;
        JSONObject jo = Utils.toJSONObject(param);
        String propId = jo.getString("propId");
        // 订单id
        String orderId = jo.getString("orderId");
        // 判断内存中是否已经充值过了，避免重复
        for (PayLog log : humanObj.dataPers.payLogs) {
            if (log.getOrderId().equals(orderId)) {
                Log.temp.error("充值失败！重复充值 orderId={}, jo={}", orderId, jo);
                port.returns(result);
                return;
            }
        }
        ConfPayMall confPayMall = GlobalConfVal.getConfPayMallByIosPid(propId);
        if(confPayMall == null){
            Log.game.error("充值失败！充值参数错误，找不到配置表propID={},orderId={}, jo={}", propId, orderId, jo);
            port.returns(result);
            return;
        }
        int paySn = confPayMall.sn;

        Event.fire(EventKey.PAY_NOTIFY, "humanObj", humanObj, "sn", paySn, "orderId", orderId, "giftId", "");
        // 通知
        MallManager.inst().sendMsg_pay_mall_recharge_s2c(humanObj, paySn, orderId, PF_PAY_Manager.payType1,  "");


        PF_PAY_Manager.inst().uploadChargeRecord(humanObj.id, jo, 0);
        PayLog log = PF_PAY_Manager.inst().recordPayLog(humanObj.id, jo, "充值成功");
        humanObj.dataPers.payLogs.add(log);
        result = true;

        port.returns(result);
    }

    @DistrMethod
    public void chargeGs(int paySn) {
        // 通知
        MallManager.inst().sendMsg_pay_mall_recharge_s2c(humanObj, paySn, "",  PF_PAY_Manager.payType1, "");

        Event.fire(EventKey.PAY_NOTIFY, "humanObj", humanObj, "sn", paySn, "orderId", "", "giftId", "");
    }

    /**
     * 台湾充值
     *
     * @return
     */
    @DistrMethod
    public void payTw(String param) {
        boolean result = false;
        JSONObject jo = Utils.toJSONObject(param);
        String propId = jo.getString("propId");
        int actualPrice = jo.getIntValue("actualPrice");
        int chargePrice = jo.getIntValue("chargePrice");
        String orderId = jo.getString("orderId");

        //普通充值
        if (propId == null || propId.isEmpty()) {
            MoneyManager.inst().produceMoneyAdd(humanObj, TokenItemType.Money999, chargePrice, MoneyItemLogKey.充值);
//            PaymentManager.inst().setVipLevel(humanObj, null, chargePrice, orderId, "", actualPrice);
            PF_PAY_Manager.inst().uploadChargeRecord(humanObj.id, jo, chargePrice);
            PF_PAY_Manager.inst().recordPayLog(humanObj.id, jo, "充值成功");

            port.returns(true);
            return;
        }
        //购买RMB商品
        int sn = Integer.parseInt(propId.replace("lingzhu", ""));
        switch (sn) {
            case 1:
                sn = 0;
                break;
            case 2:
                sn = 100;
                break;
            case 3:
                sn = 101;
                break;
            case 4:
                sn = 103;
                break;
            case 5:
                sn = 106;
                break;
            default:
                break;
        }

//        long addCount = PaymentManager.inst().pay(humanObj, sn);
//        PF_PAY_Manager.inst().uploadChargeRecord(humanObj.id, jo, addCount);
        PF_PAY_Manager.inst().recordPayLog(humanObj.id, jo, "充值成功");


//		int confGold = 0;
//		if(sn >= 100){
//			confGold = ConfSendMonthCard.get(sn).gold;
//		}else{
//			confGold = ConfPayCharge.get(sn).gold;
//		}
//
//		if(confGold == actualPrice<<1 || C.IS_OPENGM){
//			//充值校验正确
//			addCount = PaymentManager.inst().pay(humanObj, sn);
//			if(addCount == null){
//				PF_PAY_Manager.inst().recordPayLog(humanObj.id, jo, "配置表错误");
//				Inform.user(humanObj.id, Inform.提示错误, "配置表错误");
//				port.returns(result);
//				return;
//			}
//
//			PF_PAY_Manager.inst().uploadChargeRecord(humanObj.id, jo, addCount);
//			PF_PAY_Manager.inst().recordPayLog(humanObj.id, jo, "充值成功");
//			result = true;
//		}else{
//			//充值校验不正确
//			int extraRmb = -1;
//			if(actualPrice<<1 > confGold){
//				extraRmb = actualPrice<<1 - confGold;
//				//充值错误,但是找到对应金额
//				addCount = PaymentManager.inst().pay(humanObj, sn);
//				if(addCount == null){
//					PF_PAY_Manager.inst().recordPayLog(humanObj.id, jo, "配置表错误");
//					Inform.user(humanObj.id, Inform.提示错误, "配置表错误");
//					port.returns(result);
//					return;
//				}
//
//				PF_PAY_Manager.inst().uploadChargeRecord(humanObj.id, jo, addCount);
//				result = true;
//			}else{
//				extraRmb = actualPrice<<1;
//			}
//			if(extraRmb > 0){
//				//配置表里没找到对应金额
//				addCount = (long) (actualPrice<<1);
//				HumanManager.inst().produceMoneyAdd(humanObj, ProduceMoneyKey.gold, addCount, MoneyAddLogKey.错误充值);
//				PaymentManager.inst().setVipLevel(humanObj, addCount, humanObj.getHuman().getPayChargeOnceIds(), orderId, propId, actualPrice<<1);
//				PF_PAY_Manager.inst().recordPayLog(humanObj.id, jo, "充值" + actualPrice + "元，有异常");
//			}
//		}

//        //返回前段充值信息
//        if (sn >= 100) {
//            SCCharge100.Builder msg = SCCharge100.newBuilder();
//            msg.setOrderId(jo.getString("orderId"));
//            msg.setSn(sn);
//
//            humanObj.sendMsg(msg);
//        } else {
//            SCCharge.Builder msg = SCCharge.newBuilder();
//            msg.setOrderId(jo.getString("orderId"));
////			if(addCount == null){
////				addCount = 0L;
////			}
////            msg.setGold(addCount);
//            msg.setSn(sn);
//
//            humanObj.sendMsg(msg);
//        }
        port.returns(result);
    }


    //通知所有在线玩家活动变更状态
    @DistrMethod
    public void onActivityListChange() {

    }

    @DistrMethod
    public void onQuestChange(Set<Integer> questSn) {
        //humanObj
//        QuestSet quest = humanObj.dataPers.quest;
//        quest.refreshQuest(humanObj, questSn);
    }


    /**
     * 给玩家禁言
     *
     * @param
     */
    @DistrMethod
    public void setSilenceEndTime() {
        long curTime = Port.getTime();
        long endTime = humanObj.getHuman3().getSilenceEndTime();
        int hour = 1;
        if (endTime > curTime) {
            endTime += hour * Time.HOUR;
        } else {
            endTime = curTime + hour * Time.HOUR;
        }
        humanObj.getHuman3().setSilenceEndTime(endTime);
        humanObj.getHuman3().update();
    }


    /**
     * 设置gs,福利账号
     *
     * @param type
     */
    @DistrMethod
    public void gm_Gs(int type, int value) {

    }



    @DistrMethod
    public void doLeaveRep() {

    }

    /***
     * 怪物死亡增加任务进度
     */
    @DistrMethod
    public void mosterDieToAddQuestProcess(String monsterSn, int mapSn, boolean sharing) {
//        QuestManager.inst()._listener_upgress_quest_progress(
//                new Param().put("humanObj", humanObj), EventKey.MONSTER_BE_KILLED, monsterSn, mapSn, sharing
//        );
        Event.fire(EventKey.QUEST_CYCLE_LISTEN, "humanObj", humanObj, "param",
                new Param("monsterSn", monsterSn), "eventKey", EventKey.MONSTER_BE_KILLED);
    }

    /**
     * 后台修改累充
     *
     * @param sessionName
     */
    @DistrMethod
    public void resetGameSession(String sessionName) {
        if (!sessionName.contains(",")) return;
        String[] split = sessionName.split(",");
        HumanObject humanObj = getHumanObj();
        if (StringUtils.isBlank(split[1])) return;
        long i = Long.valueOf(split[1]);
        long chargeGold = humanObj.getHuman3().getChargeGold();
        humanObj.getHuman3().setChargeGold(chargeGold + i);

        // 特殊处理
        long chargeGoldSpecial = humanObj.getHuman3().getChargeGoldSpecial() + i;
        humanObj.getHuman3().setChargeGoldSpecial(chargeGoldSpecial);
        humanObj.getHuman3().update();

        int rmb = (int) (i / 100);
//        ActivityControlManager.inst().payOfChargeGoldRank(humanObj, rmb);
//        if (humanObj.dataPers != null && humanObj.dataPers.activityControlData != null && humanObj.dataPers.activityControlData.getControl() != null) {
//            ActivityControl control = humanObj.dataPers.activityControlData.getControl();
//            if (control.getControl_50() == 0) {
//                humanObj.dataPers.activityControlData.getControl().setControl_50(1);
//            }
//        }

    }


    @DistrMethod
    public void backMethod4(String param) {

    }

    /**
     * 预留线上修改
     *
     * @param str
     */
    @DistrMethod
    public void test01(String str) {


    }

    @DistrMethod
    public void test02(String str) {


    }

    @DistrMethod
    public void test03(String str) {


    }

    @DistrMethod
    public void test04(String str) {


    }

    @DistrMethod
    public void reserveMethod5(String param) {

    }

    @DistrMethod
    public void reserveMethod6(String param) {

    }

    /**
     * 注意，这里只是拷贝，不是真实玩家对象
     */
    @DistrMethod
    public void getHumanObjcetCopy() {
        port.returns(humanObj);
    }

    @DistrMethod
    public void getHumanObjcetMirr() {
        HumanObjectMirr mirr = new HumanObjectMirr();
        port.returns(mirr);
    }

    /**
     * 切入跨服地图，玩家自身隐藏
     */
    @DistrMethod
    public void bridgeStageHide() {
//        HumanInfoChange.listen(humanObj);
        humanObj.bridge = true;
        humanObj.stageHide();
    }

    /**
     * 从跨服地图归来，玩家自身展示
     */
    @DistrMethod
    public void bridgeStageShow(Param param) {
        Log.temp.error("===从跨服地图归来humanId={}", humanObj.id);
        //展示，终止玩家切地图状态
//        HumanInfoChange.listen(humanObj);
        humanObj.bridge = false;
        humanObj.isSpectators = false;
        humanObj.dataPers.isSwitchBridge = false;

        //还原玩家状态
//        humanObj.isBridgeDeadDayLight = false;
//        humanObj.isBridgeSeekHegemonyMatch = false;

//        HumanStatusManager.inst().afterSwitch(humanObj,humanObj.stageObj.confMap.sn);
        //玩家的状态不在跨服了
        bridgeStageInSet(false);


        //变更与客户端的连接
        CallPoint connPoint = humanObj.connPoint;
        ConnectionProxy proxy = ConnectionProxy.newInstance(connPoint);

        //更新连接服务器的玩家信息
        proxy.updateStatus(port.getNodeId(), port.getId());
        Log.game.info("===从跨服地图归来humanId={} 更换连接信息 nodeId={} portId={}", humanObj.id, port.getNodeId(), port.getId());

        //从跨服回来的时候执行下待办
        pocketLineProcessBridge();
        Log.game.info("===从跨服地图归来humanId={} 代办执行完毕", humanObj.id);

        Log.temp.error("===从跨服地图归来humanId,需要的操作执行完毕={}", humanObj.id);
    }

    /**
     * 跨服回来的时候处理一下待办
     */
    private void pocketLineProcessBridge() {
        // 担心线程有问题 这里特殊验证一下
        if(this.getPort() != humanObj.getPortNow()){
            Log.human.error("发现多线程问题 多个HuanObjectService 在操作同一个humanObject humanObj={}, humanObjPort={}, humanServicePort = {}",
                    humanObj, humanObj.getPortNow(), this.getPort());
        }

        PocketLineManager.inst().loadPocketList(humanObj, true);
    }


    ///////////////////********************以下是跨服相关调用********************/////////////////////
    /**
     * 跨服地图判断
     * @param stageIn
     */
    @DistrMethod
    public void bridgeStageInSet(boolean stageIn) {
        Log.stageCommon.error("bridgeStageInSet, {} stageIn {}", humanObj.name, stageIn);
        humanObj.getHuman3().setBridgeStageIn(stageIn);
    }

    /**
     * 进入跨服地图，跨服地图设置
     *
     * @param stageId
     * @param stageSn
     * @param stageType
     * @param pos
     */
    @DistrMethod
    public void bridgeStageHistorySet(long stageId, int stageSn, String stageType, Vector2D pos) {
        HumanManager.inst().bridgeStageHistorySet(humanObj, stageId, stageSn, stageType, pos);
    }

    /**
     * 处理单条待办
     * @param pocketLine
     */
    @DistrMethod
    public void pockLineProcess(PocketLine pocketLine){
        //记录待处理
        humanObj.dataPers.pocketLine.add(pocketLine);

        if(humanObj.isStageSwitching()) return;

        // 跨服状态暂不处理，等回来后统一处理
        if(humanObj.bridge) return;

        // 处理待办
        PocketLineManager.inst().loadPocketList(humanObj, true);
    }

    /**
     * 玩家在跨服服务器离线，需要在游戏服务器断开连接
     */
    @DistrMethod
    public void bridgeLogout() {
        HumanGlobalServiceProxy hgsprx = HumanGlobalServiceProxy.newInstance();
        hgsprx.cancel(humanObj.id);
//        if(humanObj.stageObj instanceof FactionLeagueWaittingZone){
//            // TODO 不知道为啥只断连接，不清理数据，为了不影响原有逻辑，但车轮战要清理
//            humanObj.connCloseClear();
//        }
    }

    /**
     * 跨服地图位置坐标更新，暂时只认为玩家跨服时只在一张地图
     *
     * @param pos
     */
    @DistrMethod
    public void bridgeStagePosUpdate(Vector2D pos) {
        HumanManager.inst().bridgeStagePosUpdate(humanObj, pos);
    }


    @DistrMethod
    public void updateStageHistory(String stageHistory) {
        humanObj.getHuman3().setStageHistory(stageHistory);
    }


    //========================TODO 业务代码请写下面， 业务逻辑开始=====================================

    /**
     * 跨服使用道具 , 跨服判断已经做完了 只删就好
     */
    @DistrMethod
    public void bridgeItemUse(int packType , int itemSn,int num) {
//        Item item = ItemBagManager.inst().getItem(humanObj, packType, itemId);
//        item.setNum(item.getNum() - num);

//        ItemBagManager.inst().remove(humanObj, itemSn, num, MoneyAddLogKey.跨服物品使用);
    }
    @DistrMethod
    public void bridgeItemUse(int packType, int itemSn,int num, MoneyItemLogKey log){
    }



    @DistrMethod
    public void clearModuleReset(int type){humanObj.humanReset.clearModuleReset(type);}



    /**
     * 全服邮件
     * <AUTHOR>
     * @Date 2023/7/31
     * @Param
     */
    @DistrMethod
    public void sendFillMail(FillMail fillMail) {
        Human human = humanObj.getHuman();
        int level = human.getLevel();
        if(fillMail.getLevelMin() > level){
            return;
        }
        if(fillMail.getLevelMax() > 0 && level > fillMail.getLevelMax()){
            return;
        }
        long createTime = human.getTimeCreate();
        // 创角时间大于等于RegisteredBefore（创角时刻小于此时间戳）则终止
        if(fillMail.getRegisteredBefore() != 0 && createTime >= fillMail.getRegisteredBefore()){
            return;
        }
        // 创角时间小于等于RegisteredAfter（后台配置的创角时刻大于此时间戳）则终止
        if(createTime <= fillMail.getRegisteredAfter()){
            return;
        }
        JSONObject jo = Utils.toJSONObject(fillMail.getParam());
        int mailSn = MailManager.MAIL_SN_COMMON;
        if(jo.containsKey("crossWarMailSn")){
            mailSn = jo.getIntValue("crossWarMailSn");
        }
        if(jo.containsKey("funcSn")){
            int funcSn = jo.getIntValue("funcSn");
            if(!humanObj.isModUnlock(funcSn)){
                return;
            }
        }
        // 玩家能否接收全服邮件
        if(!MailManager.canReceivedFillMail(humanObj.id, fillMail)){
            return;
        }
        long mailId = fillMail.getId();
        if(humanObj.operation.fillMailIdList.contains(mailId)){
            return;
        }

        String redisKey = Utils.createStr("{}{}", RedisKeys.humanIdFillMailIdList, humanObj.id);
        String fillMailIdJSON = Utils.getRedisStrValue(redisKey);
        List<Long> fillMailIdList = Utils.strToLongList(fillMailIdJSON);
        if(fillMailIdList.contains(mailId)){
            return;
        }
        humanObj.operation.fillMailIdList.add(mailId);
        //领取记录
        fillMailIdList.add(mailId);
        RedisTools.set(EntityManager.redisClient, redisKey, Utils.listToString(fillMailIdList));

        Param params = new Param();
        params.put(Mail.K.backstage, true);
        if(jo.containsKey("rank")){
            int rank = jo.getIntValue("rank");
            params.put("rank", rank);
        }

        if ("".equals(fillMail.getItemSn()) && "".equals(fillMail.getItemNum())) {
            MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, mailSn,
                    fillMail.getTitle(), fillMail.getContent(), "", params);
        } else {
            int[] sns = Util.strToIntArray(fillMail.getItemSn());
            int[] nums = Util.strToIntArray(fillMail.getItemNum());
            MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, mailSn,//fillMail.getId(),
                    fillMail.getTitle(), fillMail.getContent(), sns, nums, params);
        }


    }

    @DistrMethod
    public void humanLoginGameState(){
        Log.game.info("loginTrace[{}] login_step_1800=====玩家成功登录游戏！！！可以下发initData,humanId={}, account={}, name={}", humanObj.connPoint.servId,humanObj.id, humanObj.account, humanObj.name);
        HumanManager.inst().sendInitDataToClient(humanObj, true);
        //下一帧再去触发其他事件，先完成登录
        Port port = Port.getCurrent();
        port.addQueue(new PortPulseQueue() {
            @Override
            public void execute(Port port) {
                Event.fire(EventKey.HUMAN_LOGIN_FINISH, "humanObj", humanObj);
            }
        });
    }


    /** 
     * 统一给道具
     * <AUTHOR>
     * @Date 2024/2/28
     * @Param 
     */
    @DistrMethod
    public void produceAdd(int sn, int num, MoneyItemLogKey log, Object... obj) {
        ProduceManager.inst().produceAdd(humanObj, sn, num, log, obj);
    }
    
    /** 
     * 统一给道具 
     * <AUTHOR>
     * @Date 2024/2/28
     * @Param 
     */
    @DistrMethod
    public void produceAdd(Map<Integer, Integer> itemNumMap, MoneyItemLogKey log, Object... obj) {
        ProduceManager.inst().produceAdd(humanObj, itemNumMap, log, obj);
    }


    @DistrMethod
    public void leaveGuild(long guildId){
        GuildManager.inst().leaveGuild(humanObj, guildId);
    }

    @DistrMethod
    public void updateGuildInfo(Guild guild) {
        GuildManager.inst().updateHumanGuildInfo(humanObj, guild);
    }


    @DistrMethod
    public void denyApply(String guildName){
        Inform.sendMsg_error_move_str(humanObj, 503, guildName);
    }

    /**
     * 添加好友申请
     * @param friendId
     */
    @DistrMethod
    public void addFriendApply(long friendId) {
        Friend friend = humanObj.operation.friend;
        ReasonResult result = FriendManager.inst().addFriendApply(friend, friendId);
        if(result.success){
            FriendManager.inst().sendFriendUpdate(humanObj, Collections.singletonList(friendId), FriendType.UPDATE);
        }
        port.returns("result",result);
    }

    /**
     * 删除好友
     * @param friendId
     */
    @DistrMethod
    public void delFriend(long friendId) {
        Friend friend = humanObj.operation.friend;
        ReasonResult result = FriendManager.inst().delFriend(friend, friendId);
        if((int)result.param.get() == 2){
            FriendManager.inst().sendFriendUpdate(humanObj, Collections.singletonList(friendId), FriendType.DEL);
            ActivityManager.inst().delFriend(humanObj, friendId);
        }
        port.returns("result",result);
    }

    /**
     * 处理好友同意,拒绝
     * @param friendId
     */
    @DistrMethod
    public void dealFriendApply(long friendId, long dealType){
        Friend friend = humanObj.operation.friend;
        ReasonResult result = FriendManager.inst().dealFriendApply(friend, friendId, dealType);
        if(result.success == true && dealType == FriendType.AGREE){
            // 通知对方
            ActivityManager.inst().addFriend(humanObj, friendId);
           FriendManager.inst().sendFriendUpdate(humanObj, Collections.singletonList(friendId), FriendType.ADD);
        }else {
            FriendManager.inst().sendFriendUpdate(humanObj, Collections.singletonList(friendId), FriendType.UPDATE, FriendType.CAN_ADD);
        }
        port.returns("result",result);
    }

    /**
     * 好友赠送
     */
    @DistrMethod
    public void giveGift(long friendId) {
        Friend friend = humanObj.operation.friend;
        Map<Long, FavorabilityVo> favorabilityVoMap = FavorabilityVo.fromMapJsonString(friend.getFavorabilityMap());
        FavorabilityVo favorabilityVo = favorabilityVoMap.getOrDefault(friendId, new FavorabilityVo());
        favorabilityVo.dailyGiftTpe = FriendType.GIFT_GET;
        favorabilityVo.intimacy += ConfGlobal.get("friend_gift_favorability").value;
        favorabilityVoMap.put(friendId, favorabilityVo);
        friend.setFavorabilityMap(FavorabilityVo.toMapJsonString(favorabilityVoMap));
        MsgFriend.friend_give_gift_s2c.Builder msg = MsgFriend.friend_give_gift_s2c.newBuilder();
        Define.p_key_value.Builder keyValue = Define.p_key_value.newBuilder();
        keyValue.setV(friendId);
        keyValue.setK(FriendType.GIFT_GET);
        msg.setFriendGiftState(keyValue);
        humanObj.sendMsg(msg);
        FriendManager.inst().sendFriendGiftNum(humanObj,favorabilityVoMap);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_106);
    }

    /**
     * 帮助施肥对方在线
     * @param fromName 对方玩家
     * @param fromHumanId 帮助者ID
     * @param landVo 农田ID
     */
    @DistrMethod
    public void helpFertilize(long fromHumanId, String fromName, LandVo landVo, int num, int totalAccTime) {
        Farm2 farm2 = humanObj.farmData.farm2;
        Define.p_farm_land.Builder pLand = landVo.build(fromHumanId);
        MsgHome.home_farm_fertilize_s2c.Builder msg = MsgHome.home_farm_fertilize_s2c.newBuilder();
        msg.setCode(0);
        msg.setRoleId(humanObj.id);
        msg.setNewLand(pLand);
        humanObj.sendMsg(msg);
        Define.p_lang_info.Builder content = Define.p_lang_info.newBuilder();
        content.setId(EHomeType.EVENT_BE_HELP);
        content.addArgList(Define.p_key_value_name.newBuilder().setK(3).setV(0).setName(fromName));
        content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(num).setName(""));
        content.addArgList(Define.p_key_value_name.newBuilder().setK(0).setV(totalAccTime/60).setName(""));
        HomeManager.inst().addFarmLog(farm2,fromHumanId,EHomeType.EVENT_BE_HELP,content,fromHumanId);//被别人帮助施肥
    }

    /**
     * 被偷完成
     * @param farmVo farmVo
     */
    @DistrMethod
    public void stolenCompleted(FarmVo farmVo, int landId, RobberVo robberVo) {
        HomeManager.inst().stolenCompleted(humanObj, farmVo, landId, robberVo);
    }

    /**
     * 偷菜
     */
    @DistrMethod
    public void stealCompleted(FarmVo farmVo, int landId) {
        HomeManager.inst().stealCompleted(humanObj, farmVo, landId);
    }

    @DistrMethod
    public void updateBuildPorpCalc(){
        HomeManager.inst().updateBuildPorpCalc(humanObj);
    }

    /**
     * 失败偷菜
     */
    @DistrMethod
    public void stealFaild(long farmId, LandVo landVo) {
        HomeManager.inst().stealFaild(humanObj, farmId, landVo);
    }

    @DistrMethod
    public void sendFarmInfo(int reson){
        HomeManager.inst().handleFarmInfoC2S(humanObj,0, reson);
    }

    @DistrMethod
    public void freeSlave(long slaveId){
        Capture capture = humanObj.operation.capture;
        if(capture == null){
            Log.game.error("释放奴隶找不到捕获数据，humanId = {}, slaveId={}",humanObj.id, slaveId);
            return;
        }
        CaptureSlaveManager.inst().freeSlave(capture, slaveId);
    }

    @DistrMethod
    public void captureAddEnemy(long slaveId){
        Capture capture = humanObj.operation.capture;
        if(capture == null){
            return;
        }
        CaptureSlaveManager.inst().addEnemy(capture, slaveId);
    }

    @DistrMethod(argsImmutable = true)
    public void receiveCarParkReward(Map<Integer,Integer> reward, boolean isUpgraded, Map<Integer, MountVo> mountMap, Define.p_car_park_record p_record, boolean refresh){
        CarParkManager.inst().receiveCarParkReward(humanObj, reward, isUpgraded, mountMap, p_record, refresh);
    }

    @DistrMethod
    public void addCarParkIncome(int income){
        CarParkManager.inst().addCarParkIncome(humanObj, income);
    }

    @DistrMethod
    public void help(int type, int tagetId) {
        GuildManager.inst().help(humanObj, type, tagetId);
    }

    @DistrMethod
    public void checkAndConsume(Map<Integer, Integer> costItemMap, MoneyItemLogKey logKey) {
        int[][] costItems = new int[costItemMap.size()][];
        int i = 0;
        for (Map.Entry<Integer, Integer> entry : costItemMap.entrySet()) {
            costItems[i] = new int[] {
                    entry.getKey(),
                    entry.getValue()
            };
            i++;
        }
        ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, costItems, logKey);
        port.returns("result", rr);
    }

    @DistrMethod
    public void openActivity(List<ActivityVo> activityVoList){

        for(ActivityVo activityVo : activityVoList){
            if(!humanObj.openActivitySnList.contains(activityVo.activitySn)){
                humanObj.openActivitySnList.add(activityVo.activitySn);
                Event.fire(EventKey.ACTIVITY_OPEN,  "humanObj", humanObj, "activityVo", activityVo);
                Log.temp.info("=====开启活动, account={}, activitySn={}, mySnList={}",
                        humanObj.account, activityVo.activitySn, humanObj.openActivitySnList);
            }
        }
        activityVoList.clear();
    }

    @DistrMethod
    public void closeActivity(List<Integer> activityIdList){
        for (Integer activitySn : activityIdList) {
            humanObj.openActivitySnList.remove(activitySn);
            ConfActivityControl confActivityControl = ConfActivityControl.get(activitySn);
            ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(confActivityControl.type);
            if (data == null) {
                continue;
            }
            // 活动期数一定要在onActivityClose之前取出，不然执行完数据都没了
            int round = data.getActControlData().getRound();
            ActivityManager.inst().onActivityClose(humanObj, activitySn);

            if(confActivityControl.showTime > 0){
                humanObj.showActivitySnList.add(activitySn);
            }else {
                ActivityManager.inst().onActivityEndShow(humanObj, activitySn);
                Event.fire(EventKey.ACTIVITY_END_SHOW, "humanObj", humanObj, "activitySn", activitySn, "round", round);
            }
        }
    }

    @DistrMethod
    public void endShowActivity(List<Integer> endShowList) {
        for (Integer activitySn : endShowList) {
            ConfActivityControl confActivityControl = ConfActivityControl.get(activitySn);
            ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(confActivityControl.type);
            if (data == null) {
                continue;
            }
            // 活动期数一定要在onActivityClose之前取出，不然执行完数据都没了
            int round = data.getActControlData().getRound();

            if(humanObj.openActivitySnList.contains(activitySn)){
                humanObj.openActivitySnList.remove(activitySn);
                Log.game.error("活动结束展示期的活动在开启当中, id={}, activitySn={}, mySnList={}",
                        humanObj.id, activitySn, humanObj.openActivitySnList);
                ActivityManager.inst().onActivityClose(humanObj, activitySn);
            }
            if(humanObj.showActivitySnList.contains(activitySn)){
                humanObj.showActivitySnList.remove(activitySn);
            }

            ActivityManager.inst().onActivityEndShow(humanObj, activitySn);
            Event.fire(EventKey.ACTIVITY_END_SHOW, "humanObj", humanObj, "activitySn", activitySn, "round", round);
        }
    }

    @DistrMethod
    public void chat2Msg(long sendHumanId){
        InformManager.inst()._msg_chat_friend_info_list_c2s(humanObj, Collections.singletonList(sendHumanId));
    }

    @DistrMethod
    public void sendChatMsg(Param param){
        long sendHumanId = param.getLong("sendHumanId");
        int channel = param.getInt("channel");
        long targetId = param.get("targetId");
        Define.p_chat chatInfo = param.get("chatInfo");
        if(chatInfo == null){
            Log.temp.info("ignore chatMsg. chatInfo=null humanId={} sendHumanId={}", humanObj.id, sendHumanId);
            return;
        }
        if(chatInfo.getType() == Inform.ContentTypeLink){
            for(Define.p_link p : chatInfo.getLinksList()){
                if(p.getType() == Inform.MakeTeam){
                    Map<Integer, Long> settingMap = Utils.jsonToMapIntLong(humanObj.getHumanExtInfo().getSettingMap());
                    long isValue = settingMap.getOrDefault(10003, 0L); // Setting表配置
                    if(isValue == 1){
                        Log.temp.info("humanId={}, 设置了无法被邀请组队本， tagerId={}", humanObj.id, sendHumanId);
                        return;
                    }
                }
            }
        }

        if(channel == Inform.私聊){
            MsgChat.chat_message_s2c.Builder msg = MsgChat.chat_message_s2c.newBuilder();
            msg.setChannel(channel);
            msg.setTargetId(targetId);
            // 发给自己的私聊
            if(humanObj.id == sendHumanId){
                HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
                // 我是否被对方拉黑
                proxy.isInBlockList(targetId, sendHumanId);
                proxy.listenResult((results, context) -> {
                    boolean isBlock = Utils.getParamValue(results, "isBlock", false);
                    Define.p_chat.Builder chatBuilder = Define.p_chat.newBuilder(chatInfo);
                    chatBuilder.setIsBlock(isBlock ? 1 : 0);
                    msg.setChatInfo(chatBuilder);
                    humanObj.sendMsg(msg);
                });
                return;
            }
            // 发给对方的私聊
            // 更新_msg_chat_friend_info_list_c2s
            chat2Msg(sendHumanId);
        }
        List<Long> blockIdList = Utils.strToLongList(humanObj.operation.friend.getBlackList());
        boolean isBlock = blockIdList.contains(sendHumanId);
        if(isBlock){
            Log.temp.info("ignore chatMsg. channel={} blockIdList of humanId={} contains sendHumanId={}", channel, humanObj.id, sendHumanId);
            return;
        }
        MsgChat.chat_message_s2c.Builder msg = MsgChat.chat_message_s2c.newBuilder();
        msg.setChannel(channel);
        msg.setTargetId(sendHumanId);
        msg.setChatInfo(chatInfo);
        humanObj.sendMsg(msg);
    }

    @DistrMethod
    public void isInBlockList(long targetId){
        List<Long> blockIdList = Utils.strToLongList(humanObj.operation.friend.getBlackList());
        boolean isBlock = blockIdList.contains(targetId);
        port.returns("result", isBlock);
    }

    @DistrMethod
    public void sendMsgParam(Param param){
        int msgId = param.get("msgId");
        switch (msgId){
            case MsgIds.dungeon_league_solo_info_s2c:
                List< Define.p_league_solo_box_record> dInfoList = param.get("dInfoList");
                int boxNum = param.get("boxNum");
                int boxHighNum = param.get("boxHighNum");
                GuildManager.inst().sendMsg_dungeon_league_solo_info_s2c(humanObj, dInfoList, boxNum, boxHighNum);
                break;
            default:
                Log.temp.error("未实现代码， 未知的msgId={}", msgId);
                break;
        }
    }

    @DistrMethod
    public void guildGveJoin(int gveSn, int mailSn, long time) {
        long currTime = Port.getTime();
        if (Utils.isSameDay(currTime, time)) {
            // 同一天，就要检测
            HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.guildDailyGveJoin.getType());
            if (info.getValue() == 0) {
                info.setValue(1);
                humanObj.saveDailyResetRecord();
                GuildManager.inst().sendGveJoinMail(humanObj.id, gveSn, mailSn);
            }
        } else {
            // 不是同一天，不检测并且不设置每日重置相关
            GuildManager.inst().sendGveJoinMail(humanObj.id, gveSn, mailSn);
        }
    }


    @DistrMethod
    public void sendGveKillMail(int gveSn, int mailSn){
        GuildManager.inst().sendGuildGveKill(humanObj, gveSn, mailSn);//10143
    }

    /**
     * 通知公会充值活动信息更新
     */
    @DistrMethod
    public void notifyGuildPayInfo(int actType, int payCount) {
        ActivityControlGuildPay control = (ActivityControlGuildPay) ActivityControlTypeFactory.getTypeData(actType);
        if (control != null) {
            control.notifyGuildPayInfo(humanObj, payCount);
        }
    }

    @DistrMethod
    public void gveSettle(int gveSn) {
        Log.temp.info("[{}] gveSettle gveSn={}", humanObj.id, gveSn);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_回归任务, TaskConditionTypeKey.TASK_TYPE_11, InstanceConstants.LEAGUEGVECHAPTER_7, 1);
    }

    @DistrMethod
    public void payGiftNew(PayGift payGiftOld, String json){
        boolean result = MallManager.inst().payGiftNew(humanObj, json);
        port.returns("result", result, "reason", result?"礼包已经记录成功":"充值礼包记录失败");
    }

    @DistrMethod
    public void payGift(String json){
        JSONObject jo = Utils.toJSONObject(json);
        String payId = jo.getString("propId");
        if(!humanObj.payGiftMap.containsKey(payId)){
            Log.temp.error("充值失败！===payId={}, 礼包不存在. humanId={}, payIdKey={}, json={}", payId, humanObj.id, humanObj.payGiftMap.keySet(), json);
            port.returns("result", false, "reason", "充值礼包不存在", "deliverCode", PlaceOurpalm.STATUS_RECEIVE_101);
            return;
        }
        // 订单id
        String orderId = jo.getString("orderId");
        // 判断内存中是否已经充值过了，避免重复
        for (PayLog log : humanObj.dataPers.payLogs) {
            if (log.getOrderId().equals(orderId)) {
                Log.temp.error( "充值失败！充值礼包订单已经充值, orderId={}, payId={}, humanId={}", orderId, payId, humanObj.id);
                port.returns("result", false, "reason", "充值礼包订单已经充值", "deliverCode", PlaceOurpalm.STATUS_RECEIVE_100);
                return;
            }
        }
        PayGift payGift = humanObj.payGiftMap.get(payId);
        if(payGift == null){
            Log.temp.error("充值失败！===充值礼包payId={}, 数据已经清空或礼包不存在.json={}", payId, json);
            port.returns("result", false, "reason", "充值礼包不存在", "deliverCode", PlaceOurpalm.STATUS_RECEIVE_101);
            return;
        }

        boolean isPurchaseLimitAmount = MallManager.inst().payGiftGiveReward(humanObj, payGift, jo);
        if(isPurchaseLimitAmount){
            Log.temp.error( "充值失败！超出充值次数限制,转钻石, orderId={}, payId={}, humanId={}", orderId, payId, humanObj.id);
            port.returns("result", true, "reason", Utils.createStr("超出充值次数限制,转钻石{}", payGift.getAmountToDiamond()),
                    "deliverCode", PlaceOurpalm.STATUS_RECEIVE_103);
            return;
        }
        port.returns("result", true, "reason", "充值成功");

    }

    @DistrMethod
    public void updateGuildPosition(int guildPosition){
        humanObj.guildPosition = guildPosition;
    }

    @DistrMethod
    public void sendGuildTreasureBoxInfo() {
        GuildManager.inst()._msg_guild_treasure_info_c2s(humanObj);
    }

    /**
     * 跨服战击杀玩家
     * @param isInvade
     */
    @DistrMethod
    public void crossWarKillPlayer(boolean isInvade) {
        // 跨服战击杀敌人
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_活动, TaskConditionTypeKey.TASK_TYPE_42, ActivityControlType.Act_33, 1, 1);
        if(!isInvade){
            // 防守战前10次获得奖励
            HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyCrossWarDefKillPlayer.getType());
            if(info.getValue() < 10){
                Map<Integer, Integer> rewardMap = new HashMap<>();
                ConfCrossWarKv confKv = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_106);
                if(confKv != null){
                    rewardMap = Utils.intArrToIntMap(confKv.value);
                }
                ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.跨服战防守击杀玩家奖励);
                CrossWarManager.inst().sendMsg_crossWar_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewardMap);
            }
            else{
                Inform.sendMsg_error(humanObj, ErrorTip.CrossWarKillPlayerRewardIsFull);
            }
            info.setValue(info.getValue() + 1);
            humanObj.saveDailyResetRecord();
        }
    }

    /**
     * 跨服战击杀怪物
     * @param isInvade
     */
    @DistrMethod
    public void crossWarKillMonster(boolean isInvade) {
        // 跨服战击杀怪物
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_活动, TaskConditionTypeKey.TASK_TYPE_42, ActivityControlType.Act_33, 2, 1);
        if(!isInvade){
            // 防守战前10次获得奖励
            HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyCrossWarDefKillMonster.getType());
            if(info.getValue() < 10){
                Map<Integer, Integer> rewardMap = new HashMap<>();
                ConfCrossWarKv confKv = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_105);
                if(confKv != null){
                    rewardMap = Utils.intArrToIntMap(confKv.value);
                }
                ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.跨服战防守击杀怪物奖励);
                CrossWarManager.inst().sendMsg_crossWar_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewardMap);
            }
            else{
                Inform.sendMsg_error(humanObj, ErrorTip.CrossWarKillMonsterRewardIsFull);
            }
            info.setValue(info.getValue() + 1);
            humanObj.saveDailyResetRecord();
        }
    }

    /**
     * 跨服战单次入侵结束
     * @param score
     */
    @DistrMethod
    public void crossWarInvadeEnd(int score) {
        // 跨服战个人最高积分
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_活动, TaskConditionTypeKey.TASK_TYPE_41, ActivityControlType.Act_33, score);
    }

    /**
     * 跨服战结算
     */
    @DistrMethod
    public void onCrossWarEnd() {
        IActivityControl control = ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_33);
        if(control == null) {
            return;
        }
        control.sendActivityData(humanObj);
    }

    /**
     * 武道会发放竞猜币
     * @param betNum
     */
    @DistrMethod
    public void kungFuRaceAddBetCoin(int betNum) {
        int[] rewards = new int[]{KungFuRaceConst.KUNG_FU_RACE_BET_COIN_ID, betNum};
        ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.武道会竞猜获得);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewards);
    }

    /**
     * 武道会回收竞猜币
     */
    @DistrMethod
    public void kungFuRaceRecycleBetCoin() {
        KungFuRaceManager.inst().recycleBetCoin(humanObj);
    }

    /**
     * 武道会积分达到X分
     * @param newScore
     */
    @DistrMethod
    public void kungFuRaceBattleNewScore(int newScore) {
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_武道会任务, TaskConditionTypeKey.TASK_TYPE_99, newScore);
    }

    @DistrMethod
    public void triggerMsgIdOperate(int msgId){
        switch (msgId){
            case MsgIds.guild_apply_list_c2s:
                GuildManager.inst()._msg_guild_apply_list_c2s(humanObj);
                break;
            case MsgIds.gvg_info_c2s:
                GuildManager.inst().sendMsg_gvg_info_s2c(humanObj);
                break;
            default:
            Log.temp.error("未实现代码， 未知的msgId={}", msgId);
            break;
        }
    }

    @DistrMethod
    public void removeItem(int itemSn, int num) {
        ProduceManager.inst().costItem(humanObj, itemSn, num, MoneyItemLogKey.GM后台操作);
    }

    @DistrMethod
    public void removeMoney(int itemSn, int num) {
        MoneyManager.inst().produceMoneyReduce(humanObj, itemSn, num, MoneyItemLogKey.GM后台操作);
    }

    @DistrMethod
    public void receiveFlower(long roleId, int actType, int flowerSn, int num) {
        ActivityManager.inst().receiveFlower(humanObj, actType, roleId, flowerSn, num);
    }

    @DistrMethod
    public void getPBattleRoleMsg(boolean containBattleRole) {
        Define.p_battle_role msg;
        if (containBattleRole) {
            HumanData humanData = HumanData.getHumanData(humanObj);
            TeamMember member = new TeamMember(humanData);
            msg = member.p_battle;
        } else {
            msg = HumanManager.inst().to_p_battle_role(humanObj.getHuman(), humanObj.getHuman2());
        }
        port.returns("p_battle_role", msg);
    }

    @DistrMethod
    public void gmCallHumanServiceMethod(Param param){
        String type = param.getString("type");
        boolean succ = false;
        int value = 0;
        switch (type){
            case "dailyActRefresh":
                value = param.getInt("actType");
                succ = ActivityManager.inst().dailyResetActivityData(humanObj,value);
                break;
            default:
                break;
        }
        if(succ){
            Log.game.error("gm操作成功，type={}，value={}, humanId={}，name={}", type, value, humanObj.id,humanObj.getHuman().getName());
        }else {
            Log.game.error("gm操作失败，type={}，humanId={}，name={}", type, value, humanObj.id,humanObj.getHuman().getName());
        }
    }

    /**
     * 趣味竞答获得家族内前三名
     */
    @DistrMethod
    public void getTop3InGuildQuestionInnerRank(){
        // 触发成就《趣味竞答获得家族内前三名》的检查
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_109, 1);
    }

    /**
     * 家族乱斗击杀敌人
     */
    @DistrMethod
    public void gvgBattleKillEnemy(int killNum){
        // 战斗结算，触发成就《家族乱斗累计战胜X名对手》的检查
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_110, killNum);
    }

    /**
     * 更新飞宠借用信息
     */
    @DistrMethod
    public void updateFlyPetBorrowHybrid(long petId) {
        FlyPetManager.inst().updateFlyPetBorrowHybrid(humanObj, petId);
    }
    @DistrMethod
    public void updateDoubleChapterHelpList(Define.p_double_chapter_help_player help, boolean b) {
        DoubleChapterManager.inst().updateHelpList(humanObj.operation.doubleChapter, help, b);
    }

    /**
     * 获取飞宠信息
     */
    @DistrMethod
    public void getFlyPet(long petId) {
        port.returns(humanObj.operation.flyPetMap.get(petId));
    }

    /**
     * 飞宠搭档变动
     *
     * @param opt 0:删除，1：添加
     */
    @DistrMethod
    public void updateFlyHybridPartner(long partnerId, int opt) {
        FlyPetManager.inst().updateFlyHybridPartner(humanObj, partnerId, opt);
    }

    @DistrMethod
    public void addFlyHybridAsk(int type, long partnerId, long petId) {
        FlyPetManager.inst().addFlyHybridAsk(humanObj, type, partnerId, petId);
    }
    @DistrMethod(argsImmutable = true)
    public void addDoubleChapterTeammate(Define.p_double_chapter_teammate inviterTeammate, Define.p_double_chapter_teammate_detail teammateDetail, Define.p_battle_role battleRole) {
        DoubleChapterManager.inst().addDoubleChapterTeammate(humanObj, inviterTeammate, teammateDetail, battleRole);
        port.returns("maxChapter", humanObj.operation.doubleChapter.getChapter());
    }

    @DistrMethod(argsImmutable = true)
    public void handleInviteShare(Define.p_double_chapter_help_player applyInfo) {
        DoubleChapterManager.inst().handleInviteShare(humanObj,applyInfo);
    }
    @DistrMethod
    public void checkAndUpdateTeammate(long humanId) {
        DoubleChapterManager.inst().checkAndUpdateTeammate(humanObj, humanId);
    }

    @DistrMethod
    public void getHumanBrief(long humanId) {
        HumanBrief humanBrief = humanObj.operation.getBrief();
        port.returns("humanBrief", humanBrief);
    }

    // =============================TODO  下面是出bug时写容错代码，业务逻辑请写上面。业务逻辑结束

    @DistrMethod
    public void update1(String json){

    }
    @DistrMethod
    public void update2(Object... objs){

    }

    @DistrMethod
    public void update3(Param param){

    }

    @DistrMethod
    public void update4(String json){

    }
}
