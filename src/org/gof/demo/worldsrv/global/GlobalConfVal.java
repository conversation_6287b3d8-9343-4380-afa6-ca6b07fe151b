package org.gof.demo.worldsrv.global;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.gof.core.Port;
import org.gof.core.support.*;
import org.gof.core.utils.StrUtil;
import org.gof.demo.distr.cross.domain.CrossGroupInfo;
import org.gof.demo.distr.world.srvmerge.ServerMerge;
import org.gof.demo.support.Symbol;
import org.gof.demo.support.TimeUtil;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.crossWar.CrossWarConst;
import org.gof.demo.worldsrv.guild.league.GuildLeagueUtils;
import org.gof.demo.worldsrv.instance.ConfChapterVO;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.mall.MallManager;
import org.gof.demo.worldsrv.mall.PayMallTypeKey;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.singleton.ParamManager;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.task.type.TaskTypeData;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

public class GlobalConfVal {

    // 属性sn，属性key
    public static Map<Integer, String> propSnStringMap = new HashMap<>();
    // 属性key名字, 属性sn
    public static Map<String, Integer> propNameSnMap = new HashMap<>();
    // 类型1的名字，属性sn
    public static Map<String, Integer> propNameSnModule1Map = new HashMap<>();

    // 属性sn,战力系数
    public static Map<Integer, Float> propSnCombatMap = new HashMap<>();
    // 属性sn,品质系数
    public static Map<Integer, Float> propSnQualityCombatMap = new HashMap<>();
    //
    public static Map<Integer, TaskTypeData> taskConditionTypeMap = new HashMap<>();

    public static String abcs[] = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K"
            , "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

    // 默认主地图sn
    public static final int mainSn = -1;
    // 默认主地图最大人数
    public static final int mainSnMaxNum = 500;
    // 初始主线任务
    public static final int initMainTask = 1;

    public static final int maxdiff = 1000;

    // 副本表最大值
    public static int repMaxValue = 1;
    // 怪物最大值
    public static int mainUnitMaxValue = 1;
    // 聊天cd冷却
    public static final int sendMsgCD = (int) (2 * Time.SEC);


    public static final int equipMaxPart = 10;      //装备10个部位

    public static int MAX_LEVEL = 80;
    public static String defaultNameHead = "player";

    //职业，map<等级段，装备列表>
    private static Map<Integer, Map<RangeInt, Map<Integer, Integer>>> confEquipGradeSnMap = new HashMap<>();
    private static Map<Integer, List<ConfEquipmentAttr>> confEquipGroupAttrListMap = new HashMap<>();
    //活动Id,map<等级段，装备列表>
    private static Map<Integer, Map<RangeInt, Map<Integer, Integer>>> activityEquipmentMap = new HashMap<>();
    //品质，对应的雕像属性列表
    private static Map<Integer, List<ConfStatueAttr>> statueQualityMap = new HashMap<>();
    // 物品sn,对应保存字段类型
    public static Map<Integer, Integer> itemSnClassifyMap = new HashMap<>();

    public static Set<Integer> itemSnSet = new HashSet<>();
    // 技能sn,最大等级
    public static Map<Integer, Integer> skillSnMaxLvMap = new HashMap<>();
    public static Map<Integer, Integer> skillSnItemMap = new HashMap<>();
    // Map<技能品质, 满级合成时对应的掉落>
    public static Map<Integer, Integer> skillComposeOutputMap = new HashMap<>();

    // 伙伴sn,最大等级
    public static Map<Integer, Integer> petSnMaxLvMap = new HashMap<>();
    public static Map<Integer, Integer> petSnItemMap = new HashMap<>();
    // Map<同伴品质, 满级合成时对应的掉落>
    public static Map<Integer, Integer> petComposeOutputMap = new HashMap<>();

    public static Map<Integer, Integer> itemSnEmojiSnMap = new HashMap<>();

    public static Map<Integer, Integer> skillSnLvIllustratedMap = new HashMap<>();
    public static Map<Integer, Integer> petSnLvIllustratedMap = new HashMap<>();
    // 副本类型， 最大sn
    public static Map<Integer, Integer> repTypeMaxSnMap = new HashMap<>();
    // 副本类型， 最大小
    public static Map<Integer, Integer> repTypeMinSnMap = new HashMap<>();

    public static Map<Integer, Map<Integer, Integer>> repTypeLevelSnMap = new HashMap<>();


    public static Map<Integer, Map<Integer,Integer>> darkTrialLevelTypeMap = new HashMap<>();
    // Map<副本类型(part_type), Map<副本难度(diffcult), 最大星级的副本sn>>
    public static Map<Integer, Map<Integer, Integer>> darkTrialTypeDiffSnMap = new HashMap<>();

    // 副本类型，对应消耗
    public static Map<Integer, int[]> repTypeCostMap = new HashMap<>();
    // 副本类型，对应回复次数
    public static Map<Integer, Integer> repTypeNumMap = new HashMap<>();

    // 主线副本sn最下值和最大值，副本信息
    public static Map<Integer, ConfChapterVO> chapterVoMap = new HashMap<>();

    public static Map<Long, Map<Integer, Integer>> monsterKillRewardMap = new HashedMap();
    public static Map<Long, Map<Integer, Integer>> monsterKillOneRewardMap = new HashedMap();
    // 类型，充值配置，正序
    public static Map<Integer, List<ConfPayMall>> typePayMallMap = new HashMap<>();
    // 类型|组ID，配置，正序
    public static Map<String, List<ConfMountDraw>> confMountDrawMap = new HashMap<>();
    // 类型|组ID，配置，正序
    public static Map<String, List<ConfMountDrawCumulativeTimes>> confMountDrawCumulativeTimesMap = new HashMap<>();
    // 类型|组ID|num，配置
    public static Map<String, ConfMountDrawGuaranteed> confMountDrawGuaranteedMap = new HashMap<>();
    // 类型|组ID，配置，正序
    public static Map<Integer, List<ConfDoubleProbabillity>> confDoubleProbabillityMap = new HashMap<>();
    // 类型|组ID，配置，正序
    public static Map<String, List<ConfBoxTowerLevel>> confBoxTowerLevelMap = new HashMap<>();
    // 类型|组ID|品质，配置
    public static Map<String, ConfBoxTowerBox> confBoxTowerBoxMap = new HashMap<>();
    //类型，配置
    public static Map<Integer, List<ConfActivityTerm>> confActivityTermMap = new HashMap<>();
    public static Map<Integer, List<ConfEquipmentAttr>> ConfEquipmentAttrMap = new HashMap<>();

    public static List<Integer> mountSnList = new ArrayList<>();

    public static List<Integer> wingSnList = new ArrayList<>();

    public static List<Integer> artifactSkinList = new ArrayList<>();
    public static int guildFamilySignupNum = 20;

    public static Map<Integer, Integer> worldBossSnMap = new HashMap<>();

    // serverId对应几服
    public static Map<Integer, Integer> serverIdNumMap = new HashMap<>();
    // 几服对应serverId
    public static Map<Integer, Integer> numServerIdMap = new HashMap<>();
    // serverId对应几区
    public static Map<Integer, Integer> serverIdZoneMap = new HashMap<>();
    // 区，List<serverId>（正序）
    public static Map<Integer, List<Integer>> zoneServerIdListMap = new HashMap<>();
    // Map<活动类型，List<充值表sn>>
    public static Map<Integer, List<Integer>> actTypePayMallSnMap = new HashMap<>();
    // Map<充值表sn，条件数组> 活动相关充值表和对应活动条件数组
    public static Map<Integer, int[]> actPayMallConditionMap = new HashMap<>();

    public static int arenaDay = 7;
    public static int arenaHistoryNum = 30;     // 竞技场记录条数
    public static int historySec = Time.DAY_3_SEC; // 战斗记录过期时间

    public static int arenaBridgeHistoryNum = 30; // 竞技场排位赛记录条数
    public static Map<Integer, Integer> areanWinNumSnMap = new HashMap<>();
    public static Map<Integer, Integer> areanSnRankMap = new HashMap<>();

    public static Map<Integer, Integer> areanGradeTopMap = new HashMap<>();
    public static Map<Integer, Integer> areanGradeNextMap = new HashMap<>();
    public static Map<Integer, RangeInt> areanGradeRankRangeMap = new HashMap<>();


    public static Map<Integer, Map<Integer, RangeInt>> leagueRankTypeSnRangeMap = new HashMap<>();
    public static Map<Integer, Integer> leagueRankTypeMaxNumMap = new HashMap<>();

    public static Map<Integer, Map<Integer, RangeInt>> crossRankTypeSnRangeMap = new HashMap<>();


    public static Map<Integer, Integer> guildKillNumBuffSnMap = new HashMap<>();

    private static List<Integer> activityType4List = new ArrayList<>();

    // BreakGoldEggLayers_砸金蛋数量. Map<活动类型，Map<活动组Id, Map<活动层数， confSn>>
    public static Map<Integer, Map<Integer, Map<Integer, Integer>>> typeGroupLayerConfSnMap = new HashMap<>();
    // BreakGoldEggLayers_最大层数. Map<活动类型，Map<活动组Id, 最大层数>>
    public static Map<Integer, Map<Integer, Integer>> typeGroupMaxLayerMap = new HashMap<>();

    // 掉落组, 掉落sn
    public static Map<Integer, Integer> confOutputMap = new HashMap<>();
    private static Map<Integer, Map<Integer,String>> typeWeekConfSnMap = new HashMap<>();
    private static Map<Integer, Integer> moduleAttributeSnMap = new HashMap<>();

    private static Map<Integer, Map<Integer, List<Integer>>> confTypeGroupIdSnListMap = new HashMap<>();

    private static Map<Integer, List<Integer>> confTypePrivilegeCardSnListMap = new HashMap<>();

    private static List<Integer> captureSlaveEarningSnList = new ArrayList<>();

    private static Map<Integer, List<Integer>> typeMallSnListMap = new HashMap<>();

    private static List<Integer> skinTypeOneSnASCList = new ArrayList<>();

    private static Map<Integer, List<Integer>> typeSnASCListSnMap = new HashMap<>();

    private static Map<Integer, List<Integer>> confGroupGemattrSnList = new HashMap<>();

    private static  Map<Integer, Map<Integer, RangeInt>> pvpRewardSnRangeMap= new HashMap<>();
    private static Map<Integer, List<Integer>> pvpTypeASCSnListMap  = new HashMap<>();
    private static Map<Integer, Integer> pvpTypeMaxNumMap = new HashMap<>();

    // Map<副本sn,充值表列表>
    public static Map<Integer, List<ConfPayMall>> guardPayRepSnMallSnMap = new HashMap<>();
    public static Map<Integer, List<ConfPayMall>> dungeonPayRepSnMallSnMap = new HashMap<>();
    public static Map<String, ConfPayMall> iosPidConfPayMallMap = new HashMap<>();

    public static List<Integer> mountGoodsSnList = new ArrayList<>();
    public static List<Integer> artifactGoodsSnList = new ArrayList<>();
    public static List<Integer> wingGoodsSnList = new ArrayList<>();

    // Map<道具sn, List<26类型充值表sn>>这个是循环
    public static Map<Integer, List<Integer>> shortLoopPaySnMap = new HashMap<>();
    // Map<时装id_时装等级, List<26类型充值表sn>>
    public static Map<String, List<Integer>> fashionSkinLvPaySnMap = new HashMap<>();
    // Map<抽卡类型, List<26类型充值表sn>>这个是每日循环
    public static Map<Integer, List<Integer>> drawLoopPaySnMap = new HashMap<>();
    // Map<物品列表字符串, List<26类型充值表sn>>
    public static Map<String, List<Integer>> anyShortageItemsPaySnMap = new HashMap<>();
    // Map<条件字符串, List<26类型充值表sn>>
    public static Map<String, List<Integer>> commonConditionPaySnMap = new HashMap<>();
    // List<26类型充值表> 飞宠蛋不足时
    public static List<Integer> flyEggLoopPaySnList = new ArrayList<>();

    // Map<充值表sn, Condition分割后的字符串>
    public static Map<Integer, String[]> payConditionsMap = new HashMap<>();
    public static List<ConfParkingDesign_0> parkingDesignInitialList = new ArrayList<>();

    /**
     * Map<活动类型, Map<活动组, Map<天数, List<sn并且根据order排序>>>>
     */
    public static Map<Integer, Map<Integer, Map<Integer, List<Integer>>>> pvpClueMap = new HashMap<>();
    /**
     * Map<活动类型, Map<活动组, List<圣诞献礼buff表sn>>>
     */
    public static Map<Integer, Map<Integer, List<Integer>>> pvpClueBuffMap = new HashMap<>();

    public static int[] teamMatch;
    public static int[] teamMatchNum;
    public static int[] teamPro;

    public static int cross_server_id_base=39900;

    private static Map<Integer, CrossGroupInfo> crossGroupSnInfoMap = new LinkedHashMap<>();

    private static Map<Integer, ServerMerge> serverMergeMap = new LinkedHashMap<>();

    public static long crossServerIdTime = -1;
    public static int[] crossServerIdRange = new int[2];
	
	// 答题：骰子奖励
    public static Map<Integer, Map<Integer, Integer>> quizDiceRewardMap = new HashMap<>();
    // 答题：答题排行奖励
    public static Map<Integer, Map<Integer, Integer>> quizRankRewardMap = new HashMap<>();
    // 答题：答题排行前几名经验奖励
    public static Map<Integer, Integer> quizRankRewardExpMap = new HashMap<>();
    public static List<ConfParkingTime> parkingTimeList = new ArrayList<>();

    // Map<基金sn, List<基金奖励sn>>
    public static Map<Integer, List<Integer>> fundIdRewardLevelMap = new HashMap<>();

    // 跨服战职业属性加成 <jobId, <objType, Map<attrKey, value>>>
    public static Map<Integer, Map<Integer, Map<Long, Long>>> crossWarAttrBonusMap = new HashMap<>();
    // 跨服战打普通怪物获取的积分 <monsterId, 获得的分数>
    public static Map<Long, Integer> crossWarMonsterScoreMap = new HashMap<>();
    // 跨服战光明之子每打几分之几的血获得的积分 [分母，分子，获得的分数]
    public static int[] crossWarSonOfLightScoreInfo = new int[3];
    // 跨服战打玩家获得的积分 List<[最小等级， 最大等级， 获得的分数]>
    public static List<int[]> crossWarPlayerScoreList = new ArrayList<>();

    // Map<活动类型，Map<组id，Map<层，List<正序配置>>>>砸金蛋
    public static Map<Integer, Map<Integer, Map<Integer,List<ConfBreakGoldEggWeight>>>> confBreakGoldEggWeightMap = new HashMap<>();
    // Map<活动类型，Map<组id，Map<轮*10000+层，List<正序配置>>>>叠叠乐
    public static Map<Integer, Map<Integer, Map<Integer,List<ConfLoopBreakGoldEggWeight>>>> confLoopBreakGoldEggWeightMap = new HashMap<>();

    // Map<章节，最大关卡> 双人本最后一关关卡
    public static Map<Integer, Integer> confDoubleChapterMaxChapterMap = new HashMap<>();
    public static Map<String, ConfDoubleLadderChapter> confDoubleLadderChapterMap = new HashMap<>();
    private static int confMaxDoubleLadderChapter = 0;

    private static Map<Integer, Integer> confMonopolyBoardSizeMap = new HashMap<>();
    private static Set<Integer> confSlimeEffectItemSnSet = new HashSet<>();
    private static Set<Long> removeIdSet = new HashSet<>();

    /**
     * 重载配置表
     */
    public static void reloadConfVal() {
        cross_server_id_base = Integer.parseInt(Config.GAME_SERVER_PREFIX)+9900;
        MAX_LEVEL = ConfLevel.findBy(ConfLevel.K.sn, OrderBy.DESC).get(0).sn;
        // 全局表
        reloadGlobal();
        // 全局表部分数据
        ParamManager.getInstance().reload();
        // 装备表
        reloadEquipment();
        //装备属性表
        reloadConfEquipmentAttr();
        //雕像属性表
        reloadStatueAttr();
        // 属性表
        reloadAttribute();

        // 物品表
        reloadGoods();
        // Collections.unmodifiableList
        reloadTaskCondition();
        // 加载副本表
        reloadConfChapter();
        // 加载怪物表
        reloadConfMainUnit();

//        reloadConfUnit();
        reloadPetLv();
        reloadSkillLv();

        reloadWorldBossTotalDmg();

        reloadServerId();

        reloadArean();

        reloadLeagueData();
        reloadCross();
        reloadIllustrated();
        reloadTypePayMallMap();
        reloadConfMountDrawMap();
        reloadConfMountDrawGuaranteedMap();
        reloadConfMountDrawCumulativeTimesMap();
//        reloadConfMount();
//        reloadArtifact();
//        reloadConfBackLevel_0();
        reloadConfGuildBuff();
        reloadConfActivityTerm();
        reloadActivityControl();

        reloadBreakGoldEggLayers();
        reloadConfEquipmentAttr();

        GuildLeagueUtils.initStepTime();
        reloadConfOutput();

        reloadConfActivityAdjust();

        reloadConfActivityRankReward();
        reloadPrivilegeCard();

        reloadCaptureSlaveEarnings();
        reloadConfPayMall();
        reloadConfMall();
        reloadConfSkin();
        reloadArtifactGemattr();
        reloadConfFateDraw();
        reloadConfChristmasPvp();
        reloadParkingDesignInitialList();
        reloadConfBoxTowerLevel();
        reloadConfBoxTowerBox();

        reloadCrossGroups();
        reloadConfParkingTime();

        reloadConfFund();
        reloadCrossWar();
        reloadConfBreakGoldEggWeight();
        reloadConfLoopBreakGoldEggWeight();
        reloadConfFly();
        reloadDoubleChapterMaxLevel();
        reloadConfPetSkin();
        reloadMonopolyBoardSizeMap();
        reloadSummonerSkill();
        reloadSlimeEffectItemSet();

        reloadConfRefreshGift();
        reloadConfNews();
        reloadConfServerMerge();
        reloadConfDoubleProbabillityMap();
    }


    public static Map<Integer, CrossGroupInfo> getCrossGroupSnInfoMap() {
        return crossGroupSnInfoMap;
    }
    public static void reloadCrossGroups() {
        Collection<ConfServerActivityGroup> groups = ConfServerActivityGroup.findAll();
        String parseResult = doParse(groups);
        if(StrUtil.isNotEmpty(parseResult)){
            throw new SysException(parseResult);
        }
    }

    private static String doParse(Collection<ConfServerActivityGroup> groups) {
        long nowTime = Port.getTime();
        List<CrossGroupInfo> allInfos = new ArrayList<>();
        Map<Integer, CrossGroupInfo> infos = new LinkedHashMap<>();
        int serverIdBase = Integer.parseInt(Config.GAME_SERVER_PREFIX);
        for (ConfServerActivityGroup groupCfg : groups) {
            boolean parseAllocate = StrUtil.isNotEmpty(groupCfg.crossAllocate);
            String defaultCrossNodeId = D.NODE_CROSS_PREFIX+"0_"+(cross_server_id_base+1);
            CrossGroupInfo info = new CrossGroupInfo(groupCfg.sn, groupCfg.crossType + "_0");
            // 每一组的服务器ID范围
            try {
                //groupCfg.range 格式： 1403,1404,1405-1420|1401,1402
                String str1 = groupCfg.range.replaceAll("[a-zA-Z]","");
                String[] arr1 = str1.split(Symbol.SHUXIAN_REG);
                for (String arrStr : arr1) {
                    List<Integer> groupIds = StrUtil.parseRangeCfg(arrStr);
                    for(Integer groupId : groupIds) {
                        ConfServerGroup confSeverGroup = ConfServerGroup.get(groupId);
                        String severRange = confSeverGroup.sever_range;
                        List<Integer> serverIds = StrUtil.parseRangeCfg(severRange);
                        for (int serverId : serverIds) {
                            Integer oldGroupId = info.serverAtGroups.put(serverId+serverIdBase, groupId);
                            if (oldGroupId != null && oldGroupId > 0) {
                                return "SN:" + groupCfg.sn + " 服务器ID[" + serverId + "]在不同分组出现: " + oldGroupId + "," + groupId;
                            }
                        }
                        info.groupAllServers.put(groupId, serverIds);
                        info.groupAtServerStrs.put(groupId, severRange);
                        if(!parseAllocate){
                            info.groupAtCrossNodes.put(groupId, defaultCrossNodeId);
                        }
                    }
                }
            } catch (Exception e) {
                Log.game.error("解析跨服分组配置出错，ex="+e, e);
                return "sn[" + groupCfg.sn + "] 解析区域范围时出现错误 [" + groupCfg.range + "], err:" + e.getMessage();
            }

            // 每一跨服节点的组范围
            if(parseAllocate) {
                try {
                    //groupCfg.crossAllocate格式：c1:1001-1002
                    String str2 = groupCfg.crossAllocate.replaceAll("[a-zA-Z]", "");
                    String[] arr2 = str2.split(Symbol.SHUXIAN_REG);
                    for (String arrStr : arr2) {
                        String[] arr21 = arrStr.split(Symbol.MAOHAO_REG);
                        String crossNodeId = D.NODE_CROSS_PREFIX+"0_"+(cross_server_id_base+Integer.parseInt(arr21[0]));
                        List<Integer> groupIds = StrUtil.parseRangeCfg(arr21[1]);
                        for (int groupId : groupIds) {
                            String oldCrossNodeId = info.groupAtCrossNodes.put(groupId, crossNodeId);
                            if (StrUtil.isNotEmpty(oldCrossNodeId)) {
                                return "sn[" + groupCfg.sn + "] 分组ID[" + groupId + "]在不同节点出现: " + oldCrossNodeId + "," + crossNodeId;
                            }
                        }
                    }
                } catch (Exception e) {
                    Log.game.error("解析跨服服务器的分配配置出错，ex=" + e, e);
                    return "sn[" + groupCfg.sn + "] 解析跨服分组范围时出现错误[" + groupCfg.crossAllocate + "],err:" + e.getMessage();
                }
            }
            if(StrUtil.isNotEmpty(groupCfg.startTime)) {
                info.validTime = TimeUtil.parseTime(groupCfg.startTime);
            }
            if(StrUtil.isNotEmpty(groupCfg.endTime)) {
                info.invalidTime = TimeUtil.parseTime(groupCfg.endTime);
            }else{
                info.invalidTime = Long.MAX_VALUE;
            }
            if (info.invalidTime > 0) {
                if (info.validTime >= info.invalidTime) {
                    return "sn[" + groupCfg.sn + "] 失效时间不能早于生效时间";
                }
                // 已失效
                if (nowTime >= info.invalidTime) {
                    continue;
                }
            }
            // 是否出现交叉
            for (CrossGroupInfo other : allInfos) {
                if (!other.key.equals(info.key)) {
                    continue;
                }
                String errstr = "";

                // 本例的开始时间在这一例的时间中间
                if (other.validTime <= info.validTime && info.validTime <= other.invalidTime) {
                    errstr = "ServerActivityGroup表sn[" + info.id + "] 的生效时间在 sn[" + other.id + "]的时间范围中！";
                }
                // 本例的结束时间在这一例的时间中间
                else if (other.validTime <= info.invalidTime && info.invalidTime <= other.invalidTime) {
                    errstr = "ServerActivityGroup表sn[" + info.id + "] 的失效时间在 sn[" + other.id + "]的时间范围中！";
                }
                // 没事-下一个。
                else continue;
                // 存在一样的服务器ID
                for (int serverId1 : other.serverAtGroups.keySet()) {
                    if (info.serverAtGroups.containsKey(serverId1)) {
                        return errstr + "都存在服务器：" + serverId1;
                    }
                }
            }
            allInfos.add(info);

            infos.put(groupCfg.sn, info);
        }
        allInfos.clear();
        crossGroupSnInfoMap = infos;
        Log.temp.info("===crossGroupSnInfoMap={}", crossGroupSnInfoMap.keySet());
        return "";
    }

    /**
     * 跨服专用，获取玩法分配服务器组ID
     * @param crossType
     * @param serverId
     * @return
     */
    public static int getCrossServerIdGroup(int crossType, int serverId) {
        String groupKey = crossType + "_0";
        for(CrossGroupInfo info : crossGroupSnInfoMap.values()){
            if (!info.key.equals(groupKey)) {
                continue;
            }
            Integer groupId = info.serverAtGroups.get(serverId);
            if (groupId == null) {
                continue;
            }
//            String crossNodeId = info.groupAtCrossNodes.get(groupId);
//            if (crossNodeId == null || crossNodeId.isEmpty()) {
//                continue;
//            }
//            String serverStr = info.groupAtServerStrs.get(groupId);
            return groupId;
        }
        return 0;
    }

    public static List<Integer> getCrossServerIdList(int groupId) {
        CrossGroupInfo info = crossGroupSnInfoMap.get(groupId);
        if(info == null){
            return null;
        }
        return info.groupAllServers.get(groupId);
    }

    public static void reloadConfFateDraw() {
        Map<Integer,  Map<Integer, RangeInt>> typeSnRangeMap = new HashMap<>();
        Map<Integer, List<Integer>> typeASCSnListMapNew = new HashMap<>();
        Map<Integer, Integer> typeMaxNumMap = new HashMap<>();
        for(ConfPvpReward conf : ConfPvpReward.findAll()){
            List<Integer> snList =typeASCSnListMapNew.get(conf.type);
            if(snList == null){
                snList = new ArrayList<>();
                typeASCSnListMapNew.put(conf.type, snList);
            }
            snList.add(conf.sn);

            int[] rankRange = Utils.strToIntArray(conf.rank, "\\|");
            if(rankRange == null || rankRange.length < 2){
                Log.temp.error("====ConfPvpReward配表错误， 数组个数不对。sn={}, rank={}", conf.sn, conf.rank);
                continue;
            }
            int max = rankRange[1];
            Map<Integer, RangeInt> snRangeMap =  typeSnRangeMap.get(conf.type);
            if(snRangeMap == null){
                snRangeMap = new HashMap<>();
                typeSnRangeMap.put(conf.type, snRangeMap);
            }
            snRangeMap.put(conf.sn, new RangeInt(rankRange[0], max));
            int oldMax = typeMaxNumMap.getOrDefault(conf.type, 0);
            if(oldMax < max){
                typeMaxNumMap.put(conf.type, max);
            }
        }
        for(List<Integer> snList : typeASCSnListMapNew.values()){
            snList.sort(Comparator.comparingInt(Integer::intValue));
        }
        pvpTypeASCSnListMap = Collections.unmodifiableMap(typeASCSnListMapNew);
        pvpRewardSnRangeMap = Collections.unmodifiableMap(typeSnRangeMap);
        pvpTypeMaxNumMap = Collections.unmodifiableMap(typeMaxNumMap);
    }

    public static int getPvpTypeMaxNum(int type) {
        return pvpTypeMaxNumMap.getOrDefault(type, 0);
    }

    public static List<Integer> getPvpTypeASCSnList(int type) {
        return pvpTypeASCSnListMap.get(type);
    }

    public static Map<Integer, RangeInt> getPvpRewardSnRangeMap(int type) {
        return pvpRewardSnRangeMap.getOrDefault(type, new HashMap<>());
    }


    private static void reloadArtifactGemattr() {
        Map<Integer, List<Integer>> confGroupGemattrSnListNew = new HashMap<>();

        for(ConfArtifactGemattr conf : ConfArtifactGemattr.findAll()){
            List<Integer> snList = confGroupGemattrSnListNew.get(conf.group_id);
            if(snList == null) {
                snList = new ArrayList<>();
                confGroupGemattrSnListNew.put(conf.group_id, snList);
            }
            snList.add(conf.sn);
        }
        confGroupGemattrSnList = Collections.unmodifiableMap(confGroupGemattrSnListNew);
    }

    public static List<Integer> getConfGroupGemattrSnList(int groupId) {
        return confGroupGemattrSnList.get(groupId);
    }


    private static void reloadConfSkin() {
        List<Integer> skinTypeOneSnASCListNew = new ArrayList<>();
        for(ConfSkin conf : ConfSkin.findAll()){
            if(conf.type == 1){
                skinTypeOneSnASCListNew.add(conf.sn);
            }
        }
        skinTypeOneSnASCListNew.sort(Comparator.comparingInt(Integer::intValue));
        skinTypeOneSnASCList = Collections.unmodifiableList(skinTypeOneSnASCListNew);
    }

    public static List<Integer> getSkinTypeOneSnASCList() {
        return skinTypeOneSnASCList;
    }



    private  static void reloadConfMall(){
        Map<Integer, List<Integer>> typeMallSnListMapNew = new HashMap<>();
        for(ConfMall conf : ConfMall.findAll()){
             List<Integer> snList = typeMallSnListMapNew.get(conf.type);
             if(snList == null){
                 snList = new ArrayList<>();
                 typeMallSnListMapNew.put(conf.type, snList);
             }
             snList.add(conf.sn);
        }
        for(List<Integer> snList : typeMallSnListMapNew.values()){
            Collections.sort(snList, (a, b) -> {
                int ret = 0;// 0默认相等
                ConfMall confA = ConfMall.get(a);
                ConfMall confB = ConfMall.get(b);
                if (confA != null && confB != null) {
                    if (confA.sort < confB.sort) {
                        ret = -1;
                    } else if (confA.sort > confB.sort) {
                        ret = 1;
                    }
                }
                return ret;
            });
        }
        typeMallSnListMap = Collections.unmodifiableMap(typeMallSnListMapNew);
        Log.temp.info("typeMallSnListMap={}", typeMallSnListMap);
    }

    public static List<Integer> getTypeMallSortASCSnList(int type) {
        return typeMallSnListMap.get(type);
    }

    private static void reloadConfPayMall() {
        payConditionsMap.clear();
        shortLoopPaySnMap.clear();
        drawLoopPaySnMap.clear();
        anyShortageItemsPaySnMap.clear();
        commonConditionPaySnMap.clear(); // 清空新增的Map
        fashionSkinLvPaySnMap.clear();
        flyEggLoopPaySnList.clear();
        Map<Integer, List<ConfPayMall>> guardPayRepSnMallSnMapNew = new HashMap<>();
        Map<Integer, List<ConfPayMall>> dungeonPayRepSnMallSnMapNew = new HashMap<>();
        Map<Integer, List<Integer>> actTypePayMallSnMapNew = new HashMap<>();
        Map<String, ConfPayMall> iosPidConfPayMallMapNew = new HashMap<>();
        for (ConfPayMall conf : ConfPayMall.findAll()) {
            String[] strArr = conf.conditon.split("\\|");
            payConditionsMap.put(conf.sn, strArr);
            switch (strArr[0]) {
                case PayMallTypeKey.pay_condition_guard: {
                    int repSn = Integer.parseInt(strArr[1]);
                    guardPayRepSnMallSnMapNew.computeIfAbsent(repSn,k -> new ArrayList<>()).add(conf);
                }
                break;
                case PayMallTypeKey.pay_condition_dungeon:{
                    int repSn = Integer.parseInt(strArr[1]);
                    dungeonPayRepSnMallSnMapNew.computeIfAbsent(repSn,k -> new ArrayList<>()).add(conf);
                }
                break;
                case PayMallTypeKey.pay_condition_act_type: {
                    int actType = Integer.parseInt(strArr[1]);
                    actTypePayMallSnMapNew.computeIfAbsent(actType, k -> new ArrayList<>()).add(conf.sn);
                    int[] ints = Utils.strToIntArray(strArr[2]);
                    actPayMallConditionMap.put(conf.sn, new int[] {
                            actType,
                            ints[0],
                            ints[1]
                    });
                }
                break;
                case PayMallTypeKey.pay_condition_shortage: {
                    if (conf.type == PayMallTypeKey.PAY_Type_26) {
                        int itemSn = Utils.intValue(strArr[1]);
                        if (conf.reset != PayMallTypeKey.resetType_0) {
                            shortLoopPaySnMap.computeIfAbsent(itemSn, k -> new ArrayList<>()).add(conf.sn);
                        }
                    }
                }
                break;
                case PayMallTypeKey.pay_condition_any_shortage: {
                    // 使用整个物品列表字符串作为key
                    String itemsStr = strArr[1];
                    if (!anyShortageItemsPaySnMap.containsKey(itemsStr)) {
                        anyShortageItemsPaySnMap.put(itemsStr, new ArrayList<>());
                    }
                    anyShortageItemsPaySnMap.get(itemsStr).add(conf.sn);
                }
                break;
                case PayMallTypeKey.pay_condition_common: {
                    // 使用条件字符串作为key
                    String conditionStr = strArr[1];
                    if (!commonConditionPaySnMap.containsKey(conditionStr)) {
                        commonConditionPaySnMap.put(conditionStr, new ArrayList<>());
                    }
                    commonConditionPaySnMap.get(conditionStr).add(conf.sn);
                }
                break;
                case PayMallTypeKey.pay_condition_draw: {
                    if (conf.type == PayMallTypeKey.PAY_Type_26) {
                        int drawType = Utils.intValue(strArr[1]);
                        if (conf.reset == PayMallTypeKey.resetType_1) {
                            drawLoopPaySnMap.computeIfAbsent(drawType, k -> new ArrayList<>()).add(conf.sn);
                        }
                    }
                }
                break;
                case PayMallTypeKey.pay_condition_flyEgg: {
                    if (conf.type == PayMallTypeKey.PAY_Type_26) {
                        flyEggLoopPaySnList.add(conf.sn);
                    }
                }
                break;
                case PayMallTypeKey.pay_condition_fashionSkin: {
                    if (conf.type == PayMallTypeKey.PAY_Type_26) {
                        String key = strArr[1];
                        List<Integer> snList = fashionSkinLvPaySnMap.computeIfAbsent(key, k -> new ArrayList<>());
                        snList.add(conf.sn);
                    }
                }
                break;
            }
            if (conf.ios_pid != null && !conf.ios_pid.isEmpty()) {
                iosPidConfPayMallMapNew.put(conf.ios_pid,conf);
            }
        }
        // 26类型的相关礼包排序
        for (List<Integer> snList : shortLoopPaySnMap.values()) {
            snList.sort(Comparator.comparingInt(c -> ConfPayMall.get(c).parameter));
        }
        for (List<Integer> snList : drawLoopPaySnMap.values()) {
            snList.sort(Comparator.comparingInt(c -> ConfPayMall.get(c).parameter));
        }
        for (List<Integer> snList : fashionSkinLvPaySnMap.values()) {
            snList.sort(Comparator.comparingInt(c -> ConfPayMall.get(c).parameter));
        }
        flyEggLoopPaySnList.sort(Comparator.comparingInt(c -> ConfPayMall.get(c).parameter));

        // 对guardPayRepSnMallSnMap中的每个List进行排序
        for (List<ConfPayMall> list : guardPayRepSnMallSnMapNew.values()) {
            list.sort(Comparator.comparingInt(c -> c.parameter));
        }

        // 对dungeonPayRepSnMallSnMap中的每个List进行排序
        for (List<ConfPayMall> list : dungeonPayRepSnMallSnMapNew.values()) {
            list.sort(Comparator.comparingInt(c -> c.parameter));
        }

        guardPayRepSnMallSnMap = Collections.unmodifiableMap(guardPayRepSnMallSnMapNew);
        dungeonPayRepSnMallSnMap = Collections.unmodifiableMap(dungeonPayRepSnMallSnMapNew);
        actTypePayMallSnMap = Collections.unmodifiableMap(actTypePayMallSnMapNew);
        iosPidConfPayMallMap = Collections.unmodifiableMap(iosPidConfPayMallMapNew);
    }

    public static ConfPayMall getConfPayMallByIosPid(String iosPid){
        return iosPidConfPayMallMap.get(iosPid);
    }

    private static void reloadCaptureSlaveEarnings() {
        List<Integer> snList = new ArrayList<>();
        for(ConfCaptureSlaveEarnings conf : ConfCaptureSlaveEarnings.findAll()){
            snList.add(conf.sn);
        }
        snList.sort(Comparator.comparingInt(Integer::intValue));
        captureSlaveEarningSnList = Collections.unmodifiableList(snList);
    }

    public static List<Integer> getCaptureSlaveEarningsSnList(){
        return captureSlaveEarningSnList;
    }

    public static void reloadPrivilegeCard() {
        Map<Integer, List<Integer>> confTypePrivilegeCardSnListMapNew = new HashMap<>();
        // 全局配置
        for (ConfPrivilegeCard conf : ConfPrivilegeCard.findAll()) {
            List<Integer> snList = confTypePrivilegeCardSnListMapNew.get(conf.privilege_type);
            if(snList == null){
                snList = new ArrayList<>();
                confTypePrivilegeCardSnListMapNew.put(conf.privilege_type, snList);
            }
            snList.add(conf.sn);
        }
        confTypePrivilegeCardSnListMap = Collections.unmodifiableMap(confTypePrivilegeCardSnListMapNew);
    }

    public static List<Integer> getPrivilegeCardSn(int privilegeType) {
        return confTypePrivilegeCardSnListMap.get(privilegeType);
    }

    public static void reloadConfActivityRankReward(){
        Map<Integer, Map<Integer, List<Integer>>> confTypeGroupIdSnListMapNew = new HashMap<>();
        for(ConfActivityRankReward conf : ConfActivityRankReward.findAll()){
            Map<Integer, List<Integer>> groupSnListMap = confTypeGroupIdSnListMapNew.get(conf.type);
            if(groupSnListMap == null){
                groupSnListMap = new HashMap<>();
                confTypeGroupIdSnListMapNew.put(conf.type, groupSnListMap);
            }
            List<Integer> snList = groupSnListMap.get(conf.group_id);
            if(snList == null){
                snList = new ArrayList<>();
                groupSnListMap.put(conf.group_id, snList);
            }
            snList.add(conf.sn);
        }

        for(Map<Integer, List<Integer>> entry : confTypeGroupIdSnListMapNew.values()){
            for(List<Integer> snList : entry.values()){
                snList.sort(Comparator.comparingInt(Integer::intValue));
            }
        }
        confTypeGroupIdSnListMap = Collections.unmodifiableMap(confTypeGroupIdSnListMapNew);
    }

    public static List<Integer> getConfActivityRankRewardSnList(int type, int group_id){
        Map<Integer, List<Integer>> groupSnListMap = confTypeGroupIdSnListMap.get(type);
        if(groupSnListMap == null){
            return null;
        }
        List<Integer> snList = groupSnListMap.get(group_id);
        if(snList == null){
            return null;
        }
        return snList;
    }

    public static void reloadConfActivityAdjust(){
        Map<Integer, Map<Integer,String>> typeWeekConfSnMapNew = new HashMap<>();
        for(ConfActivityAdjust conf : ConfActivityAdjust.findAll()){
            Map<Integer, String> weekSnMap = typeWeekConfSnMapNew.get(conf.type);
            if(weekSnMap == null){
                weekSnMap = new HashMap<>();
                typeWeekConfSnMapNew.put(conf.type, weekSnMap);
            }
            weekSnMap.put(conf.week, conf.sn);
        }
        typeWeekConfSnMap = Collections.unmodifiableMap(typeWeekConfSnMapNew);
    }

    public static String getConfActivityAdjustSn(int type, int week){
        Map<Integer, String> weekSnMap = typeWeekConfSnMap.get(type);
        return weekSnMap.get(week);
    }

    public static void reloadConfOutput(){
        Map<Integer, Integer> confOutputMapNew = new HashMap<>();
        for(ConfOutput conf : ConfOutput.findAll()){
            confOutputMapNew.put(conf.groupid, confOutputMapNew.getOrDefault(conf.groupid, 0) + 1);
        }
        confOutputMap = Collections.unmodifiableMap(confOutputMapNew);

    }

    public static int getConfOutPutMaxNum(int groupid){
        return confOutputMap.getOrDefault(groupid, 0);
    }


    private static void reloadBreakGoldEggLayers(){
        Map<Integer, Map<Integer, Map<Integer, Integer>>> typeGroupLayerConfSnMapNew = new HashMap<>();
        Map<Integer, Map<Integer, Integer>> typeGroupMaxLayerMapNew = new HashMap<>();
        for(ConfBreakGoldEggLayers conf : ConfBreakGoldEggLayers.findAll()){
            Map<Integer, Map<Integer, Integer>> groupLayerConfSnMap = typeGroupLayerConfSnMapNew.get(conf.type);
            if(groupLayerConfSnMap == null){
                 groupLayerConfSnMap = new HashMap<>();
                 typeGroupLayerConfSnMapNew.put(conf.type, groupLayerConfSnMap);
            }
            Map<Integer, Integer> layerConfSnMap = groupLayerConfSnMap.get(conf.group_id);
            if(layerConfSnMap == null){
                layerConfSnMap = new HashMap<>();
                groupLayerConfSnMap.put(conf.group_id, layerConfSnMap);
            }
            layerConfSnMap.put(conf.layers, conf.sn);

            Map<Integer, Integer> typeMaxLayerMap = typeGroupMaxLayerMapNew.get(conf.type);
            if(typeMaxLayerMap == null){
                typeMaxLayerMap = new HashMap<>();
                typeGroupMaxLayerMapNew.put(conf.type, typeMaxLayerMap);
            }
            int maxLayer = typeMaxLayerMap.getOrDefault(conf.group_id, 0);
            if(maxLayer < conf.layers){
                typeMaxLayerMap.put(conf.group_id, conf.layers);
            }
        }
        typeGroupLayerConfSnMap = Collections.unmodifiableMap(typeGroupLayerConfSnMapNew);
        typeGroupMaxLayerMap = Collections.unmodifiableMap(typeGroupMaxLayerMapNew);
    }

    public static int getBreakGoldEggConfSn(int type, int group_id, int layers){
        Map<Integer, Map<Integer, Integer>> groupLayerConfSnMap = typeGroupLayerConfSnMap.get(type);
        if(groupLayerConfSnMap == null){
            return 0;
        }
        Map<Integer, Integer> layerConfSnMap = groupLayerConfSnMap.get(group_id);
        if(layerConfSnMap == null){
            return 0;
        }
        return layerConfSnMap.getOrDefault(layers, 0);
    }

    public static int getBreakGoldEggMaxLayer(int type, int group_id){
        Map<Integer, Integer> typeMaxLayerMap = typeGroupMaxLayerMap.get(type);
        if(typeMaxLayerMap == null){
            return 0;
        }
        return typeMaxLayerMap.getOrDefault(group_id, 0);
    }




    private static void reloadActivityControl(){
        List<Integer> confList = new ArrayList<>();
        for(ConfActivityControl conf : ConfActivityControl.findAll()){
            if(conf.timeType == ParamKey.activityTimeType_4){
                confList.add(conf.sn);
            }
        }
        activityType4List = Collections.unmodifiableList(confList);
    }

    public static List<Integer> getActivityType4List(){
        return activityType4List;
    }

    private static void reloadConfGuildBuff() {
        Map<Integer, Integer> guildKillNumBuffSnMapNew = new HashMap<>();
        for(ConfFamliyGvgBufflist conf : ConfFamliyGvgBufflist.findAll()){
            guildKillNumBuffSnMapNew.put(conf.defeatNum, conf.sn);
        }
        guildKillNumBuffSnMap = Collections.unmodifiableMap(guildKillNumBuffSnMapNew);
    }

    public static int getGuildKillNumBuffSn(int killNum){
        if(guildKillNumBuffSnMap.containsKey(killNum)){
            return guildKillNumBuffSnMap.get(killNum);
        } else {
            int killTop = 0;
            int tempSn = 0;
            for(Map.Entry<Integer, Integer> entry : guildKillNumBuffSnMap.entrySet()){
                if(entry.getKey() <= killNum){
                    if(entry.getKey() >= killTop){
                        killTop = entry.getKey();
                        tempSn = entry.getValue();
                    }
                }
            }
            return tempSn;
        }
    }
    private static void reloadConfMount(){
        List<Integer> mountSnListNew = new ArrayList<>();
        for(ConfMountSkin_0 conf : ConfMountSkin_0.findAll()){
            if(!mountSnListNew.contains(conf.mount_id)){
                mountSnListNew.add(conf.mount_id);
            }
        }
        mountSnList = Collections.unmodifiableList(mountSnListNew);
    }

    private static void reloadConfBackLevel_0(){
        List<Integer> wingSnListNew = new ArrayList<>();
        for(ConfBackLevel_0 conf : ConfBackLevel_0.findAll()){
            if(!wingSnListNew.contains(conf.id)){
                wingSnListNew.add(conf.id);
            }
        }
        wingSnList = Collections.unmodifiableList(wingSnListNew);
    }

    private static void reloadArtifact(){
        List<Integer> snList = new ArrayList<>();
        for(ConfArtifactSkin_0 conf : ConfArtifactSkin_0.findAll()){
            snList.add(conf.id);
        }
        artifactSkinList = Collections.unmodifiableList(snList);
    }

    private static void reloadIllustrated(){
        Map<Integer, Integer> skillSnLvIllustratedMapNew = new HashMap<>();
        Map<Integer, Integer> petSnLvIllustratedMapNew = new HashMap<>();
        for(ConfIllustrated_0 conf : ConfIllustrated_0.findAll()){
            if(conf.type == MallManager.cardPoolType1){
                if(skillSnLvIllustratedMapNew.containsKey(conf.id)){
                    continue;
                }
                skillSnLvIllustratedMapNew.put(conf.id, 0);
            } else if(conf.type == MallManager.cardPoolType2){
                if(petSnLvIllustratedMapNew.containsKey(conf.id)){
                    continue;
                }
                petSnLvIllustratedMapNew.put(conf.id, 0);
            }
        }
        skillSnLvIllustratedMap = Collections.unmodifiableMap(skillSnLvIllustratedMapNew);
        petSnLvIllustratedMap = Collections.unmodifiableMap(petSnLvIllustratedMapNew);
    }
    public static Map<Integer, Integer> getInitSkillIllustratedMap(){
        return skillSnLvIllustratedMap;
    }

    public static Map<Integer, Integer> getInitPetIllustratedMap(){
        return petSnLvIllustratedMap;
    }

    private static void reloadArean(){
        Map<Integer, Integer> winNumSnMap = new HashMap<>();
        Map<Integer, Integer> snRankMap = new HashMap<>();
        for(ConfCrossPvpGradingMatch conf : ConfCrossPvpGradingMatch.findAll()){
            winNumSnMap.put(conf.promote_win_num, conf.sn);
            snRankMap.put(conf.sn, conf.rank_id);
        }
        areanWinNumSnMap = Collections.unmodifiableMap(winNumSnMap);
        areanSnRankMap = Collections.unmodifiableMap(snRankMap);

        Map<Integer, Integer> areanGradeTopMapNew = new HashMap<>();
        Map<Integer, Integer> areanGradeNextMapNew = new HashMap<>();

        Map<Integer, RangeInt> areanGradeRankRangeMapNew = new HashMap<>();

        for(ConfCrossPvpGrade conf : ConfCrossPvpGrade.findAll()){
            areanGradeTopMapNew.put(conf.next_rank, conf.sn);
            areanGradeNextMapNew.put(conf.sn, conf.next_rank);
            if(conf.main_rank == ParamKey.arenaRankTopMainGrade){
                int[] rankArr = Utils.strToIntArray(conf.range, "\\|");
                areanGradeRankRangeMapNew.put(conf.sn, new RangeInt(rankArr[0], rankArr[1]));
            }
        }
        areanGradeTopMap = Collections.unmodifiableMap(areanGradeTopMapNew);
        areanGradeNextMap = Collections.unmodifiableMap(areanGradeNextMapNew);
        areanGradeRankRangeMap = Collections.unmodifiableMap(areanGradeRankRangeMapNew);

    }

    private static void reloadCross(){
        Map<Integer, Map<Integer, RangeInt>> crossRankTypeSnRangeMapNew = new HashMap<>();
        for(ConfCrossWarRankReward conf : ConfCrossWarRankReward.findAll()){
            Map<Integer, RangeInt> snRangeMap = crossRankTypeSnRangeMapNew.get(conf.type);
            if(snRangeMap == null){
                snRangeMap = new HashMap<>();
                crossRankTypeSnRangeMapNew.put(conf.type, snRangeMap);
            }
            snRangeMap.put(conf.sn, new RangeInt(conf.range[0], conf.range[1]));
        }
        crossRankTypeSnRangeMap =  Collections.unmodifiableMap(crossRankTypeSnRangeMapNew);
    }

    public static int getCrossRankSn(int type, int rank){
        Map<Integer, RangeInt> snRangeMap = crossRankTypeSnRangeMap.get(type);
        if(snRangeMap != null){
            for(Map.Entry<Integer, RangeInt> entry : snRangeMap.entrySet()){
                RangeInt rangeInt = entry.getValue();
                if(rangeInt.min <= rank && rank <= rangeInt.max){
                    return entry.getKey();
                } else if(rangeInt.min <= rank && 0 == rangeInt.max){
                    return entry.getKey();
                }
            }
        }
        return 0;
    }

    private static void reloadLeagueData(){
        Map<Integer, Map<Integer, RangeInt>> leagueRankTypeSnRangeMapNew = new HashMap<>();
        Map<Integer, Integer> typeMaxNum = new HashMap<>();
        for(ConfFamiliybrawlRankReward conf : ConfFamiliybrawlRankReward.findAll()){
            Map<Integer, RangeInt> snRangeMap = leagueRankTypeSnRangeMapNew.get(conf.group);
            if(snRangeMap == null){
                snRangeMap = new HashMap<>();
                leagueRankTypeSnRangeMapNew.put(conf.group, snRangeMap);
            }
            snRangeMap.put(conf.sn, new RangeInt(conf.rank_range[0], conf.rank_range[1]));
            int maxNum = Utils.intValue(typeMaxNum.get(conf.group));
            if(maxNum < conf.rank_range[1]){
                typeMaxNum.put(conf.group, conf.rank_range[1]);
            }
        }
        leagueRankTypeSnRangeMap = Collections.unmodifiableMap(leagueRankTypeSnRangeMapNew);
        leagueRankTypeMaxNumMap = Collections.unmodifiableMap(typeMaxNum);
    }
    public static int getLeagueRankMaxNum(int type){
        if(leagueRankTypeMaxNumMap.containsKey(type)){
            return leagueRankTypeMaxNumMap.get(type);
        }
        return 0;
    }

    public static int getLeagueRankSn(int type, int rank){
        Map<Integer, RangeInt> snRangeMap = leagueRankTypeSnRangeMap.get(type);
        if(snRangeMap != null){
            for(Map.Entry<Integer, RangeInt> entry : snRangeMap.entrySet()){
                RangeInt rangeInt = entry.getValue();
                if(rangeInt.min <= rank && rank <= rangeInt.max){
                    return entry.getKey();
                }
            }
        }
        return 0;
    }

    public static int getTopGradeSn(int rank){
        for(Map.Entry<Integer, RangeInt> entry : areanGradeRankRangeMap.entrySet()){
            RangeInt rangeInt = entry.getValue();
            if(rangeInt.min <= rank && rank <= rangeInt.max){
                return entry.getKey();
            }
        }
        return -1;
    }

    public static int getArenaBridgeTopSn(int grade){
        return Utils.intValue(areanGradeTopMap.get(grade));
    }

    public static int getArenaBridgeNextGradeSn(int grade){
        return Utils.intValue(areanGradeNextMap.get(grade));
    }

    public static int getArenaGradeSn(int winNum){
        return Utils.intValue(areanWinNumSnMap.get(winNum));
    }

    public static int getArenaRank(int winNum){
        return Utils.intValue(areanSnRankMap.get(getArenaGradeSn(winNum)));
    }

    private static void reloadServerId(){
        // serverId对应几服
        Map<Integer, Integer> serverIdNumMapNew = new HashMap<>();
        // serverId对应几区
        Map<Integer, Integer> serverIdZoneMapNew = new HashMap<>();
        // 几服对应serverId
        Map<Integer, Integer> numServerIdMapNew = new HashMap<>();


        // 区，List<serverId>（正序）
        Map<Integer, List<Integer>> zoneServerIdListMapNew = new HashMap<>();

        List<ConfServerId> confList = ConfServerId.findBy(ConfServerId.K.sn, OrderBy.ASC);
        for(ConfServerId conf : confList){
            serverIdNumMapNew.put(conf.serverId, conf.sn);
            numServerIdMapNew.put(conf.sn, conf.serverId);
            serverIdZoneMapNew.put(conf.serverId, conf.zone);

            List<Integer> serverIdList = zoneServerIdListMapNew.get(conf.zone);
            if(serverIdList == null){
                serverIdList = new ArrayList<>();
                zoneServerIdListMapNew.put(conf.zone, serverIdList);
            }
            serverIdList.add(conf.serverId);
        }
        serverIdNumMap = Collections.unmodifiableMap(serverIdNumMapNew);
        numServerIdMap = Collections.unmodifiableMap(numServerIdMapNew);
        zoneServerIdListMap = Collections.unmodifiableMap(zoneServerIdListMapNew);
        serverIdZoneMap = Collections.unmodifiableMap(serverIdZoneMapNew);
    }

    public static int getZone(int serverId){
        if(serverIdZoneMap.containsKey(serverId)){
            return serverIdZoneMap.get(serverId);
        }
        return 0;
    }

    private static void reloadWorldBossTotalDmg(){
        Map<Integer, Integer> worldBossSnMapNew = new HashMap<>();

        for(ConfWorldBossTotalDmg conf : ConfWorldBossTotalDmg.findAll()){
            worldBossSnMapNew.put(conf.sn, conf.sn);
        }

        worldBossSnMap = Collections.unmodifiableMap(worldBossSnMapNew);
    }

    public static int getWorldBossTotalDmgSn(int day){
        int sn = 1;
        for(int key : worldBossSnMap.keySet()){
            if(key > day){
                continue;
            }
            if(sn < key){
                sn = key;
            }
        }
        return sn;
    }

    public static void mainUnitMaxValue(int newValue) {
        if (mainUnitMaxValue < newValue) {
            mainUnitMaxValue = newValue;
        }
    }

    public static void reloadConfMainUnit(){
        Map<Long, Map<Integer, Integer>> monsterKillRewardMapNew = new HashMap<>();
        Map<Long, Map<Integer, Integer>> monsterKillOneRewardMapNew = new HashMap();
        for (int m = 1; m <= mainUnitMaxValue; m++) {
            String confJSON = _readConfFile(Utils.createStr("ConfMainUnit{}.json", m));
            if (StringUtils.isBlank(confJSON)) continue;
            //填充实体数据
            JSONArray confs = Utils.toJSONArray(confJSON);
            for (int i = 0; i < confs.size(); i++) {
                JSONObject conf = confs.getJSONObject(i);
                if(conf == null){
                    continue;
                }
                Map<Integer, Integer> killMap = Utils.intArrToIntMap(Utils.strToIntArray(conf.getString("kill_reward")));
                Map<Integer, Integer> killOneMap = Utils.intArrToIntMap(new HashMap<>(), Utils.parseIntArray2(conf.getString("first_kill_reward")));
                long id =  conf.getLongValue("sn");
                monsterKillRewardMapNew.put(id, Collections.unmodifiableMap(killMap));
                monsterKillOneRewardMapNew.put(id, Collections.unmodifiableMap(killOneMap));
            }
        }
        monsterKillRewardMap = Collections.unmodifiableMap(monsterKillRewardMapNew);
        monsterKillOneRewardMap = Collections.unmodifiableMap(monsterKillOneRewardMapNew);
    }

    public static Map<Integer, Integer> getKillReward(long unitId){
        Map<Integer, Integer> map = new HashMap<>();
        if(monsterKillRewardMap.containsKey(unitId)){
            return monsterKillRewardMap.get(unitId);
        }
        return map;
    }
    public static Map<Integer, Integer> getKillOneReward(long unitId){
        if(monsterKillOneRewardMap.containsKey(unitId)){
            return monsterKillOneRewardMap.get(unitId);
        }
        return new HashMap<>();
    }

    public static void repMaxValue(int newValue){
        if(repMaxValue < newValue){
            repMaxValue = newValue;
        }
    }

    public static int getDarkTrialLevelTypeMap(int level, int type){
        Map<Integer, Integer> typeSnMap = darkTrialLevelTypeMap.get(level);
        if(typeSnMap != null){
            return typeSnMap.get(type);
        }
        return darkTrialLevelTypeMap.get(1).get(type);
    }

    /**
     * 根据副本类型和副本难度拿到最大星级对应的副本sn
     */
    public static int getDarkTrialTypeDiffTopSn(int type, int diffcult){
        Map<Integer, Integer> diffSnMap = darkTrialTypeDiffSnMap.get(type);
        if (diffSnMap != null) {
            return diffSnMap.getOrDefault(diffcult, 0);
        }
        return darkTrialLevelTypeMap.get(type).get(1);
    }

    public static void reloadConfChapter() {

        Map<Integer, ConfChapterVO> chapterVoMapNew = new HashMap<>();
        for (int m = 1; m <= repMaxValue; m++) {
            String confJSON = _readConfFile(Utils.createStr("ConfChapter{}.json", m));
            if (StringUtils.isBlank(confJSON)) continue;
            //填充实体数据

            JSONArray confs = Utils.toJSONArray(confJSON);
            for (int i = 0; i < confs.size(); i++) {
                JSONObject conf = confs.getJSONObject(i);
                ConfChapterVO vo = new ConfChapterVO(conf.getIntValue("sn"), conf.getIntValue("index"),
                        conf.getIntValue("type"), conf.getIntValue("level"), conf.getString("power"),
                        conf.getIntValue("map"), conf.getIntValue("chapter"), conf.getIntValue("section"),
                        conf.getIntValue("part"), conf.getIntValue("part_type"), conf.getIntValue("next_part"),
                        Utils.parseFloatArray(conf.getString("interval")),
                        Utils.parseIntArray2(conf.getString("monster_refresh1")),
                        Utils.parseIntArray2(conf.getString("monster_refresh2")),
                        Utils.parseIntArray2(conf.getString("monster_refresh3")),
                        Utils.parseIntArray2(conf.getString("monster_refresh4")),
                        Utils.parseIntArray2(conf.getString("monster_refresh5")),
                        conf.getIntValue("time"), Utils.parseIntArray2(conf.getString("online_reward1")),
                        Utils.parseIntArray2(conf.getString("online_reward2")),
                        Utils.parseIntArray2(conf.getString("offline_reward1")),
                        Utils.parseIntArray2(conf.getString("offline_reward2")),
                        conf.getIntValue("bossModel"), conf.getIntValue("min_part_time"));

                chapterVoMapNew.put(vo.sn, vo);
            }
        }
        chapterVoMap = Collections.unmodifiableMap(chapterVoMapNew);
        Log.temp.info("===加载副本表数量={}", repMaxValue);


        Map<Integer, Integer> repTypeMaxSnMapNew = new HashMap<>();
        Map<Integer, Integer> repTypeMinSnMapNew = new HashMap<>();

        Map<Integer, Map<Integer, Integer>> repTypeLevelSnMapNew = new HashMap<>();

        // 突袭神灯小偷CoinChapter表
        for(ConfCoinChapter conf : ConfCoinChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.COINCHAPTER_2, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.COINCHAPTER_2, conf.level);
            }

            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.COINCHAPTER_2, 0);
            if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.COINCHAPTER_2, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.COINCHAPTER_2);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.COINCHAPTER_2, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        // 挑战冰巢龙穴DiamondChapter表
        for (ConfDiamondChapter conf : ConfDiamondChapter.findAll()) {
            // 记录该类型副本的最大等级
            int level = repTypeMaxSnMapNew.getOrDefault(InstanceConstants.DIAMONDCHAPTER_3, 0);
            if (level < conf.level) {
                repTypeMaxSnMapNew.put(InstanceConstants.DIAMONDCHAPTER_3, conf.level);
            }
            // 记录该类型副本的最小等级
            int levelMin = repTypeMinSnMapNew.getOrDefault(InstanceConstants.DIAMONDCHAPTER_3, 0);
            if (levelMin == 0 || levelMin > conf.level) {
                repTypeMinSnMapNew.put(InstanceConstants.DIAMONDCHAPTER_3, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.computeIfAbsent(InstanceConstants.DIAMONDCHAPTER_3, k -> new HashMap<>());
            int minSn = levelSnMap.getOrDefault(conf.level, conf.sn);
            levelSnMap.put(conf.level, Math.min(conf.sn, minSn));
        }

        // 揭秘时间尽头legacy_chapter表
//        ConfLegacyChapter conf_4 = ConfLegacyChapter.findBy(ConfLegacyChapter.K.level, OrderBy.DESC).get(0);
//        if(conf_4 != null){
//            petTypeMaxSnMapNew.put(InstanceConstants.LEGACY_CHAPTER_4, conf_4.level);
//        }

        int PVPCHAPTER_5 = 5;// pvp副本PvpChapter表
        for(ConfPvpChapter conf : ConfPvpChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.PVPCHAPTER_5, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.PVPCHAPTER_5, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.PVPCHAPTER_5, 0);
             if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.PVPCHAPTER_5, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.PVPCHAPTER_5);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.PVPCHAPTER_5, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        int LEAGUESOLOCHAPTER_6 = 6;// 征战熔岩巨兽LeagueSoloChapter表
        for(ConfLeagueSoloChapter conf : ConfLeagueSoloChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.LEAGUESOLOCHAPTER_6, 0);
            if(level < conf.sn){
                repTypeMaxSnMapNew.put(InstanceConstants.LEAGUESOLOCHAPTER_6, conf.sn);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.LEAGUESOLOCHAPTER_6, 0);
            if(levelMin > conf.sn){
                repTypeMinSnMapNew.put(InstanceConstants.LEAGUESOLOCHAPTER_6, conf.sn);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.LEAGUESOLOCHAPTER_6);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.LEAGUESOLOCHAPTER_6, levelSnMap);
            }
            levelSnMap.put(conf.sn, conf.sn);
        }
        // 讨伐郁郁胖头鱼LeagueGveChapter表
        for(ConfLeagueGveChapter conf : ConfLeagueGveChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.LEAGUEGVECHAPTER_7, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.LEAGUEGVECHAPTER_7, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.LEAGUEGVECHAPTER_7, 0);
            if(levelMin > conf.sn){
                repTypeMinSnMapNew.put(InstanceConstants.LEAGUEGVECHAPTER_7, conf.sn);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.LEAGUEGVECHAPTER_7);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.LEAGUEGVECHAPTER_7, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        // 守卫残垣古城LegacyTeamChapter表(组队)
        for(ConfLegacyTeamChapter conf : ConfLegacyTeamChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.LEGACYTEAMCHAPTER_8, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.LEGACYTEAMCHAPTER_8, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.LEGACYTEAMCHAPTER_8, 0);
             if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.LEGACYTEAMCHAPTER_8, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.LEGACYTEAMCHAPTER_8);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.LEGACYTEAMCHAPTER_8, levelSnMap);
            }
            if(conf.diff == 1 && conf.part == 1){
                levelSnMap.put(conf.level, conf.sn);
            }
        }
        // 颠倒时序塔MountChapter表
        for (ConfMountChapter conf : ConfMountChapter.findAll()) {
            int level = repTypeMaxSnMapNew.getOrDefault(InstanceConstants.MOUNTCHAPTER_9, 0);
            if (level < conf.level) {
                repTypeMaxSnMapNew.put(InstanceConstants.MOUNTCHAPTER_9, conf.level);
            }
            int levelMin = repTypeMinSnMapNew.getOrDefault(InstanceConstants.MOUNTCHAPTER_9, 0);
            if (levelMin == 0 || levelMin > conf.level) {
                repTypeMinSnMapNew.put(InstanceConstants.MOUNTCHAPTER_9, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.computeIfAbsent(InstanceConstants.MOUNTCHAPTER_9, k -> new HashMap<>());
            int minSn = levelSnMap.getOrDefault(conf.level, conf.sn);
            levelSnMap.put(conf.level, Math.min(conf.sn, minSn));
        }

        // 庄园内pvp副本FarmPvpChapter表
        for(ConfFarmPvpChapter conf : ConfFarmPvpChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.FARMPVPCHAPTER_10, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.FARMPVPCHAPTER_10, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.FARMPVPCHAPTER_10, 0);
            if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.FARMPVPCHAPTER_10, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.FARMPVPCHAPTER_10);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.FARMPVPCHAPTER_10, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }
        // 命格副本FateChapter表
        for(ConfFateChapter conf : ConfFateChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.FATECHAPTER_11, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.FATECHAPTER_11, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.FATECHAPTER_11, 0);
            if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.FATECHAPTER_11, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.FATECHAPTER_11);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.FATECHAPTER_11, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        // 切磋PkChapter表
        for(ConfPkChapter conf : ConfPkChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.PKCHAPTER_12, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.PKCHAPTER_12, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.PKCHAPTER_12, 0);
             if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.PKCHAPTER_12, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.PKCHAPTER_12);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.PKCHAPTER_12, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        // 穿越深渊之门WorldBoss表
//        ConfWorldBoss conf_13 = ConfWorldBoss.findBy(ConfWorldBoss.K.level, OrderBy.DESC).get(0);
//        if(conf_13 != null){
//            petTypeMaxSnMapNew.put(InstanceConstants.WORLDBOSS_13, conf_13.level);
//        }
        // 家族乱斗FamilyBrawlChapter表
        for(ConfFamilyBrawlChapter conf : ConfFamilyBrawlChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.FAMILYBRAWLCHAPTER_14, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.FAMILYBRAWLCHAPTER_14, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.FAMILYBRAWLCHAPTER_14, 0);
            if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.FAMILYBRAWLCHAPTER_14, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.FAMILYBRAWLCHAPTER_14);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.FAMILYBRAWLCHAPTER_14, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }


        // 跨服排位赛CrossPvpChapter表
        for(ConfCrossPvpChapter conf : ConfCrossPvpChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.CROSSPVPCHAPTER_15, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.CROSSPVPCHAPTER_15, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.CROSSPVPCHAPTER_15, 0);
            if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.CROSSPVPCHAPTER_15, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.CROSSPVPCHAPTER_15);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.CROSSPVPCHAPTER_15, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        // 菇生模拟器StrategyActivityChapter表
        for(ConfStrategyActivityChapter conf : ConfStrategyActivityChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.STRATEGYACTIVITYCHAPTER_16, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.STRATEGYACTIVITYCHAPTER_16, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.STRATEGYACTIVITYCHAPTER_16, 0);
            if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.STRATEGYACTIVITYCHAPTER_16, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.STRATEGYACTIVITYCHAPTER_16);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.STRATEGYACTIVITYCHAPTER_16, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        // 跨服玩家战斗KfWarChapter表
        for(ConfKfWarChapter conf : ConfKfWarChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.KFWARCHAPTER_17, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.KFWARCHAPTER_17, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.KFWARCHAPTER_17, 0);
             if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.KFWARCHAPTER_17, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.KFWARCHAPTER_17);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.KFWARCHAPTER_17, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        // 跨服怪物战斗KfWarChapterMonster表
        for(ConfKfWarMonsterChapter conf : ConfKfWarMonsterChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.KFWARCHAPTERMONSTER_18, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.KFWARCHAPTERMONSTER_18, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.KFWARCHAPTERMONSTER_18, 0);
             if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.KFWARCHAPTERMONSTER_18, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.KFWARCHAPTERMONSTER_18);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.KFWARCHAPTERMONSTER_18, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        // 车位PVP副本ParkPvpChapter表
        for(ConfParkPvpChapter conf : ConfParkPvpChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.PARKPVPCHAPTER_19, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.PARKPVPCHAPTER_19, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.PARKPVPCHAPTER_19, 0);
             if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.PARKPVPCHAPTER_19, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.PARKPVPCHAPTER_19);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.PARKPVPCHAPTER_19, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        // 万圣节PVP副本HalloweenPvp表
        for(ConfHalloweenPvp conf : ConfHalloweenPvp.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.HALLOWEENPVP_20, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.HALLOWEENPVP_20, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.HALLOWEENPVP_20, 0);
             if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.HALLOWEENPVP_20, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.HALLOWEENPVP_20);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.HALLOWEENPVP_20, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }


        // 神器预览副本ArtifactPreviewChapter表
        for(ConfArtifactPreviewChapter conf : ConfArtifactPreviewChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.ARTIFACTPREVIEWCHAPTER_21, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.ARTIFACTPREVIEWCHAPTER_21, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.ARTIFACTPREVIEWCHAPTER_21, 0);
            if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.ARTIFACTPREVIEWCHAPTER_21, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.ARTIFACTPREVIEWCHAPTER_21);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.ARTIFACTPREVIEWCHAPTER_21, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }
        // 探秘焚焰神殿ArtifactGemChapter表
        for(ConfArtifactGemChapter conf : ConfArtifactGemChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.ARTIFACTGEMCHAPTER_22, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.ARTIFACTGEMCHAPTER_22, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.ARTIFACTGEMCHAPTER_22, 0);
            if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.ARTIFACTGEMCHAPTER_22, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.ARTIFACTGEMCHAPTER_22);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.ARTIFACTGEMCHAPTER_22, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        // 恶魔入侵SevenTrialChapter表
        for(ConfSevenTrialChapter conf : ConfSevenTrialChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.SEVENTRIALCHAPTER_23, 0);
            if(level < conf.sn){
                repTypeMaxSnMapNew.put(InstanceConstants.SEVENTRIALCHAPTER_23, conf.sn);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.SEVENTRIALCHAPTER_23, 0);
            if(levelMin == 0 || levelMin > conf.sn){
                repTypeMinSnMapNew.put(InstanceConstants.SEVENTRIALCHAPTER_23, conf.sn);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.SEVENTRIALCHAPTER_23);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.SEVENTRIALCHAPTER_23, levelSnMap);
            }
            levelSnMap.put(conf.sn, conf.sn);
        }

//        int CAPTURESLAVE_25 = 25;// 抓仆人CaptureSlave表
//        for(ConfCaptureSlave conf : ConfCaptureSlave.findAll()){
//            int level =  petTypeMaxSnMapNew.getOrDefault(InstanceConstants.CAPTURESLAVE_25, 0);
//            if(level < conf.level){
//                petTypeMaxSnMapNew.put(InstanceConstants.CAPTURESLAVE_25, conf.level);
//            }
//        }

        // 跨服车位PVP副本ParkCrossPvpChapter表
        for(ConfParkCrossPvpChapter conf : ConfParkCrossPvpChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.PARKCROSSPVPCHAPTER_26, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.PARKCROSSPVPCHAPTER_26, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.PARKCROSSPVPCHAPTER_26, 0);
            if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.PARKCROSSPVPCHAPTER_26, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.PARKCROSSPVPCHAPTER_26);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.PARKCROSSPVPCHAPTER_26, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        // 逆转之战ReversionWarChapter表
        for(ConfReversionWarChapter conf : ConfReversionWarChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.REVERSIONWARCHAPTER_27, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.REVERSIONWARCHAPTER_27, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.REVERSIONWARCHAPTER_27, 0);
            if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.REVERSIONWARCHAPTER_27, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.REVERSIONWARCHAPTER_27);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.REVERSIONWARCHAPTER_27, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        Map<Integer, Map<Integer, Integer>> levelTypeMapNew = new HashMap<>();
        darkTrialTypeDiffSnMap.clear();
        // 暗黑试炼-战士DarkTrialChapter表
        for(ConfDarkTrialChapter conf : ConfDarkTrialChapter.findAll()){
            Map<Integer, Integer> typeSnMap = levelTypeMapNew.get(conf.level);
            if(typeSnMap == null){
                typeSnMap = new HashMap<>();
                levelTypeMapNew.put(conf.level, typeSnMap);
            }
            typeSnMap.put(conf.part_type, conf.sn);

            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(conf.part_type);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(conf.part_type, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);


            int level =  repTypeMaxSnMapNew.getOrDefault(conf.part_type, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(conf.part_type, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(conf.part_type, 0);
            if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(conf.part_type, conf.level);
            }

            // 计算每个副本类型和难度对应的最大星级的副本sn
            Map<Integer, Integer> diffSnMap = darkTrialTypeDiffSnMap.computeIfAbsent(conf.part_type, k -> new HashMap<>());
            int oldSn = diffSnMap.getOrDefault(conf.difficulty, 0);
            if (oldSn == 0 || conf.sn > oldSn) {
                diffSnMap.put(conf.difficulty, conf.sn);
            }
        }
        darkTrialLevelTypeMap = Collections.unmodifiableMap(levelTypeMapNew);


        // 航海pvp战斗SeasonPvpChapter表
        for(ConfSeasonPvpChapter conf : ConfSeasonPvpChapter.findAll()){
            int level =  repTypeMaxSnMapNew.getOrDefault(InstanceConstants.SEASONPVPCHAPTER_31, 0);
            if(level < conf.level){
                repTypeMaxSnMapNew.put(InstanceConstants.SEASONPVPCHAPTER_31, conf.level);
            }
            int levelMin =  repTypeMinSnMapNew.getOrDefault(InstanceConstants.SEASONPVPCHAPTER_31, 0);
            if(levelMin == 0 || levelMin > conf.level){
                repTypeMinSnMapNew.put(InstanceConstants.SEASONPVPCHAPTER_31, conf.level);
            }
            Map<Integer,Integer> levelSnMap = repTypeLevelSnMapNew.get(InstanceConstants.SEASONPVPCHAPTER_31);
            if(levelSnMap == null){
                levelSnMap = new HashMap<>();
                repTypeLevelSnMapNew.put(InstanceConstants.SEASONPVPCHAPTER_31, levelSnMap);
            }
            levelSnMap.put(conf.level, conf.sn);
        }

        repTypeMaxSnMap = Collections.unmodifiableMap(repTypeMaxSnMapNew);
        repTypeMinSnMap = Collections.unmodifiableMap(repTypeMinSnMapNew);
        repTypeLevelSnMap = Collections.unmodifiableMap(repTypeLevelSnMapNew);

        Map<Integer, int[]> repTypeCostMapNew = new HashMap<>();
        Map<Integer, Integer> repTypeNumMapNew = new HashMap<>();


        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.res_chapter.SN);
        String[] arrStr = Utils.strToStrArray(confGlobal.strValue, "\\|");
        for(int i = 0; i < arrStr.length; i++){
            String[] str = Utils.strToStrArray(arrStr[i], "\\,");
            if(str == null || str.length < 3){
                Log.temp.error("===ConfGlobal， res_chapter错误， strValue={}", confGlobal.strValue);
                continue;
            }
            int repType = Utils.intValue(str[0]);
            int[] arrInt = Utils.strToIntArray(str[2], "\\_");
            repTypeCostMapNew.put(repType, arrInt);
            repTypeNumMapNew.put(repType, Utils.intValue(str[1]));
        }
        repTypeCostMap = Collections.unmodifiableMap(repTypeCostMapNew);
        repTypeNumMap = Collections.unmodifiableMap(repTypeNumMapNew);
    }


    public static int getRepSn(int type, int level){
        if(level == 0){
            level = 1;
            Log.temp.warn("===需要检查代码，什么情况会出现。level=0， 修正为1， type={}, level={}", type, level);
        }
        // 目前好像都是一个level对应一个sn
        Map<Integer, Integer> levelSnMap = repTypeLevelSnMap.get(type);
        if(levelSnMap == null || !levelSnMap.containsKey(level)){
            Log.temp.error("===levelSnMap为空， type={}, level={}", type, level);
            return level;
        }
        return levelSnMap.get(level);

//        switch (type){
//            case InstanceConstants.COINCHAPTER_2:
//            case InstanceConstants.DIAMONDCHAPTER_3:
//            case InstanceConstants.LEGACY_CHAPTER_4:
//            case InstanceConstants.PVPCHAPTER_5:
//            case InstanceConstants.LEAGUESOLOCHAPTER_6:
//            case InstanceConstants.LEAGUEGVECHAPTER_7:
//            case InstanceConstants.LEGACYTEAMCHAPTER_8:
//            case InstanceConstants.MOUNTCHAPTER_9:
//            case InstanceConstants.FARMPVPCHAPTER_10:
//            case InstanceConstants.FATECHAPTER_11:
//            case InstanceConstants.PKCHAPTER_12:
//            case InstanceConstants.WORLDBOSS_13:
//            case InstanceConstants.FAMILYBRAWLCHAPTER_14:
//            case InstanceConstants.CROSSPVPCHAPTER_15:
//            case InstanceConstants.STRATEGYACTIVITYCHAPTER_16:
//            case InstanceConstants.KFWARCHAPTER_17:
//            case InstanceConstants.KFWARCHAPTERMONSTER_18:
//            case InstanceConstants.PARKPVPCHAPTER_19:
//            case InstanceConstants.HALLOWEENPVP_20:
//            case InstanceConstants.ARTIFACTPREVIEWCHAPTER_21:
//            case InstanceConstants.ARTIFACTGEMCHAPTER_22:
//            case InstanceConstants.SEVENTRIALCHAPTER_23:
//            case InstanceConstants.CAPTURESLAVE_25:
//            case InstanceConstants.PARKCROSSPVPCHAPTER_26:
//            case InstanceConstants.REVERSIONWARCHAPTER_27:
//            case InstanceConstants.DARKTRIALCHAPTER_28:
//            case InstanceConstants.DARKTRIALCHAPTER_29:
//            case InstanceConstants.DARKTRIALCHAPTER_30:
//            case InstanceConstants.SEASONPVPCHAPTER_31:
//                break;
//            default:
//                Log.temp.error("===未知的副本类型， type={}， level={}", type, level);
//                break;
//        }
//        return 0;
    }

    /**
     * 根据副本类型获取回复数据次数
     * <AUTHOR>
     * @Date 2024/5/14
     * @Param
     */
    public static int getRepTypeNum(int type){
        if(repTypeNumMap.containsKey(type)){
            return repTypeNumMap.get(type);
        }
        return 0;
    }

    public static int[] getRepCost(int type){
        if(repTypeCostMap.containsKey(type)){
            int[] costArray = repTypeCostMap.get(type);
            return Arrays.copyOf(costArray, costArray.length);
        }
        return null;
    }

    public static int getRepTypeMaxLv(int type){
        if(repTypeMaxSnMap.containsKey(type)){
            return repTypeMaxSnMap.get(type);
        }
        return 0;
    }

    /**
     * 读取游戏配置
     */
    private static String _readConfFile(String name) {
        String result = "";
        BufferedReader reader = null;

        try {
            String filePath = Thread.currentThread().getContextClassLoader().getResource(name).getPath();
            InputStream is = new FileInputStream(filePath);
            reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            String tempString = "";
            while ((tempString = reader.readLine()) != null) {
                result += tempString;
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (reader != null)
                try {
                    reader.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
        }

        return result;
    }

    private static void reloadPetLv() {
        Map<Integer, Integer> petSnMaxLvMapNew = new HashMap<>();
        for (ConfPetlevel_0 conf : ConfPetlevel_0.findAll()) {
            int lv = Utils.intValue(petSnMaxLvMapNew.get(conf.id));
            if (lv < conf.level) {
                petSnMaxLvMapNew.put(conf.id, conf.level);
            }
        }
        petSnMaxLvMap = Collections.unmodifiableMap(petSnMaxLvMapNew);
    }

    public static int getPetMaxLv(int petSn) {
        return Utils.intValue(petSnMaxLvMap.get(petSn));
    }

    private static void reloadSkillLv() {
        Map<Integer, Integer> skillSnMaxLvMapNew = new HashMap<>();
        for (ConfSkillLevel_0 conf : ConfSkillLevel_0.findAll()) {
            int lv = Utils.intValue(skillSnMaxLvMapNew.get(conf.id));
            if (lv < conf.level) {
                skillSnMaxLvMapNew.put(conf.id, conf.level);
            }
        }
        skillSnMaxLvMap = Collections.unmodifiableMap(skillSnMaxLvMapNew);
    }

    public static int getSkillMaxLv(int skillSn) {
        return Utils.intValue(skillSnMaxLvMap.get(skillSn));
    }

    private static void reloadConfUnit() {
        int jobSn = ConfGlobal.get(ConfGlobalKey.默认职业.SN).value;
        ConfJobs confJobs = ConfJobs.get(jobSn);
        ConfUnit confUnit = ConfUnit.get(Utils.longValue(confJobs.model));

//        Log.temp.info("==={}", GlobalConfVal.getPropCalc(confUnit).toJSONStr());
    }

    public static Map<Integer, Integer> getPropReward(int repSn, boolean isFirst){
        Map<Integer, Integer> propRewardMap = new WeakHashMap<>();
        int index = repSn % 500;
        int value = repSn / 500 + (index != 0 ? 1 : 0);

        String confJSON;
        try {
            confJSON = _readConfFile(Utils.createStr("ConfChapter{}.json", value));
        } catch (Exception e) {
            Log.temp.error("读取配置文件失败: {}", e.getMessage());
            return propRewardMap;
        }
        if (StringUtils.isBlank(confJSON)) return propRewardMap;

        //填充实体数据
        JSONArray confs = Utils.toJSONArray(confJSON);
        JSONObject conf = null;
        int size = confs.size();
        if (index < size) {
            conf = confs.getJSONObject(index);
        }
        if(conf == null || conf.getIntValue("sn") != repSn){
            //填充实体数据
            for (int i = 0; i < size; i++) {
                conf = confs.getJSONObject(i);
                if(conf.getIntValue("sn") == repSn){
                    break;
                }
            }
        }
        if(conf == null){
            return propRewardMap;
        }
        if(isFirst) {
            // 给首通奖励
            Map<Integer, Integer> dropAllMap = ProduceManager.inst().producePreDrop(Utils.parseIntArray2(conf.getString("offline_reward2")));
            propRewardMap = Utils.intArrToIntMap(dropAllMap, Utils.parseIntArray2(conf.getString("offline_reward1")));
        } else {
            Map<Integer, Integer> dropAllMap = ProduceManager.inst().producePreDrop(Utils.parseIntArray2(conf.getString("online_reward2")));
            propRewardMap = Utils.intArrToIntMap(dropAllMap, Utils.parseIntArray2(conf.getString("online_reward1")));
        }
        return propRewardMap;
    }

    public static int[][] getCalculateReward(int repSn, int type){
        int index = repSn % 500;
        int value = repSn / 500 + (index != 0 ? 1 : 0);

        ConfChapterVO confChapterVO = getConfChapterVO(repSn);
        if(confChapterVO == null){
            return new int[0][0];
        }
        //填充实体数据
        if(type == ParamKey.rep_online_reward1) {
            return confChapterVO.online_reward1;
        } else if(type == ParamKey.rep_online_reward2) {
            return confChapterVO.online_reward2;
        } else if(type == ParamKey.rep_offline_reward3) {
            return confChapterVO.offline_reward1;
        } else if(type == ParamKey.rep_offline_reward4) {
            return confChapterVO.offline_reward2;
        }
        return new int[0][0];
    }

    public static ConfChapterVO getConfChapterVO(int repSn) {
        if(chapterVoMap.containsKey(repSn)){
            ConfChapterVO vo = chapterVoMap.get(repSn);
            if (vo != null) {
                return vo;
            }
        }
        return null;
    }

    private static void reloadGoods() {
        mountGoodsSnList.clear();
        artifactGoodsSnList.clear();
        wingGoodsSnList.clear();

        Map<Integer, Integer> itemSnClassifyMapNew = new HashMap<>();
        Map<Integer, List<Integer>> typeList = new HashedMap();

        Map<Integer, Integer> petSnItemSnMapNew = new HashMap<>();
        Map<Integer, Integer> skillSnItemSnMapNew = new HashMap<>();

        Map<Integer, Integer> itemSnEmojiSnMapNew = new HashMap<>();
        Map<Integer, Integer> warTokenRecycleItemMapNew = new HashMap<>();

        Map<Integer, List<Integer>> itemQualityGoodsSnListMapNew = new HashMap<>();

        for (ConfGoods confGoods : ConfGoods.findAll()) {
            itemSnClassifyMapNew.put(confGoods.sn, confGoods.classify);
            List<Integer> snList = typeList.get(confGoods.classify);
            if (snList == null) {
                snList = new ArrayList<>();
                typeList.put(confGoods.classify, snList);
            }
            if(confGoods.type == ItemConstants.技能){
                skillSnItemSnMapNew.put(confGoods.effect[0][0], confGoods.sn);
            } else if(confGoods.type == ItemConstants.同伴){
                petSnItemSnMapNew.put(confGoods.effect[0][0], confGoods.sn);
            }
            snList.add(confGoods.sn);

            if(confGoods.type == ItemConstants.表情包){
                if(confGoods.effect == null || confGoods.effect[0].length != 2){
                    Log.temp.error("表情包,物品effect错误， sn={}", confGoods.sn);
                }
                itemSnEmojiSnMapNew.put(confGoods.sn, confGoods.effect[0][0]);
            }
            if(confGoods.type == ItemConstants.战令){
                if(confGoods.effect == null || confGoods.effect.length != 2){
                    Log.temp.error("战令,物品effect错误， sn={}", confGoods.sn);
                }
                ConfGoods confValue = ConfGoods.get(confGoods.effect[0][0]);
                if(confValue != null && confValue.effect.length>0){
                    warTokenRecycleItemMapNew.put(confValue.effect[0][0], confGoods.sn);
                }
            }
            if (confGoods.type == ItemConstants.坐骑皮肤) {
                mountGoodsSnList.add(confGoods.sn);
            } else if (confGoods.type == ItemConstants.神器皮肤) {
                artifactGoodsSnList.add(confGoods.sn);
            } else if (confGoods.type == ItemConstants.背饰皮肤) {
                wingGoodsSnList.add(confGoods.sn);
            } else {
                List<Integer> goodsSnList =itemQualityGoodsSnListMapNew.get(confGoods.quality);
                if(goodsSnList == null){
                    goodsSnList = new ArrayList<>();
                    itemQualityGoodsSnListMapNew.put(confGoods.quality, goodsSnList);
                }
                goodsSnList.add(confGoods.sn);
            }


        }
        itemSnClassifyMap = Collections.unmodifiableMap(itemSnClassifyMapNew);
        skillSnItemMap = Collections.unmodifiableMap(skillSnItemSnMapNew);
        petSnItemMap = Collections.unmodifiableMap(petSnItemSnMapNew);
        itemSnEmojiSnMap = Collections.unmodifiableMap(itemSnEmojiSnMapNew);
    }


    public static int getPetSnToItemSn(int petSn){
        return petSnItemMap.getOrDefault(petSn, 0);
    }

    public static int getSkillSnToItemSn(int skillSn){
        return skillSnItemMap.getOrDefault(skillSn, 0);
    }

    private static void reloadTaskCondition() {
        for (ConfCondition conf : ConfCondition.findAll()) {
            taskConditionTypeMap.put(conf.sn, new TaskTypeData(conf.sn).getInstance());
        }
    }

    /**
     * 属性表
     *
     * <AUTHOR>
     * @Date 2024/2/26
     * @Param
     */
    public static void reloadAttribute() {
        Map<Integer, String> propSnStringMapNew = new HashMap<>();
        Map<String, Integer> propNameSnMapNew = new HashMap<>();
        Map<String, Integer> propNameSnModule1MapNew = new HashMap<>();
        Map<Integer, Float> propSnCombatMapNew = new HashMap<>();
        Map<Integer, Float> propSnQualityCombatMapNew = new HashMap<>();
        for (ConfAttribute conf : ConfAttribute.findAll()) {
            propSnStringMapNew.put(conf.sn, conf.key);
            propNameSnMapNew.put(conf.key, conf.sn);
            if (conf.module == 1) {
                propNameSnModule1MapNew.put(conf.key, conf.sn);
            }
            propSnCombatMapNew.put(conf.sn, conf.value);
            propSnQualityCombatMapNew.put(conf.sn, conf.EquipmentValue);
        }
        propSnStringMap = Collections.unmodifiableMap(propSnStringMapNew);
        propNameSnMap = Collections.unmodifiableMap(propNameSnMapNew);
        propNameSnModule1Map = Collections.unmodifiableMap(propNameSnModule1MapNew);
        propSnCombatMap = Collections.unmodifiableMap(propSnCombatMapNew);
        propSnQualityCombatMap = Collections.unmodifiableMap(propSnQualityCombatMapNew);
    }


    public static float getCombatCoefficient(int propSn) {
        if (propSnCombatMap.containsKey(propSn)) {
            return propSnCombatMap.get(propSn);
        }
        return 0;
    }

    public static float getPropSnQualityCombatMap(int propSn) {
        if (propSnQualityCombatMap.containsKey(propSn)) {
            return propSnQualityCombatMap.get(propSn);
        }
        return 0;
    }

    public static int getModule1PropSn(String propName) {
        if (propNameSnModule1Map.containsKey(propName)) {
            return propNameSnModule1Map.get(propName);
        }
        return 0;
    }

    public static int getPropSn(String propName) {
        if (propNameSnMap.containsKey(propName)) {
            return propNameSnMap.get(propName);
        }
        return 0;
    }


    public static void reloadGlobal() {
        MAX_LEVEL = ConfGlobal.get(ConfGlobalKey.角色最大等级.SN).value;
        defaultNameHead = ConfGlobal.get(ConfGlobalKey.默认名字规则.SN).strValue;
        guildFamilySignupNum = ConfGlobal.get(ConfGlobalKey.familybattle_signup_condition.SN).value;

        arenaDay = ConfGlobal.get(ConfGlobalKey.pvp_season_duration.SN).value;
        ConfGlobal conf = ConfGlobal.get(ConfGlobalKey.pvp_k.SN);
        arenaHistoryNum = conf.value;
        historySec = conf.intArray[0];
        arenaBridgeHistoryNum = ConfGlobal.get(ConfGlobalKey.cross_pvp_k.SN).value;

        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.legacy_team_chapter_match_teammates.SN);
        teamMatch = Utils.strToIntArray(confGlobal.strValue, "\\|");
        teamMatchNum = Utils.arrayStrToInt(confGlobal.strArray);
        teamPro = confGlobal.intArray;

        ConfGlobal confCross = ConfGlobal.get(ConfGlobalKey.familybattle_cross_server.SN);
        if(confCross.intArray == null || confCross.intArray.length != 2){
            Log.temp.error("familybattle_cross_server配置错误, sn=familybattle_cross_server, intArray有误");
        } else {
            crossServerIdTime = Utils.formatTimeToLong(confCross.strValue);
            crossServerIdRange[0] = Utils.intValue(Config.GAME_SERVER_PREFIX) + confCross.intArray[0];
            crossServerIdRange[1] = Utils.intValue(Config.GAME_SERVER_PREFIX) + confCross.intArray[1];
        }
		Map<Integer, Map<Integer, Integer>> quizDiceRewardMapNew = new HashMap<>();
        String quizDiceRewardStr = ConfGlobal.get(ConfGlobalKey.quiz_dice_reward.SN).strValue;
        String[] quizDiceRewardArray = Utils.strToStrArray(quizDiceRewardStr, "\\|");
        for (String str : quizDiceRewardArray) {
            String[] splitStr = Utils.strToStrArray(str, "\\,");
            if (splitStr == null || splitStr.length != 2) {
                Log.temp.error("===ConfGlobal， quiz_dice_reward 错误， strValue={}", str);
                continue;
            }
            int point = Utils.intValue(splitStr[0]);
            String[] rewardArray = Utils.strToStrArray(splitStr[1], "\\;");
            Map<Integer, Integer> rewardMap = new HashMap<>();
            for(int i = 0; i < rewardArray.length; i++) {
                int[] arrInt = Utils.strToIntArray(rewardArray[i], "\\_");
                rewardMap.put(arrInt[0], arrInt[1]);
            }
            quizDiceRewardMapNew.put(point, rewardMap);
        }
        quizDiceRewardMap = Collections.unmodifiableMap(quizDiceRewardMapNew);
//        Log.temp.info("quizDiceRewardMap={}", quizDiceRewardMap);

        Map<Integer, Map<Integer, Integer>> quizRankRewardMapNew = new HashMap<>();
        String quizRankRewardStr = ConfGlobal.get(ConfGlobalKey.quiz_rank_reward.SN).strValue;
        String[] quizRankRewardArray = Utils.strToStrArray(quizRankRewardStr, "\\|");
        for (String str : quizRankRewardArray) {
            String[] splitStr = Utils.strToStrArray(str, "\\,");
            if (splitStr == null || splitStr.length != 2) {
                Log.temp.error("===ConfGlobal， quiz_rank_reward 错误， strValue={}", str);
                continue;
            }
            int point = Utils.intValue(splitStr[0]);
            String[] rewardArray = Utils.strToStrArray(splitStr[1], "\\;");
            Map<Integer, Integer> rewardMap = new HashMap<>();
            for(int i = 0; i < rewardArray.length; i++) {
                int[] arrInt = Utils.strToIntArray(rewardArray[i], "\\_");
                rewardMap.put(arrInt[0], arrInt[1]);
            }
            quizRankRewardMapNew.put(point, rewardMap);
        }
        quizRankRewardMap = Collections.unmodifiableMap(quizRankRewardMapNew);
//        Log.temp.info("quizRankRewardMap={}", quizRankRewardMap);

        Map<Integer, Integer> quizRankRewardExpMapNew = new HashMap<>();
        String quizRankRewardExpStr = ConfGlobal.get(ConfGlobalKey.quiz_rank_reward_exp.SN).strValue;
        String[] quizRankRewardExpArray = Utils.strToStrArray(quizRankRewardExpStr, "\\|");
        for (String str : quizRankRewardExpArray) {
            String[] splitStr = Utils.strToStrArray(str, "\\,");
            if (splitStr == null || splitStr.length != 2) {
                Log.temp.error("===ConfGlobal， quiz_rank_reward_exp 错误， strValue={}", str);
                continue;
            }
            int rank = Utils.intValue(splitStr[0]);
            quizRankRewardExpMapNew.put(rank, Utils.intValue(splitStr[1]));
        }
        quizRankRewardExpMap = Collections.unmodifiableMap(quizRankRewardExpMapNew);
//        Log.temp.info("quizRankRewardExpMap={}", quizRankRewardExpMap);

        skillComposeOutputMap.clear();
        for (String s : ConfGlobal.get(ConfGlobalKey.技能满级物品合成消耗数量.SN).strArray) {
            String[] strs = s.split(",");
            skillComposeOutputMap.put(Utils.intValue(strs[0]), Utils.intValue(strs[1]));
        }
        petComposeOutputMap.clear();
        for (String s : ConfGlobal.get(ConfGlobalKey.伙伴满级物品合成消耗数量.SN).strArray) {
            String[] strs = s.split(",");
            petComposeOutputMap.put(Utils.intValue(strs[0]), Utils.intValue(strs[1]));
        }
    }

    public static boolean isLeagueCross(int serverId){
        // 生效时间
        if(crossServerIdTime > 0 && Port.getTime() >= crossServerIdTime){
            if(crossServerIdRange[0] <= serverId && serverId <= crossServerIdRange[1]){
                return true;
            }
        }
        return false;
    }

    /**
     * 装备表分组
     *
     * <AUTHOR>
     * @Date 2024/3/5
     * @Param
     */
    public static void reloadEquipment() {
        // 只管装备
        Map<Integer, Map<RangeInt, Map<Integer, Integer>>> confEquipGradeSnMapNew = new HashMap<>();
        Map<Integer, Map<RangeInt, Map<Integer, Integer>>> activityEquipmentMapNew = new HashMap<>();
        
        for (ConfEquipment conf : ConfEquipment.findAll()) {
            if(conf.gradeRange == null || conf.gradeRange.length != 2){
                Log.temp.error("ConfEquipment装备表分组,装备等级区间错误， sn={}", conf.sn);
                continue;
            }
            // 1-150级
            if(conf.gradeRange[0] > MAX_LEVEL || conf.gradeRange[1] > MAX_LEVEL){
                continue;
            }
            
            // 处理活动装备
            if (conf.act_id > 0) {
                // 活动装备，添加到活动装备索引
                if (!activityEquipmentMapNew.containsKey(conf.act_id)) {
                    activityEquipmentMapNew.put(conf.act_id, new HashMap<>());
                }
                
                Map<RangeInt, Map<Integer, Integer>> gradeSnMap = activityEquipmentMapNew.get(conf.act_id);
                RangeInt rangeInt = new RangeInt(conf.gradeRange[0], conf.gradeRange[1]);
                
                if (!gradeSnMap.containsKey(rangeInt)) {
                    gradeSnMap.put(rangeInt, new HashMap<>());
                }
                
                Map<Integer, Integer> snQualityMap = gradeSnMap.get(rangeInt);
                snQualityMap.put(conf.sn, conf.quality);
                
                continue; // 跳过活动装备，不添加到普通装备索引
            }
            
            // 普通装备处理逻辑保持不变
            if(!confEquipGradeSnMapNew.containsKey(conf.job)){
                confEquipGradeSnMapNew.put(conf.job, new HashMap<>());
            }
            Map<RangeInt, Map<Integer, Integer>> gradeSnMap = confEquipGradeSnMapNew.get(conf.job);
            RangeInt rangeInt = new RangeInt(conf.gradeRange[0], conf.gradeRange[1]);
            if(!gradeSnMap.containsKey(rangeInt)){
                gradeSnMap.put(rangeInt, new HashMap<>());
            }
            Map<Integer, Integer> snQualityMap = gradeSnMap.get(rangeInt);
            snQualityMap.put(conf.sn, conf.quality);
        }
        
        // 保存活动装备索引
        activityEquipmentMap = Collections.unmodifiableMap(activityEquipmentMapNew);
        
        confEquipGradeSnMap = Collections.unmodifiableMap(confEquipGradeSnMapNew);
    }

    public static List<ConfEquipmentAttr> getConfEquipGroupAttrList(int groupId) {
        return confEquipGroupAttrListMap.computeIfAbsent(groupId, k -> new ArrayList<>());
    }

    public static void reloadConfEquipGroupAttr() {
        Map<Integer, List<ConfEquipmentAttr>> confEquipGroupAttrListMapNew = new HashMap<>();
        for (ConfEquipmentAttr conf : ConfEquipmentAttr.findAll()) {
            int key = conf.groupId;
            if (!confEquipGroupAttrListMapNew.containsKey(key)) {
                confEquipGroupAttrListMapNew.put(key, new ArrayList<>());
            }
            confEquipGroupAttrListMapNew.get(key).add(conf);
        }
        confEquipGroupAttrListMap = Collections.unmodifiableMap(confEquipGroupAttrListMapNew);
    }

    /**
     * 装备表等级区间分组
     *
     * <AUTHOR>
     * @Date 2024/3/5
     * @Param
     */
    private static int[] getEquipmentLvGrade(int[] gradeRange) {
        //根据等级区间分组1到10级的，1级一组,大于10的10级一组
        int[] grade;
        if (gradeRange[0] < 10) {
            grade = new int[gradeRange[1] - gradeRange[0] + 1];
            if (gradeRange[0] < 1 || gradeRange[1] > 10) {
                if(S.isTestLog){
                    Log.game.error("装备表等级区间分组错误");
                }
            }
            for (int i = gradeRange[0], j = 0; i <= gradeRange[1]; i++, j++) {
                grade[j] = i;
            }
        } else {
            grade = new int[1];
            grade[0] = gradeRange[0];
        }
        return grade;
    }

    public static Set<Integer> getConfEquipments(int job, int grade, int quality) {
        if(grade > MAX_LEVEL){
            grade = MAX_LEVEL;
        }
        Map<RangeInt, Map<Integer, Integer>> gradeEquipmentsMap = confEquipGradeSnMap.getOrDefault(job, new HashMap<>());
        Set<Integer> snList = new HashSet<>();
        for(Map.Entry<RangeInt, Map<Integer, Integer>> entry : gradeEquipmentsMap.entrySet()) {
            RangeInt rangeInt = entry.getKey();
            if (rangeInt.min <= grade && grade <= rangeInt.max) {
                for(Map.Entry<Integer, Integer> entry2 : entry.getValue().entrySet()){
                    if(entry2.getValue() == quality){
                        snList.add(entry2.getKey());
                    }
                }
            }
        }
        return snList;
    }

    public static void reloadTypePayMallMap() {
        Map<Integer, List<ConfPayMall>> typePayMallMapNew = new HashMap<>();
        Set<Integer> snSet = new HashSet<>();
        for (ConfPayMall conf : ConfPayMall.findBy(ConfPayMall.K.sn, OrderBy.ASC)) {
            if (!typePayMallMapNew.containsKey(conf.type)) {
                typePayMallMapNew.put(conf.type, new ArrayList<>());
            }
            List<ConfPayMall> payMalls = typePayMallMapNew.get(conf.type);
            payMalls.add(conf);
            if(conf.conditon != null && !conf.conditon.isEmpty()){
                String[] actStr = Utils.splitStr(conf.conditon, "\\|");
                if(actStr.length > 0 && PayMallTypeKey.pay_condition_shortage.equals(actStr[0])){
                    snSet.add(Utils.intValue(actStr[1]));
                }
            }
        }
        itemSnSet = Collections.unmodifiableSet(snSet);
        typePayMallMap = Collections.unmodifiableMap(typePayMallMapNew);
    }

    public static List<ConfPayMall> getConfPayMalls(int type) {
        if (!typePayMallMap.containsKey(type)) {
            return new ArrayList<>(0);
        }
        return typePayMallMap.get(type);
    }

    public static void reloadConfMountDrawMap() {
        confMountDrawMap = ConfMountDraw.findBy(ConfMountDraw.K.sn, OrderBy.ASC)
                .stream()
                .collect(Collectors.groupingBy(conf -> conf.type + "|" + conf.group_id));
        confMountDrawMap = confMountDrawMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> Collections.unmodifiableList(e.getValue())));
    }

    public static List<ConfMountDraw> getConfMountDraws(int type, int groupId) {
        return confMountDrawMap.get(type + "|" + groupId);
    }

    public static void reloadConfMountDrawCumulativeTimesMap() {
        confMountDrawCumulativeTimesMap = ConfMountDrawCumulativeTimes.findBy(ConfMountDrawCumulativeTimes.K.sn, OrderBy.ASC)
                .stream()
                .collect(Collectors.groupingBy(conf -> conf.type + "|" + conf.group_id));
        confMountDrawCumulativeTimesMap = confMountDrawCumulativeTimesMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> Collections.unmodifiableList(e.getValue())));
    }

    public static List<ConfMountDrawCumulativeTimes> getConfMountDrawCumulativeTimes(int type, int groupId) {
        return confMountDrawCumulativeTimesMap.get(type + "|" + groupId);
    }

    public static void reloadConfMountDrawGuaranteedMap() {
        Map<String, ConfMountDrawGuaranteed> confMountDrawGuaranteedMapNew = new HashMap<>();
        for (ConfMountDrawGuaranteed conf : ConfMountDrawGuaranteed.findAll()) {
            String key = conf.type + "|" + conf.group_id + "|" + conf.num;
            confMountDrawGuaranteedMapNew.put(key, conf);
        }
        confMountDrawGuaranteedMap = Collections.unmodifiableMap(confMountDrawGuaranteedMapNew);
    }

    public static ConfMountDrawGuaranteed getConfMountDrawGuaranteed(int type, int groupId, int num) {
        return confMountDrawGuaranteedMap.get(type + "|" + groupId + "|" + num);
    }

    public static void reloadConfDoubleProbabillityMap() {
        confDoubleProbabillityMap = ConfDoubleProbabillity.findBy(ConfDoubleProbabillity.K.sn, OrderBy.ASC)
                .stream()
                .collect(Collectors.groupingBy(conf -> conf.turn_id ));
        confDoubleProbabillityMap = confDoubleProbabillityMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> Collections.unmodifiableList(e.getValue())));
    }

    public static List<ConfDoubleProbabillity> getConfDoubleProbabillity(int turnId) {
        return confDoubleProbabillityMap.get(turnId);
    }

    public static void reloadStatueAttr(){
        Map<Integer, List<ConfStatueAttr>> confStatueAttrMap = new HashMap<>();
        for (ConfStatueAttr conf : ConfStatueAttr.findAll()) {
            if (!confStatueAttrMap.containsKey(conf.product)) {
                confStatueAttrMap.put(conf.product, new ArrayList<>());
            }
            List<ConfStatueAttr> statueAttrs = confStatueAttrMap.get(conf.product);
            statueAttrs.add(conf);
        }
        statueQualityMap = Collections.unmodifiableMap(confStatueAttrMap);
    }

    public static List<ConfStatueAttr> getConfStatueAttrs(int quality) {
        if (!statueQualityMap.containsKey(quality)) {
            return new ArrayList<>();
        }
        return statueQualityMap.get(quality);
    }

    public static void reloadConfActivityTerm() {
        Map<Integer, List<ConfActivityTerm>> confActivityTermMapNew = new HashMap<>();
        for (ConfActivityTerm conf : ConfActivityTerm.findAll()) {
            int key = conf.type;
            if (!confActivityTermMapNew.containsKey(key)) {
                confActivityTermMapNew.put(key, new ArrayList<>());
            }
            if (conf.round_range == null || conf.round_range.length != 2) {
                Log.game.error("ConfActivityTerm配置错误: type={}round_range无效", conf.type);
                continue;
            }
            confActivityTermMapNew.get(key).add(conf);
        }
        confActivityTermMap = Collections.unmodifiableMap(confActivityTermMapNew);
    }

    public static ConfActivityTerm getConfActivityTerm(int type, int round){
        List<ConfActivityTerm> confActivityTerms = confActivityTermMap.get(type);
        if (confActivityTerms == null) {
            return null;
        }
        return confActivityTerms.stream()
                .filter(conf -> conf.round_range[0] <= round && conf.round_range[1] >= round)
                .findFirst()
                .orElse(null);
    }

    public static void reloadConfEquipmentAttr() {
        Map<Integer, List<ConfEquipmentAttr>> confEquipmentAttrMapNew = new HashMap<>();
        for (ConfEquipmentAttr conf : ConfEquipmentAttr.findAll()) {
            int key = conf.groupId;
            if (!confEquipmentAttrMapNew.containsKey(key)) {
                confEquipmentAttrMapNew.put(key, new ArrayList<>());
            }
            confEquipmentAttrMapNew.get(key).add(conf);
        }
        ConfEquipmentAttrMap = Collections.unmodifiableMap(confEquipmentAttrMapNew);
    }

    public static List<ConfEquipmentAttr> getConfEquipmentAttr(int groupId){
        return ConfEquipmentAttrMap.getOrDefault(groupId,new ArrayList<>());
    }
    private static void reloadConfChristmasPvp() {
        pvpClueMap.clear();
        for (ConfChristmasPVPclue conf : ConfChristmasPVPclue.findAll()) {
            Map<Integer, Map<Integer, List<Integer>>> roundDayMap = pvpClueMap.computeIfAbsent(conf.type, k -> new HashMap<>());
            Map<Integer, List<Integer>> dayMap = roundDayMap.computeIfAbsent(conf.group_id, k -> new HashMap<>());
            List<Integer> snList = dayMap.computeIfAbsent(conf.openday, k -> new ArrayList<>());
            snList.add(conf.sn);
        }

        pvpClueBuffMap.clear();
        for (ConfChristmasPvpBuffList conf : ConfChristmasPvpBuffList.findAll()) {
            Map<Integer, List<Integer>> roundMap = pvpClueBuffMap.computeIfAbsent(conf.type, k -> new HashMap<>());
            List<Integer> buffSnList = roundMap.computeIfAbsent(conf.group_id, k -> new ArrayList<>());
            buffSnList.add(conf.sn);
}
    }
    public static void reloadParkingDesignInitialList(){
        List<ConfParkingDesign_0> parkDesignInitList = new ArrayList<>();
        for(ConfParkingDesign_0 conf : ConfParkingDesign_0.findAll()){
           if(conf.if_initial == 1){
               parkDesignInitList.add(conf);
           }
        }
        parkDesignInitList.sort(Comparator.comparingInt(c->c.id));
        parkingDesignInitialList = Collections.unmodifiableList(parkDesignInitList);
    }

    private static void reloadConfBoxTowerLevel() {
        Map<String, List<ConfBoxTowerLevel>> confBoxTowerLevelMapNew = new HashMap<>();
        for(ConfBoxTowerLevel conf : ConfBoxTowerLevel.findAll()) {
            String key = conf.type + "|" + conf.group;
            List<ConfBoxTowerLevel> list = confBoxTowerLevelMapNew.computeIfAbsent(key, k -> new ArrayList<>());
            list.add(conf);
        }

        // 对每个List按sn排序
        for(List<ConfBoxTowerLevel> list : confBoxTowerLevelMapNew.values()) {
            list.sort(Comparator.comparingInt(a -> a.sn));
        }

        confBoxTowerLevelMap = Collections.unmodifiableMap(confBoxTowerLevelMapNew);
    }

    public static ConfBoxTowerLevel getBoxTowerLevel(int type, int groupId, int level) {
        String key = type + "|" + groupId;
        List<ConfBoxTowerLevel> list = confBoxTowerLevelMap.get(key);
        if(list == null || list.isEmpty()) {
            return null;
        }

        // level作为索引值(从1开始),所以需要-1
        int index = level - 1;
        if(index < 0) {
            return null;
        }
        // 如果超过最大值,返回最后一条
        if(index >= list.size()) {
            return list.get(list.size() - 1);
        }
        return list.get(index);
    }

    private static void reloadConfBoxTowerBox() {
        Map<String, ConfBoxTowerBox> confBoxTowerBoxMapNew = new HashMap<>();
        for (ConfBoxTowerBox conf : ConfBoxTowerBox.findAll()) {
            String key = conf.type + "|" + conf.group + "|" + conf.quality;
            confBoxTowerBoxMapNew.put(key, conf);
        }
        confBoxTowerBoxMap = Collections.unmodifiableMap(confBoxTowerBoxMapNew);
    }

    public static ConfBoxTowerBox getConfBoxTowerBox(int type, int groupId, int quality) {
        return confBoxTowerBoxMap.get(type + "|" + groupId + "|" + quality);
    }
    private static void reloadConfParkingTime() {
        List<ConfParkingTime> parkTimeAscListNew = new ArrayList<>();
        for(ConfParkingTime conf : ConfParkingTime.findAll()){
            parkTimeAscListNew.add(conf);
        }
        parkTimeAscListNew.sort(Comparator.comparingInt(a -> a.sn));
        parkingTimeList = Collections.unmodifiableList(parkTimeAscListNew);
    }

    private static void reloadConfFund() {
        fundIdRewardLevelMap.clear();
        for (ConfFundReward_0 confReward : ConfFundReward_0.findAll()) {
            List<Integer> snList = fundIdRewardLevelMap.computeIfAbsent(confReward.id, k -> new ArrayList<>());
            snList.add(confReward.level);
        }
        for (List<Integer> levelList : fundIdRewardLevelMap.values()) {
            levelList.sort(Integer::compareTo);
        }
    }

    public static int[][] getCrossWarIdleReward(long power){
        List<ConfCrossWarIdleReward> confList = new ArrayList<>(ConfCrossWarIdleReward.findAll());
        for (ConfCrossWarIdleReward conf : confList) {
            if(power >= conf.power[0] && power <= conf.power[1]){
                return conf.reward;
            }
        }
        return confList.isEmpty() ? new int[0][0] : confList.get(confList.size() - 1).reward;
    }

    private static void reloadCrossWar() {
        Map<Integer, Map<Integer, Map<Long, Long>>> crossWarAttrBonusMapNew = new HashMap<>();
        for(ConfCrossWarJob confJob : ConfCrossWarJob.findAll()){
            if(confJob.buff.isEmpty()) {
                continue;
            }
            Map<Integer, Map<Long, Long>> attrMap = new HashMap<>();
            String[] buffArray = Utils.strToStrArray(confJob.buff, "\\|");
            for (String str : buffArray) {
                List<Long> splitList = Utils.strToLongList(str);
                int targetType = Utils.intValue(splitList.remove(0));
                if (splitList.size() % 2 != 0) {
                    Log.crossWar.error("===ConfCrossWarJob， buff错误， strValue={}", str);
                    continue;
                }
                for (int i = 0; i < splitList.size(); i+=2) {
                    long key = splitList.get(i);
                    long value = splitList.get(i + 1);
                    attrMap.computeIfAbsent(targetType, k -> new HashMap<>()).put(key, value);
                }
            }
            crossWarAttrBonusMapNew.put(confJob.sn, attrMap);
        }
        crossWarAttrBonusMap =  Collections.unmodifiableMap(crossWarAttrBonusMapNew);
        Log.temp.info("crossWarAttrBonusMap={}", crossWarAttrBonusMap);

        for(ConfCrossWarPoint conf : ConfCrossWarPoint.findAll()){
            if(conf.t_object == CrossWarConst.OBJ_TYPE_SONS_OF_LIGHT){
                crossWarSonOfLightScoreInfo = new int[]{conf.condition[0], conf.condition[1], conf.point};
                continue;
            }
            if(conf.t_object == CrossWarConst.OBJ_TYPE_MONSTER){
                crossWarMonsterScoreMap.put(Utils.longValue(conf.condition[0]), conf.point);
                continue;
            }
            int[] playerInfo = new int[]{conf.condition[0], conf.condition[1], conf.point};
            crossWarPlayerScoreList.add(playerInfo);
        }
        Log.temp.info("crossWarPlayerScoreList={}", crossWarPlayerScoreList);
        Log.temp.info("crossWarMonsterScoreMap={}", crossWarMonsterScoreMap);
        Log.temp.info("crossWarSonOfLightScoreInfo={}", crossWarSonOfLightScoreInfo);
    }

    public static void reloadConfBreakGoldEggWeight() {
        Map<Integer, Map<Integer, Map<Integer, List<ConfBreakGoldEggWeight>>>> confBreakGoldEggWeightMapNew = new HashMap<>();
        Collection<ConfBreakGoldEggWeight> confList = ConfBreakGoldEggWeight.findAll();
        for (ConfBreakGoldEggWeight conf : confList) {
            confBreakGoldEggWeightMapNew
                    .computeIfAbsent(conf.type, k -> new HashMap<>())
                    .computeIfAbsent(conf.group_id, k -> new HashMap<>())
                    .computeIfAbsent(conf.layers, k -> new ArrayList<>())
                    .add(conf);
        }

        for (Map<Integer, Map<Integer, List<ConfBreakGoldEggWeight>>> groupMap : confBreakGoldEggWeightMapNew.values()) {
            for (Map<Integer, List<ConfBreakGoldEggWeight>> layerMap : groupMap.values()) {
                for (List<ConfBreakGoldEggWeight> confL : layerMap.values()) {
                    confL.sort(Comparator.comparingInt(a -> a.sn));
                }
            }
        }

        confBreakGoldEggWeightMap = Collections.unmodifiableMap(confBreakGoldEggWeightMapNew);
    }

    public static List<ConfBreakGoldEggWeight> getConfBreakGoldEggWeight(int type, int groupId, int layer) {
        return confBreakGoldEggWeightMap
                .getOrDefault(type, Collections.emptyMap())
                .getOrDefault(groupId, Collections.emptyMap())
                .getOrDefault(layer, Collections.emptyList());
    }

    public static int getConfBreakGoldEggWeightSize(int type, int groupId) {
        return confBreakGoldEggWeightMap
                .getOrDefault(type, Collections.emptyMap())
                .getOrDefault(groupId, Collections.emptyMap())
                .size();
    }

    private static void reloadConfLoopBreakGoldEggWeight() {
        Map<Integer, Map<Integer, Map<Integer, List<ConfLoopBreakGoldEggWeight>>>> confLoopBreakGoldEggWeightMapNew = new HashMap<>();
        Collection<ConfLoopBreakGoldEggWeight> confList = ConfLoopBreakGoldEggWeight.findAll();
        for (ConfLoopBreakGoldEggWeight conf : confList) {
            confLoopBreakGoldEggWeightMapNew
                    .computeIfAbsent(conf.type, k -> new HashMap<>())
                    .computeIfAbsent(conf.group_id, k -> new HashMap<>())
                    .computeIfAbsent(conf.loop*10000 + conf.layers, k -> new ArrayList<>())
                    .add(conf);
        }

        for (Map<Integer, Map<Integer, List<ConfLoopBreakGoldEggWeight>>> groupMap : confLoopBreakGoldEggWeightMapNew.values()) {
            for (Map<Integer, List<ConfLoopBreakGoldEggWeight>> layerMap : groupMap.values()) {
                for (List<ConfLoopBreakGoldEggWeight> confL : layerMap.values()) {
                    confL.sort(Comparator.comparingInt(a -> a.order));
                }
            }
        }

        confLoopBreakGoldEggWeightMap = Collections.unmodifiableMap(confLoopBreakGoldEggWeightMapNew);
    }

    public static List<ConfLoopBreakGoldEggWeight> getConfLoopBreakGoldEggWeight(int type, int groupId, int loop, int layer) {
        return confLoopBreakGoldEggWeightMap
                .getOrDefault(type, Collections.emptyMap())
                .getOrDefault(groupId, Collections.emptyMap())
                .getOrDefault(loop*10000 + layer, Collections.emptyList());
    }

    // region 飞宠相关的全局配置
    // 飞宠变异天赋sn, 权重构成的二维数组
    public static int[][] flyVarEntryWeights = new int[0][];
    public static int flyMaxHybridTimeSn = 0;
    // Map<飞宠类型, List<培养次数>> 为了计算培育冷却cd
    public static Map<Integer, List<Integer>> flyTypeHybridNumMap = new HashMap<>();
    // Map<飞宠类型, 培育消耗>
    public static Map<Integer, int[][]> flyTypeHybridCostMap = new HashMap<>();
    // Map<飞宠成就条件类型, List<飞宠成就组别>>
    public static Map<Integer, List<Integer>> flyAchieveCondTypeGroup = new HashMap<>();
    // Map<飞宠成就组别, 飞宠成就组别内第一个成就sn>
    public static Map<Integer, Integer> flyAchieveGroupStartMap = new HashMap<>();

    private static void reloadConfFly() {
        // 飞宠变异天赋
        List<ConfFlyEntryWeight> varConfList = new ArrayList<>();
        for (ConfFlyEntryWeight conf : ConfFlyEntryWeight.findAll()) {
            if (conf.var_weight != 0) {
                varConfList.add(conf);
            }
        }
        if (!varConfList.isEmpty()) {
            flyVarEntryWeights = varConfList.stream().map(conf -> new int[] {conf.sn, conf.var_weight}).toArray(int[][]::new);
        }
        // 飞宠最大培养sn
        for (ConfFlyHybridTime conf : ConfFlyHybridTime.findAll()) {
            flyMaxHybridTimeSn = Math.max(flyMaxHybridTimeSn, conf.sn);
        }
        // 飞宠培养冷却
        flyTypeHybridNumMap.clear();
        for (ConfFlyCd conf : ConfFlyCd.findAll()) {
            List<Integer> numList = flyTypeHybridNumMap.computeIfAbsent(conf.type, k -> new ArrayList<>());
            numList.add(conf.times);
        }
        for (List<Integer> list : flyTypeHybridNumMap.values()) {
            list.sort(Integer::compareTo);
        }
        // 飞宠培养消耗
        flyTypeHybridCostMap.clear();
        String flyHybridCostStr = ConfGlobal.get(ConfGlobalKey.fly_hybrid_cost.SN).strValue;
        String[] strParts = flyHybridCostStr.split("\\|");
        for (String strPart : strParts) {
            String[] strSplit = strPart.split(",");
            int type = Utils.intValue(strSplit[0]);
            int[][] cost = new int[strSplit.length - 1][];
            for (int i = 1; i < strSplit.length; i++) {
                String[] split = strSplit[i].split("_");
                cost[i - 1] = new int[] {
                        Utils.intValue(split[0]),
                        Utils.intValue(split[1])
                };
            }
            flyTypeHybridCostMap.put(type, cost);
        }
        // 飞宠成就
        flyAchieveCondTypeGroup.clear();
        flyAchieveGroupStartMap.clear();
        for (ConfFlyAchievement conf : ConfFlyAchievement.findAll()) {
            int conditionType = conf.condition[0];
            int group = conf.group;
            List<Integer> groupList = flyAchieveCondTypeGroup.computeIfAbsent(conditionType, k -> new ArrayList<>());
            if (!groupList.contains(group)) {
                groupList.add(group);
            }

            if (!flyAchieveGroupStartMap.containsKey(group)) {
                flyAchieveGroupStartMap.put(group, conf.sn);
            }
        }
    }
    // endregion 飞宠相关的全局配置

    // region 伙伴皮肤配置
    // Map<伙伴sn, List<伙伴皮肤sn>>
    public static Map<Integer, List<Integer>> petSnSkinMap = new HashMap<>();
    public static void reloadConfPetSkin() {
        petSnSkinMap.clear();
        for (ConfPetSkin conf : ConfPetSkin.findAll()) {
            petSnSkinMap.computeIfAbsent(conf.pet, k -> new ArrayList<>()).add(conf.sn);
        }
    }
    // endregion 伙伴皮肤配置

    public static void reloadDoubleChapterMaxLevel() {
        Map<Integer, Integer> confDoubleChapterMaxChapterMapNew = new HashMap<>();
        Map<String, ConfDoubleLadderChapter> confDoubleLadderChapterMapNew = new HashMap<>();
        Collection<ConfDoubleLadderChapter> confList = ConfDoubleLadderChapter.findAll();
        for (ConfDoubleLadderChapter conf : confList) {
            String key = conf.chapter + "|" + conf.index;
            confDoubleLadderChapterMapNew.put(key,conf);
            if(conf.part_type == 1){
                confDoubleChapterMaxChapterMapNew.put(conf.chapter, conf.index);
            }
            if(conf.sn > confMaxDoubleLadderChapter){
                confMaxDoubleLadderChapter = conf.sn;
            }
        }
        confDoubleChapterMaxChapterMap = Collections.unmodifiableMap(confDoubleChapterMaxChapterMapNew);
        confDoubleLadderChapterMap =Collections.unmodifiableMap(confDoubleLadderChapterMapNew);
    }

    public static int getDoubleChapterMaxLevel(int chapter){
        return confDoubleChapterMaxChapterMap.getOrDefault(chapter,0);
    }

    public static ConfDoubleLadderChapter getConfDoubleLadderChapter(int chapter, int index){
        return confDoubleLadderChapterMap.get(chapter + "|" + index);
    }

    public static int getConfMaxDoubleLadderChapterSn(){
        return confMaxDoubleLadderChapter;
    }
    public static void reloadMonopolyBoardSizeMap(){
        Map<Integer, Integer> monopolyBoardSizeMapNew = new HashMap<>();
        for(ConfMonopolyGrid_0 conf : ConfMonopolyGrid_0.findAll()){
            int sizeValue = monopolyBoardSizeMapNew.getOrDefault(conf.board_id, 0);
            if(conf.sort > sizeValue){
                monopolyBoardSizeMapNew.put(conf.board_id, conf.sort);
            }
        }
        confMonopolyBoardSizeMap = Collections.unmodifiableMap(monopolyBoardSizeMapNew);
    }

    public static int getConfMonoPolyBoardSize(int boardId){
        return confMonopolyBoardSizeMap.getOrDefault(boardId, 0);
    }

    // region 第四职业相关
    public static Map<Integer, List<Integer>> summonerSkillMap = new HashMap<>();// Map<被动技能sn, List<第四职业被动技能id>>

    public static void reloadSummonerSkill() {
        summonerSkillMap.clear();
        for (ConfSummonerPassive conf : ConfSummonerPassive.findAll()) {
            int skillSn = conf.skill;
            List<Integer> snList = summonerSkillMap.computeIfAbsent(skillSn, k -> new ArrayList<>());
            snList.add(conf.sn);
        }
        for (List<Integer> snList : summonerSkillMap.values()) {
            snList.sort(Integer::compareTo);
        }
    }
    // endregion 第四职业相关

    private static void reloadSlimeEffectItemSet() {
        confSlimeEffectItemSnSet.clear();
        for (ConfSlimeEffect conf : ConfSlimeEffect.findAll()) {
            if(conf.effect_type1 == 2 || conf.effect_type1 == 11){
                if(conf.value1 != null){
                    for (int item[] : conf.value1) {
                        confSlimeEffectItemSnSet.add(item[0]);
                    }
                }
            }
            if(conf.effect_type2 == 2 || conf.effect_type2 == 11){
                if(conf.value2 != null){
                    for (int item[] : conf.value2) {
                        confSlimeEffectItemSnSet.add(item[0]);
                    }
                }
            }
            if (conf.effect_type3 == 2 || conf.effect_type3 == 11) {
                if (conf.value3 != null) {
                    for (int item[] : conf.value3) {
                        confSlimeEffectItemSnSet.add(item[0]);
                    }
                }
            }
        }
        for (ConfSlimeBuffList conf : ConfSlimeBuffList.findAll()) {
            if (conf.effect_type == 2) {
                if (conf.value != null) {
                    for (int item[] : conf.value) {
                        confSlimeEffectItemSnSet.add(item[0]);
                    }
                }
            }
        }
    }

    public static boolean isInSlimeEffectItemSet(int itemSn) {
        return confSlimeEffectItemSnSet.contains(itemSn);
    }

    // region 连冲活动的配置
    // Map<充值表sn, Map<礼包类别, List<奖励顺序>>>
    public static Map<Integer, Map<Integer, List<Integer>>> refreshGiftConfMap = new HashMap<>();

    private static void reloadConfRefreshGift() {
        refreshGiftConfMap.clear();
        for (ConfRefreshGift_0 conf : ConfRefreshGift_0.findAll()) {
            Map<Integer, List<Integer>> gearIndexMap = refreshGiftConfMap.computeIfAbsent(conf.id, k -> new HashMap<>());
            gearIndexMap.computeIfAbsent(conf.gift_type, k -> new ArrayList<>()).add(conf.sort);
        }
        for (Map<Integer, List<Integer>> gearIndexMap : refreshGiftConfMap.values()) {
            for (List<Integer> indexList : gearIndexMap.values()) {
                indexList.sort(Integer::compareTo);
            }
        }
    }
    // endregion 连冲活动的配置

    /**
     * 获取活动装备
     * @param activityId 活动ID
     * @param level 等级
     * @param quality 品质
     * @return 装备SN集合
     */
    public static Set<Integer> getActivityEquipments(int activityId, int level, int quality) {
        Map<RangeInt, Map<Integer, Integer>> gradeSnMap = activityEquipmentMap.get(activityId);
        if (gradeSnMap == null) {
            return Collections.emptySet();
        }
        Set<Integer> snList = new HashSet<>();
        for (Map.Entry<RangeInt, Map<Integer, Integer>> entry : gradeSnMap.entrySet()) {
            RangeInt rangeInt = entry.getKey();
            if (rangeInt.min <= level && level <= rangeInt.max) {
                for (Map.Entry<Integer, Integer> entry2 : entry.getValue().entrySet()) {
                    if (entry2.getValue() == quality) {
                        snList.add(entry2.getKey());
                    }
                }
            }
        }
        return snList;
    }

    // region 新闻播报相关
    // Map<条件类型, 最小条件值（如果条件值没超过这个就不处理，做个优化）>
    public static Map<Integer, Integer> newsMinConditionMap = new HashMap<>();
    // Map<条件类型, 最大条件值（部分类型如果条件值超过这个就不处理，做个优化）>
    public static Map<Integer, Integer> newsMaxConditionMap = new HashMap<>();
    // Map<组id, 排行最大值>
    public static Map<Integer, Integer> newsGroupMaxRankMap = new HashMap<>();
    // Map<"组id_排行", 新闻SN>
    public static Map<String, Integer> newsGroupSnMap = new HashMap<>();
    // Map<"条件类型_条件值", 组id>
    public static Map<String, Integer> newsGroupMap = new HashMap<>();
    // Map<条件类型, List<条件值>>
    public static Map<Integer, List<Integer>> newsConditionValueListMap = new HashMap<>();

    private static void reloadConfNews() {
        newsMinConditionMap.clear();
        newsMaxConditionMap.clear();
        newsGroupMaxRankMap.clear();
        newsGroupSnMap.clear();
        newsGroupMap.clear();
        newsConditionValueListMap.clear();
        for (ConfNews conf : ConfNews.findAll()) {
            int groupId = conf.group;
            int conditionType = conf.condition;
            newsMinConditionMap.put(conditionType, Math.min(conf.coefficient, newsMinConditionMap.getOrDefault(conditionType, Integer.MAX_VALUE)));
            newsMaxConditionMap.put(conditionType, Math.max(conf.coefficient, newsMaxConditionMap.getOrDefault(conditionType, 0)));
            newsGroupMaxRankMap.put(groupId, Math.max(conf.rank, newsGroupMaxRankMap.getOrDefault(groupId, 0)));
            newsGroupSnMap.put(Utils.createStr("{}_{}", groupId, conf.rank), conf.sn);
            newsGroupMap.put(Utils.createStr("{}_{}", conditionType, conf.coefficient), groupId);
            List<Integer> valueList = newsConditionValueListMap.computeIfAbsent(conditionType, k -> new ArrayList<>());
            if (!valueList.contains(conf.coefficient)) {
                valueList.add(conf.coefficient);
            }
        }
        for (List<Integer> list : newsConditionValueListMap.values()) {
            list.sort(Integer::compareTo);
        }
    }
    // endregion 新闻播报相关

    private static void reloadConfServerMerge(){
        Map<Integer, ServerMerge> serverMergeMapNew = new HashMap();
        for(ConfServerMerge conf : ConfServerMerge.findAll()){
            ServerMerge merge = new ServerMerge(conf);
            serverMergeMapNew.put(conf.sn, merge);
        }
        serverMergeMap = Collections.unmodifiableMap(serverMergeMapNew);

    }

    public static Map<Integer, ServerMerge> getServerMergeMap(){
        return serverMergeMap;
    }

    public static void addRemoveId(long humanId){
        removeIdSet.add(humanId);
    }

    public static Set<Long> getRemoveIdSet(){
        return removeIdSet;
    }
}
