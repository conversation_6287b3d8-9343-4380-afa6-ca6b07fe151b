package org.gof.demo.worldsrv.human;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.Message;
import com.pwrd.op.LogOp;
import com.pwrd.op.LogOpChannel;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.redis.client.Command;
import io.vertx.redis.client.Request;
import org.apache.commons.collections.CollectionUtils;
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.RecordTransient;
import org.gof.core.connsrv.ConnectionProxy;
import org.gof.core.db.DBKey;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.CrossRedis;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleMethod;
import org.gof.core.support.S;
import org.gof.core.support.*;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.distr.cross.CrossManager;
import org.gof.demo.distr.cross.domain.CrossPoint;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.seam.account.AccountService;
import org.gof.demo.seam.account.AccountServiceProxy;
import org.gof.demo.support.TimeUtil;
import org.gof.demo.worldsrv.activity.*;
import org.gof.demo.worldsrv.arena.ArenaManager;
import org.gof.demo.worldsrv.arena.ArenaRankedBrief;
import org.gof.demo.worldsrv.arena.ArenaServiceProxy;
import org.gof.demo.worldsrv.captureSlave.CaptureSlaveManager;
import org.gof.demo.worldsrv.carPark.CarParkServiceProxy;
import org.gof.demo.worldsrv.character.HumanObjectService;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.common.DataResetService;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.common.GameServiceProxy;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.crossWar.CrossWarConst;
import org.gof.demo.worldsrv.crossWar.CrossWarRewardVO;
import org.gof.demo.worldsrv.crossWar.CrossWarUtils;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.GuildServiceProxy;
import org.gof.demo.worldsrv.guild.league.GuildLeagueServiceProxy;
import org.gof.demo.worldsrv.guild.league.GuildLeagueWarmUpServiceProxy;
import org.gof.demo.worldsrv.guild.league.LeagueVO;
import org.gof.demo.worldsrv.home.FarmServiceProxy;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.inform.MarqueeInfo;
import org.gof.demo.worldsrv.kungFuRace.KungFuRaceRewardVO;
import org.gof.demo.worldsrv.kungFuRace.KungFuRaceUtils;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.msg.*;
import org.gof.demo.worldsrv.name.NameServiceProxy;
import org.gof.demo.worldsrv.pocketLine.Pocket;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.pocketLine.PocketLineManager;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.*;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.HumanScopeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.team.TeamMember;
import org.gof.demo.worldsrv.worldBoss.WorldBossServiceProxy;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;

@DistrClass(
		servId = D.SERV_HUMAN_GLOBAL,
		importClass = {HumanGlobalInfo.class, Map.class, HumanScopeKey.class, List.class, Message.class, MoneyItemLogKey.class,
				FillMail.class, Set.class, Vector2D.class, PocketLineEventSubKey.class, MarqueeInfo.class, Param.class, Guild.class,
				ActivityVo.class, PocketLine.class,
		},
		localOnly = false

)

public class HumanGlobalService extends GameServiceBase {
	private TickTimer msgPulseTimer = new TickTimer(500); //控制广播发送频率

	private TickTimer scheduleTimer = new TickTimer(10000);//调度的处理
	private Map<Integer, Long> shceduleMap = new LinkedHashMap<Integer, Long>();

	private TickTimer checkTimer = new TickTimer(60000); //检查，每分钟检查一次
	private static long outTime = 600000; //10分钟

	private TickTimer logTimer = new TickTimer(300000); //写日志时间
	private int COUNT100Per10SEC = 500;
	private long lastFakeBroadcastTime = 0;//上次假播报的时间

	//玩家状态信息
	private Map<Long, HumanGlobalInfo> datas = new HashMap<>();
	private Map<String, HumanGlobalInfo> account2humans = new HashMap<>();

	//向account同步服务器人数满员状态
	public static int ACCOUNTTIME = 10;			//检查超时时间
	private TickTimer accountOnlineFullTimer = new TickTimer(ACCOUNTTIME * Time.SEC);

	private TickTimer checkServerIdTT = new TickTimer(5 * Time.MIN);//调度的处理

	AccountServiceProxy accountServ;

	//各个渠道的人数限制
	private Map<String, ChannelMax> channelMap = new HashMap<String, ChannelMax>();

	private Map<Integer, Integer> zoneNumMap = new HashMap<Integer, Integer>();

	//检查是否存在异常数据时用于记录待删除的数据
	private List<Long> removeList = new LinkedList<Long>();

	//删除 keyhumanID value移除时间
	private Map<Long, Long> deleteHumanMap = new HashMap<>();
	//发送在线人数
	private TickTimer onlineNumberTimer = new TickTimer(TimeUtil.MINUTE_TIME);


	//下一次删除的
	private LinkedHashSet<Long> deleteHumanSet = new LinkedHashSet<>();

	public static final String BACKSTAGE_ONLINE_NUMBER = "/rest/serverNum/getData";//后台在线人数接口地址
	//跨服存储玩家临时战场id
	public Map<Long, Long> tempHumanBattleTeamIds = new HashMap<>();

	// 本服下的所有游戏服serverid
	public static List<Integer> serverIdListNow = new ArrayList<>();

	private TickTimer ttSec = new TickTimer(Time.SEC);
	private TickTimer ttArenaSec = new TickTimer(Time.SEC);

	public static LinkedList<ArenaRankedBrief> arenaRankedBriefList = new LinkedList<>();// 竞技场排位赛结算奖励

	public LinkedList<ArenaRankedBrief> arenaCrossBriefList = new LinkedList<>();// 竞技场跨服结算奖励

	private boolean isServerIdUp = false;

	public LinkedList<LeagueVO> humanLeagueVoList = new LinkedList<>();// 乱斗玩家奖励

	private final TickTimer ttLeagueSec = new TickTimer(Time.SEC);

	public final LinkedList<CrossWarRewardVO> crossWarRewardVoList = new LinkedList<>();// 跨服战玩家奖励
	private final TickTimer ttSendCrossSec = new TickTimer(Time.SEC);

	private final LinkedList<KungFuRaceRewardVO> m_kungFuRaceRewardVoList = new LinkedList<>();// 武道会玩家奖励
	private final TickTimer m_ttSendKungFuRaceSec = new TickTimer(Time.SEC);


	@Override
	public void pulseOverride() {
		long nowTime = Port.getTime();

		if(scheduleTimer.isPeriod(nowTime)) {
			onSchdule();
		}

		//写日志
		checkWriteOnlineLog(nowTime);

		//向account同步服务器人数满员状态
		pulseAccountOnlineFull(nowTime);

		if(NameFix.isReload){
			// 屏蔽字重新加载需要向后台重新要一份数据
			NameFix.isReload = false;
			HumanManager.inst().sendHttpShield();
		}

		if(checkServerIdTT.isPeriod(nowTime)){
			checkServerId();
		}
		if(ttSec.isPeriod(nowTime)){
			sendArenaRankedSettle();

		}
		if(ttArenaSec.isPeriod(nowTime)){
			sendArenaCrossBrief();
		}

		if(ttLeagueSec.isPeriod(nowTime)){
			sendLeagueReward();
		}
		if(ttSendCrossSec.isPeriod(nowTime)){
			sendCrossWarMail();
		}
		if(m_ttSendKungFuRaceSec.isPeriod(nowTime)){
			sendKungFuRaceMail();
		}
	}

	/** 
	 * 检测写日志及是否有残留玩家数据未清除
	 * <AUTHOR>
	 * @Date 2024/2/23
	 * @Param 
	 */
	private void checkWriteOnlineLog(long nowTime){
		if(checkTimer.isPeriod(nowTime)){
			Log.temp.error("====在线玩家数量 Human Global Count ======{}========", datas.size());
		}
		if(!logTimer.isPeriod(nowTime)) {
			return;
		}
		writeOnlineLog();

		// 日志输出在线人数
		int num = datas.size();
		Log.temp.error("Human Global Count ======{}========",num);
		// 是否有残留玩家数据未清除
		checkHumenLeaveCancel(nowTime);
	}

	/**
	 * 检查所有globalinfo。残留玩家数据未清除
	 */
	private void checkHumenLeaveCancel(long nowTime){
		removeList.clear();
		for(HumanGlobalInfo info:datas.values()){
			if(info.syncTime > 0 && (info.syncTime+outTime) < nowTime){
				removeList.add(info.id);
				HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
				if(proxy != null) {
					proxy.kickClosed();
				}
			}
		}
		if(removeList.size()>0){
			for(long id:removeList){
				Log.temp.error("检查踢人下线{}", id);
				cancel(id);
			}
		}
	}

	/**
	 * 向account同步服务器人数满员状态
	 * @param now
	 */
	private void pulseAccountOnlineFull(long now) {
		if (S.isBridge) {
			return;
		}

		if(!accountOnlineFullTimer.isPeriod(now)) {
			return;
		}
		//当前人数
		int num = datas.size();
		//Log.temp.info("pulseAccountOnlineFull:num============{}",num);
		//同步到account服务器
		accountServ.setHumanOnlineFull(num);
	}

	public HumanGlobalService(GamePort port) {
		super(port);
	}

	@Override
	protected void init() {
		accountServ = AccountService.createProxy();
		if(S.isRedis){
			checkServerId();
			return;
		}
		//初始各个渠道注册人数
		DB db = DB.newInstance(Human.tableName);
		for(String[] ss : Config.channelMax){
			ChannelMax cx = new ChannelMax();
			cx.channelName = ss[0];
			cx.channel = ss[1];
			cx.max = Integer.parseInt(ss[2]);

			if(ss[0].equals("total")){
				db.countBy(false);
				Param param = db.waitForResult();
				int count = param.get();

				cx.now = count;
			}else{
				db.countBy(false, "channel", cx.channel);
				Param param = db.waitForResult();
				int count = param.get();

				cx.now = count;
			}

			channelMap.put(cx.channel, cx);
		}
		List<String> colList = new ArrayList<>();
		colList.add(Human.K.zone);
		String whereSql = Utils.createStr(" GROUP BY `{}`", Human.K.zone);
		db.findByQuery(false, whereSql, DBKey.COLUMN, colList);
		// 各个区的人数
		Param param = db.waitForResult();
		if(param == null){
			return;
		}
		List<RecordTransient> list = param.get();
		for(RecordTransient record : list){
			int zone = record.get(Human.K.zone);
			db.countBy(false, Human.K.zone, zone);
			Param param2 = db.waitForResult();
			int count = 0;
			if(param2 != null){
				count = param2.get();
			}
			zoneNumMap.put(zone, count);
		}
		Log.temp.info("===zoneNumMap={}", zoneNumMap);


	}


	private void checkServerId(){
		List<Integer> serverList = Util.getServerTagList(C.GAME_SERVER_ID);
		if(serverList.isEmpty()){
			Log.temp.error("===没有获取到serverId列表，请检查配置,{}", C.GAME_SERVER_ID);
			return;
		}
		for(Integer serverId : serverList){
			if(!serverIdListNow.contains(serverId)){
				serverIdListNow.add(serverId);
			}
		}

	}


	/**
	 * 获取玩家的全局信息
	 * @param humanId
	 */
	@DistrMethod
	public void getInfo(long humanId) {
		HumanGlobalInfo info = datas.get(humanId);
//		Log.temp.error("====获取玩家全局数据==== id={}, info={}, ids={}", humanId, info, datas.keySet());
		port.returns(datas.get(humanId));
	}


	/**
	 * 获取多个玩家的全局信息
	 * @param humanIds
	 */
	@DistrMethod
	public void getInfos(List<Long> humanIds) {
		List<HumanGlobalInfo> infos = getHumanInfos(humanIds);
		port.returns(infos);
	}

	/**
	 * 获取多个队伍的玩家的全局信息
	 * @param humanIdsMap
	 */
	@DistrMethod
	public void getInfosByMap(Map<Object, List<Long>>humanIdsMap) {
		Map<Object, List<HumanGlobalInfo>> result = new LinkedHashMap<Object, List<HumanGlobalInfo>>();
		for(Entry<Object, List<Long>> entry:humanIdsMap.entrySet()){
			Object key = entry.getKey();
			List<Long> ids = entry.getValue();
			List<HumanGlobalInfo> infos = getHumanInfos(ids);
			result.put(key, infos);
		}
		port.returns(result);
	}
	private List<HumanGlobalInfo> getHumanInfos(List<Long> ids){
		List<HumanGlobalInfo> infos = new ArrayList<HumanGlobalInfo>();
		for(Long hId : ids){
			if(hId == null){
				continue;
			}
			if(datas.containsKey(hId)){
				infos.add(datas.get(hId));
			}
		}
		return infos;
	}
	/**
	 * @param humansIds
	 */
	@DistrMethod
	public void getInfo(List<Long> humansIds) {
		List<HumanGlobalInfo> humansInfo = new ArrayList<HumanGlobalInfo>() ;
		//优化
		for(Long id : humansIds){
			HumanGlobalInfo tmp = datas.get(id);
			if(tmp != null)
				humansInfo.add(tmp);
		}
		port.returns(humansInfo);
	}

	@DistrMethod
	public void getOnlineInfoList() {
		List<Long> humanIdList = new ArrayList<>(datas.keySet());
		List<String> accountList = new ArrayList<>();
		datas.values().stream().forEach(humanGlobalInfo -> {
			accountList.add(humanGlobalInfo.account);
		});
		port.returns("idList", humanIdList, "accountList", accountList);
	}

	/**
	 * 注册玩家全局信息
	 * @param status
	 */
	@DistrMethod(argsImmutable = true)
	public void register(HumanGlobalInfo status) {
		if(S.isBridge) {
			status.connPoint.nodeId = D.NODE_WORLD_BRIDGE_PREFIX + Utils.getServerIdByHumanId(status.id);
		}
		if(!datas.containsKey(status.id)){
			Log.game.info("loginTrace[-] login_step_1711====注册或同步玩家全局数据====humanId={}, account={}, serverId={}", status.id, status.account, Config.GAME_SERVER_ID);
		}
		datas.put(status.id, status);
		account2humans.put(status.account, status);

	}

	@DistrMethod
	public void getOnLineHumanList(List<Long> humansIds) {
		List<Long> onLineList = new ArrayList<Long>() ;
		//优化
		for(Long id : humansIds){
			if(datas.containsKey(id))
				onLineList.add(id);
		}
		port.returns(onLineList);
	}

	/**
	 * 获取公会中所有在线成员
	 */
	@DistrMethod
	public void getOnlineGuildMembers(long guildId) {
		List<HumanGlobalInfo> guildMembers = new ArrayList<>();
		for (HumanGlobalInfo info : datas.values()) {
			if (info.guildId == guildId) {
				guildMembers.add(info);
			}
		}
		port.returns("guildMembers", guildMembers);
	}

	@DistrMethod
	public void loadServerId(int serverId) {
		if(!serverIdListNow.contains(serverId)){
			serverIdListNow.add(serverId);
			isServerIdUp = true;
			loadDataServerId();
		}
	}

	private void loadDataServerId(){
		if(!S.isGameServer){
			return;
		}

		WorldBossServiceProxy.newInstance().loadServer(serverIdListNow);

		ActivityControlServiceProxy.newInstance().loadServer(serverIdListNow);

		NameServiceProxy.newInstance().loadServer(serverIdListNow);

		GameServiceProxy.newInstance().loadServer(serverIdListNow);

		FarmServiceProxy.newInstance().loadServer(serverIdListNow);
		CarParkServiceProxy.newInstance().loadServer(serverIdListNow);

		ArenaServiceProxy.newInstance().loadServer(serverIdListNow);

//		CarParkServiceCrossProxy.newInstance().loadServer(serverIdListNow);

		GuildServiceProxy.newInstance().loadServer(serverIdListNow);

		GuildLeagueWarmUpServiceProxy.newInstance().loadServer(serverIdListNow);
	}

//	@ScheduleMethod(DataResetService.CRON_WEEK_1_0)
//	public void _CRON_WEEK_1_0() {
//		for(int serverId : serverIdListNow){
//			int serNo = NodeAdapter.bridgeServerNo(serverId);
//			String nodeId = D.NODE_BRIDGE_PREFIX + serNo;
//			Node node = Port.getCurrent().getNode();
//			if(!node.isRemoteNodeConnected(nodeId)) {
//				node.addRemoteNode(nodeId);
//			}
//		}
//	}

//	private void loadMax(int serverId){
//		String whereSql = Utils.createStr("select `{}`, count(`{}`) from {} where `{}`={} GROUP BY `{}`",
//				Human.K.serverId, Human.K.serverId, Human.tableName, serverId, Human.K.serverId);
//		RowSet<Row> result = EntityManager.executeSql(whereSql);
//		Map<Integer, Integer> serverNumMap = new HashMap<>();
//		int totalNum = 0;
//		if(result != null){
//			for(Row row : result){
//				String name = row.getColumnName(0);
//				String nameNum = row.getColumnName(1);
//				int num = Utils.intValue(row.getValue(nameNum));
//				totalNum += num;
//				serverNumMap.put(Utils.intValue(row.getValue(name)), num);
//			}
//		}
//	}

	@DistrMethod
	public void ChangeConnPoint(Long id, CallPoint point) {
		HumanGlobalInfo info = datas.get(id);
		if(info == null) {
			return;
		}
		if(S.isBridge){
			// 跨服重连处理
			info.connPoint.portId = point.portId;
			info.connPoint.servId = point.servId;
			return;
		}
		info.connPoint = point;
	}

	/**
	 * 从跨服归来，归来的时候，将原地图隐藏的展示
	 * @param humanId
	 */
	@DistrMethod
	public void bridgeToWorldBack(long humanId, Param param) {
		HumanGlobalInfo info = datas.get(humanId);
		if(info == null) {
			Log.temp.error("===找不到玩家humanId={}, info={}", humanId, info);
			return;
		}

		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.bridgeStageShow(param);
	}

	/**
	 * 玩家是否已登录
	 * @param account
	 */
	@DistrMethod
	public void isLogined(String account, String channel, int serverId) {
		HumanGlobalInfo humanInfo = null;
		boolean logined = account2humans.containsKey(account);
		long humanId = 0;
		if (logined) {
			humanInfo = account2humans.get(account);
			humanId = humanInfo.id;
		}
		if(!serverIdListNow.contains(serverId)){
			port.returns("isFull", false,"isServerFull", false , "logined", logined, "humanId", humanId, "humanInfo", humanInfo, "noLogin", true);
			return;
		}

		if(null == channel){
			port.returns("isFull", false,"isServerFull", false , "logined", logined, "humanId", humanId, "humanInfo", humanInfo);
			return;
		}

		//获取全服最大注册人数
		ChannelMax totalMax = channelMap.get("total");
		if(totalMax == null){
			port.returns("isFull", false, "isServerFull", false , "logined", logined, "humanId", humanId, "humanInfo", humanInfo);
			return;
		}
		if(totalMax.max > 0 && totalMax.isFull()){
			//有人数限制 或者已经达到上限
			port.returns("isFull", true, "isServerFull", true , "logined", logined, "humanId", humanId, "humanInfo", humanInfo);
		}else{
			port.returns("isFull", totalMax.isFull(), "isServerFull", false , "logined", logined, "humanId", humanId, "humanInfo", humanInfo);
		}
	}

	@DistrMethod
	public void switchRole(String account, CallPoint cp) {
		accountServ.reCreateAccount(account, cp);
	}

	/**
	 * 踢出玩家
	 * @param humanId
	 * @param serverDataSn
	 * @param params
	 */
	@DistrMethod
	public void kick(long humanId, int serverDataSn, Object[] params) {
		kickHuman(humanId, serverDataSn, params);
	}

	@DistrMethod
	public void kickAll() {
		CountDownLatch latch = new CountDownLatch(datas.size());
		// 玩家连接信息
		for (HumanGlobalInfo info : datas.values()) {
			if (info == null) {
				latch.countDown();
				continue;
			}
			// 发送消息 通知玩家被踢
			MsgLogin.logout_s2c.Builder msg = MsgLogin.logout_s2c.newBuilder();
			msg.setCode(ErrorTip.SystemMaintainKick);
			sendMsg(info.id, msg.build());
			try {
				Log.game.info("===踢人下线humanId={}, info.portId={}", info.id, info.portId);
				Port humanPort = port.getNode().getPort(info.portId);
				humanPort.doAction(() -> {
					HumanObjectService serv = humanPort.getServices(info.id);
					if(serv!=null) {
						serv.kickClosed();
					}
					latch.countDown();
				});
			} catch (Exception e){
				Log.game.error("踢出所有玩家出现异常，humanId = {}, e={}", info.id, e, e);
				latch.countDown();
			}
		}
		try {
			latch.await();
		} catch (InterruptedException e) {
			Log.game.error("等待踢出所有玩家出现异常！", e, e);
		}
		Log.game.info("===已踢出所有玩家");
	}
	/**
	 * 踢人下线
	 * @param humanId
	 * @param serverDataSn
	 * @param params
	 */
	private void kickHuman(long humanId, int serverDataSn, Object[] params) {
		String reason = Inform.getServerData(serverDataSn, params);
		Log.game.error("===踢人下线:humanId={}, reason={}, serverDataSn={}", humanId, reason, serverDataSn);

		if (serverDataSn == 20) {
			//发送消息 通知玩家被踢
			MsgLogin.logout_s2c.Builder msg = MsgLogin.logout_s2c.newBuilder();
			msg.setCode(20);
			sendMsg(humanId, msg.build());
		} else {
			MsgLogin.logout_s2c.Builder msg = MsgLogin.logout_s2c.newBuilder();
			msg.setCode(23);
			sendMsg(humanId, msg.build());
		}

		//玩家连接信息
		HumanGlobalInfo info = datas.get(humanId);
		if(info == null){
			Log.game.info("找到不到玩家数据 humanId={}",humanId);
			return;
		}

		//断开连接
		ConnectionProxy prx = ConnectionProxy.newInstance(info.connPoint);
		prx.close();
		Log.game.info("===info.connPoint={}, ", info.connPoint);

//		//直接清除玩家的一些信息 因为如果玩家连接
		HumanObjectServiceProxy prxSource = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, humanId);
		prxSource.kickClosed();
	}

	/** 
	 * 踢出检测机器人
	 * <AUTHOR>
	 * @Date 2023/4/28
	 * @Param 
	 */
	@DistrMethod
	public void kickCheckRobotHuman(long humanId, String reason) {
		Log.game.info("===踢检测机器人下线:humanId={}, reason={}", humanId, reason);
		//玩家连接信息
		HumanGlobalInfo info = datas.get(humanId);
		if(info == null){
			Log.game.error("找到不到玩家数据 humanId={}",humanId);
			return;
		}
		//直接清除玩家的一些信息 因为如果玩家连接
		HumanObjectServiceProxy prxSource = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, humanId);
		prxSource.kickClosed();
	}


	/**
	 * 发送消息至玩家
	 * @param humanId
	 */
	@DistrMethod(argsImmutable=true)
	public void sendMsg(long humanId, Message msg) {
		//玩家连接信息
		HumanGlobalInfo info = datas.get(humanId);

		if(info == null) return ;
		HumanGlobalManager.inst().sendMsg(info.connPoint, msg);
	}

	/**
	 * 发送消息至全服玩家，有要排除的则设置excludeIds
	 * @param excludeIds
	 * @param msg
	 */
	@DistrMethod(argsImmutable=true)
	public void sendMsgToAll(List<Long> excludeIds, Message msg) {
		if(excludeIds ==  null){
			excludeIds = new ArrayList<>();
		}
		// 给所有玩家发送消息
		for(HumanGlobalInfo info : datas.values()) {
			// 排除不需要发送的玩家id
			if(excludeIds.contains(info.id)) {
				continue;
			}
			HumanGlobalManager.inst().sendMsg(info.connPoint, msg);
		}
	}


	/**
	 * 发送给指定的玩家
	 * @param humanIds
	 * @param msg
	 */
	@DistrMethod(argsImmutable=true)
	public void sendMsgTo(List<Long> humanIds, Message msg) {
		if(humanIds == null || humanIds.size() == 0){
			return;
		}
		// 给所有玩家发送消息
		for(HumanGlobalInfo info : datas.values()) {
			// 排除不需要发送的玩家id
			if(humanIds.contains(info.id)) {
				HumanGlobalManager.inst().sendMsg(info.connPoint, msg);
			}
		}
	}


	@DistrMethod(argsImmutable=true)
	public void sendMsgParam(List<Long> humanIds, Param param) {
		if(humanIds == null || humanIds.size() == 0){
			return;
		}
		// 给所有玩家发送消息
		for(HumanGlobalInfo info : datas.values()) {
			// 排除不需要发送的玩家id
			if(humanIds.contains(info.id)) {
				HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
				prx.sendMsgParam(param);
			}
		}
	}

	@DistrMethod(argsImmutable=true)
	public void sendFactionMsgTo(Message msg) {
		// 给所有有工会玩家发送消息
		for(HumanGlobalInfo info : datas.values()) {
			if(info.guildId > 0) {
				HumanGlobalManager.inst().sendMsg(info.connPoint, msg);
			}
		}
	}

	@DistrMethod(argsImmutable=true)
	public void sendGuildMsgTo(long guildId,Message msg) {
		if(guildId <= 0){
			return;
		}
		// 给所有有工会玩家发送消息
		for(HumanGlobalInfo info : datas.values()) {
			if(info.guildId == guildId) {
				HumanGlobalManager.inst().sendMsg(info.connPoint, msg);
			}
		}
	}

	/**
	 * 清除玩家全局信息
	 */
	@DistrMethod
	public void cancel(long humanId) {
		if (datas.containsKey(humanId)) {
			HumanGlobalInfo info = datas.get(humanId);
			Log.temp.error("===移除玩家={}, account={}", humanId, info.account);
			datas.remove(humanId);
			account2humans.remove(info.account);
			HumanServiceProxy proxy = HumanService.createProxy(humanId);
			proxy.delService(humanId);
		}
	}

	@DistrMethod
	public void stageIdModify(long humanId, long stageIdNew, int mapSnNew, String stageName, String nodeId, String portId, int lineNum) {
		HumanGlobalInfo info = datas.get(humanId);

		//特殊处理
		if(info == null) {
			Log.human.error("修改玩家全局地图信息时出错，玩家数据不存在："
							+ "humanId={}, stageIdNew={}, stageName={}, nodeId={}, portId={}",
					humanId, stageIdNew, stageName, nodeId, portId);
			return;
		}

		info.nodeId = nodeId;
		info.portId = portId;
		info.lineNum = lineNum;
	}



	/**
	 * 更新阵营
	 * @param humanId
	 */
	@DistrMethod
	public void updateCamp(long humanId,long campType){

		HumanGlobalInfo info = datas.get(humanId);
		if(info != null) {
			info.campType = campType;
		}
	}


	/**
	 * 更新等级
	 * @param humanId
	 * @param level
	 */
	@DistrMethod
	public void updateLevel(long humanId,int level){
		HumanGlobalInfo info = datas.get(humanId);
		if(info != null) {
			info.level = level;
		}
	}
	/**
	 * 更新名字
	 * @param humanId
	 * @param name
	 */
	@DistrMethod
	public void updateName(long humanId, String name){

		HumanGlobalInfo info = datas.get(humanId);
		if(info != null) {
			info.name = name;
		}
	}
	/**
	 * 发送补偿邮件(每发送100个人，等待1s)
	 * @param fillMail
	 */
	@DistrMethod
	public void sendFillMail(FillMail fillMail, boolean isInside){

		if(fillMail.getStartTime() > Port.getTime()){
			//未到邮件发送时间,返回
			return;
		}
		if(isInside){// 内部后台全服邮件
			for(HumanGlobalInfo info : datas.values()){
				HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId,info.id);
				prx.acceptFillMail(fillMail);
			}
		} else {// 日本后台全服邮件
			for(HumanGlobalInfo info : datas.values()){
				HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId,info.id);
				prx.sendFillMail(fillMail);
			}
		}

	}


	/**
	 *
	 * @param type 类型
	 * @param senderId 发送者humanId
	 * @param targetId 接收者humanId
	 * @param senderName 发送者名称
	 * @param senderLevel 发送者等级
	 * @param teamId 队伍Id
	 * @param factionId 阵营ID
	 */
	@DistrMethod
	public void addBallInfo(int type, long senderId, long targetId, String senderName, int senderLevel, long teamId, long factionId, String factionName, long senderCampType){
		HumanGlobalInfo info = datas.get(targetId);
		if(info != null) {
			HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, targetId);
//			prx.addBallInfo(type, senderId, senderName, senderLevel, teamId, factionId, factionName, senderCampType);
		}else{
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("type",type);
			paramMap.put("senderId",senderId);
			paramMap.put("senderName",senderName);
			paramMap.put("senderLevel",senderLevel);
			paramMap.put("teamId",teamId);
			paramMap.put("factionId",factionId);
			paramMap.put("factionName",factionName);
			paramMap.put("senderCampType",senderCampType);
			Pocket.add(targetId, PocketLineEventSubKey.NOTICE_BALL, Utils.toJSONString(paramMap));
		}
	}


	/**
	 * 切换到指定地图指定地点
	 * @param humanId
	 * @param stageId
	 */
	@DistrMethod
	public void switchTo(long humanId, long stageId, Object... params) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info != null) {
			HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, humanId);
			prx.switchTo(stageId, params);
		}
	}



	@DistrMethod
	public void sendMsgChatMessage(Param param) {
		HumanScopeKey type = Utils.getParamValue(param, "key", HumanScopeKey.ALL);
		long targetId = Utils.getParamValue(param, "targetId", 0L);
		int channel = Utils.getParamValue(param, "channel", 0);
		long sendHumanId = Utils.getParamValue(param, "sendHumanId", 0L);
		int serverId = Utils.getParamValue(param, "serverId", 0);
		int contentType = Utils.getParamValue(param, "contentType", 0);
		String content = Utils.getParamValue(param, "content", "");
		List<Define.p_link> links = Utils.getParamValue(param, "links", new ArrayList<>());
		List<Define.p_key_value_string> args = Utils.getParamValue(param, "args", new ArrayList<>());
		List<Long> list = Utils.getParamValue(param, "list", new ArrayList<>());

		if (!list.contains(sendHumanId)) {
			list.add(sendHumanId);
		}
		if (!list.contains(targetId)) {
			list.add(targetId);
		}

		int day = ConfGlobal.get(ConfGlobalKey.chat_records_time.SN).value;
		int sec = (int)(day * Time.DAY / Time.SEC);
		if (channel == Inform.私聊 && targetId != sendHumanId) {
			String key = Utils.createStr("{}{}{}", RedisKeys.chat2HumanUnread, targetId, sendHumanId);
			int num = Utils.intValue(Utils.getRedisStrValue(key)) + 1;
			RedisTools.setAndExpire(EntityManager.redisClient, key, String.valueOf(num), sec);
		}

		//获取符合条件的玩家
		List<HumanGlobalInfo> receiver = getInfoByScope(type, list, serverId);
		if (receiver.isEmpty()) {
			Log.inform.info("发送超链接sendInform消息到的区域无任何玩家, HumanScopeKey={}, type={}, list={}, serverId={}", type.toString(), type, list, serverId);
			return;
		}

		//发送人
		HumanGlobalInfo sender = null;
		if (sendHumanId == 1) {
			// 是系统消息
			sender = new HumanGlobalInfo();
			sender.id = sendHumanId;
			sender.name = "";
			sender.serverId = Config.SERVER_ID;
		} else if (sendHumanId > 0) {
			sender = datas.get(sendHumanId);
		}
		if (sender == null) {
			return;
		}
		MsgChat.chat_message_s2c.Builder msg = MsgChat.chat_message_s2c.newBuilder();
		msg.setChannel(channel);
		msg.setTargetId(sendHumanId);
		Define.p_chat.Builder dInfo = Define.p_chat.newBuilder();
		dInfo.setRoleId(sender.id);
		Define.p_head.Builder dHead = Define.p_head.newBuilder();
		// TODO
		dHead.setId(sender.headSn);
		dHead.setFrameId(sender.borderSn);
		dHead.setUrl(sender.url);
		dInfo.setHead(dHead);

		dInfo.setName(sender.name);
		dInfo.setGender(sender.sex);
		dInfo.setContent(content);
		dInfo.setServerId(sender.serverId);
		dInfo.setTime(Utils.getTimeSec());
		dInfo.setType(contentType);
		dInfo.addAllLinks(links);

		for(Define.p_key_value_string info : args){
			Define.p_chat_elem.Builder dElem = Define.p_chat_elem.newBuilder();
			dElem.setField(info.getK());
			dElem.setNum(info.getV());
			dElem.setString(info.getS());
			dInfo.addExtList(dElem);
		}
		Define.p_chat_elem.Builder dElemTitle = Define.p_chat_elem.newBuilder();
		dElemTitle.setField(ParamKey.elem_id_5);
		dElemTitle.setNum(sender.titleSn);
		dInfo.addExtList(dElemTitle);
		Define.p_chat_elem.Builder dElemBubble = Define.p_chat_elem.newBuilder();
		dElemBubble.setField(ParamKey.elem_id_7);
		dElemBubble.setNum(sender.bubble);
		dInfo.addExtList(dElemBubble);
		if(type == HumanScopeKey.UNION){
			Define.p_chat_elem.Builder dElemPos = Define.p_chat_elem.newBuilder();
			dElemPos.setField(ParamKey.elem_id_1);
			dElemPos.setNum(sender.guildPosition);
			dInfo.addExtList(dElemPos);
		}

		msg.setChatInfo(dInfo);

		for(HumanGlobalInfo r : receiver) {
			if(r.isCheckRobot){
				continue;
			}
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(r.nodeId, r.portId, r.id);
			// 发送聊天信息
			Param paramChat = new Param();
			paramChat.put("sendHumanId", sendHumanId);
			paramChat.put("targetId", targetId);
			paramChat.put("channel", channel);
			paramChat.put("chatInfo", dInfo.build());
			proxy.sendChatMsg(paramChat);
		}
	}

	@DistrMethod
	public void isInBlockList(long humanId, long targetId){
		long pid = port.createReturnAsync();
		HumanGlobalInfo humanGlobalInfo = datas.get(humanId);
		if (humanGlobalInfo != null) {
			// 在线
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(humanGlobalInfo.nodeId, humanGlobalInfo.portId, humanGlobalInfo.id);
			proxy.isInBlockList(targetId);
			proxy.listenResult((results, context) -> {
				boolean isBlock = Utils.getParamValue(results, "result", false);
				Log.temp.info("===isInBlockList humanId={}在线 targetI{} isBlock={}", humanId, targetId, isBlock);
				port.returnsAsync(pid, "isBlock", isBlock);
			});
		}
		else{
			// 不在线
			EntityManager.getEntityAsync(Friend.class, humanId, (res) -> {
				if (res.failed()) {
					Log.temp.warn("isInBlockList 获取Friend数据失败，humanId={}", humanId);
					port.returnsAsync(pid, "isBlock", false);
					return;
				}
				Friend friend = res.result();
				if (friend == null) {
					Log.temp.warn("isInBlockList 获取Friend数据失败，humanId={}", humanId);
					port.returnsAsync(pid, "isBlock", false);
					return;
				}
				List<Long> blockIdList = Utils.strToLongList(friend.getBlackList());
				boolean isBlock = blockIdList.contains(targetId);
				Log.temp.info("===isInBlockList humanId={}离线 targetI{} isBlock={}", humanId, targetId, isBlock);
				port.returnsAsync(pid, "isBlock", isBlock);
			});
		}
	}

	private List<HumanGlobalInfo> getInfoByScope(HumanScopeKey scope, List<Long> keys, int serverId){
		List<HumanGlobalInfo> list = new ArrayList<>();
		//所有玩家
		if(scope.equals(HumanScopeKey.ZONEALL)){
			list.addAll(datas.values());
			return list;
		}

		//其余情况需要单独判断
		for(HumanGlobalInfo i : datas.values()){
			if(i.isCheckRobot){
				continue;
			}
			switch (scope) {
				case HUMAN: {	//单个玩家
					if(keys.contains(i.id)) list.add(i);
				}
					break;
				case STAGE: {	//地图上的玩家
				}
					break;
				case COUNTRY: {	//阵营
					if(keys.contains(i.campType)) list.add(i);
				}
					break;
				case UNION: {	//帮会
					if(keys.contains(i.guildId)) list.add(i);
				}
					break;
				case TEAM: {	//组队
					if(keys.contains(i.teamId) || keys.contains(i.battleTeamId)) list.add(i);
				}
					break;
				case ALL:{
					if(keys.contains(i.id)) {
						list.add(i);
						continue;
					}
//					int zoneCode = Util.getServerIdZoneCode(i.serverId);
//					int group2 = Util.getServerIdGroup(info.serverId);
					list.add(i);
				}
					break;
				default:
					break;
			}
		}

		return list;
	}



	@DistrMethod
	public void sendAllServerInform(HumanScopeKey type, List<Long> keys, int channel, List<String> list) {

	}

	/**
	 * 根据条件筛选在线玩家
	 * @param humanId
	 * @param friendListStr
	 * @param blackListStr
	 * @param combat
	 */
	@DistrMethod
	public void getRecommendUsers(long humanId, String friendListStr ,String blackListStr, int combat) {
		List<HumanGlobalInfo> recommendList = new ArrayList<HumanGlobalInfo>();
		JSONArray friendList = Utils.toJSONArray(friendListStr);
		JSONArray blackList = Utils.toJSONArray(blackListStr);
		//先剔除在好友名单和黑名单的
		List<HumanGlobalInfo> users = new ArrayList<HumanGlobalInfo>();
		for (HumanGlobalInfo info : datas.values()) {
			//如果是自己，则不推荐
			if (humanId == info.id) continue;

			boolean flag = false ;//标志位
			//判断是否在好友名单
			for (int i = 0; i < friendList.size(); i++) {
				if (info.id == friendList.getLongValue(i)) {
					flag = true;
					break;
				}
			}
			if (flag) continue;
			//判断是否在黑名单
			for (int i = 0; i < blackList.size(); i++) {
				if (info.id == blackList.getLongValue(i)) {
					flag = true;
					break;
				}
			}
			if (flag) continue;
			users.add(info);//满足三个条件，则将玩家信息加入列表返回
		}
		port.returns("recommendList", recommendList);
	}

	@ScheduleMethod(DataResetService.CRON_DAY_ZERO)
	public void schdDayZeroReset() {
		long timeNow = Port.getTime();
		shceduleMap.put(EventKey.HUMAN_RESET_ZERO, timeNow);
	}

	@ScheduleMethod(DataResetService.CRON_DAY_FIVE)
	public void schdDayFiveReset() {
		long timeNow = Port.getTime();
		shceduleMap.put(EventKey.HUMAN_RESET_FIVE, timeNow);

	}

	@ScheduleMethod(DataResetService.CRON_DAY_12ST)
	public void schdDay12STReset() {
		long timeNow = Port.getTime();
		shceduleMap.put(EventKey.HUMAN_RESET_12ST, timeNow);
	}

	@ScheduleMethod(DataResetService.CRON_DAY_18ST)
	public void schdDay18STReset() {
		long timeNow = Port.getTime();
		shceduleMap.put(EventKey.HUMAN_RESET_18ST, timeNow);
	}

	@ScheduleMethod(DataResetService.CRON_DAY_21ST)
	public void schdDay21STReset() {
		long timeNow = Port.getTime();
		shceduleMap.put(EventKey.HUMAN_RESET_21ST, timeNow);
	}

	@ScheduleMethod(DataResetService.CRON_WEEK_ZERO)
	public void schdWeekZeroReset() {
		long timeNow = Port.getTime();
		shceduleMap.put(EventKey.HUMAN_RESET_WEEK_ZERO, timeNow);
	}

	@ScheduleMethod(DataResetService.CRON_WEEK_135)
	public void schdWeek135Reset() {
		long timeNow = Port.getTime();
		shceduleMap.put(EventKey.HUMAN_RESET_WEEK_135, timeNow);
	}

	@ScheduleMethod(DataResetService.CRON_WEEK_345_hour_22)
	public void _CRON_WEEK_345_hour_22(){
		long timeNow = Port.getTime();
		shceduleMap.put(EventKey.CRON_WEEK_345_hour_22, timeNow);
	}



	@ScheduleMethod(DataResetService.CRON_DAY_HOUR)
	public void schdEveryHourReset() {
		long timeNow = Port.getTime();
		shceduleMap.put(EventKey.HUMAN_RESET_EVERY_HOUR, timeNow);
	}

	@ScheduleMethod(DataResetService.CRON_WEEK_1_5ST)
	public void schdWeek15STReset() {
		long timeNow = Port.getTime();
		shceduleMap.put(EventKey.HUMAN_RESET_WEEK_5ST, timeNow);
	}

	@ScheduleMethod(DataResetService.CRON_MONTH_1_1ST)
	public void schdMonth1STReset() {
		long timeNow = Port.getTime();
		shceduleMap.put(EventKey.HUMAN_RESET_MONTH_1ST, timeNow);
		Log.game.error("每月一号零点发布事件  当前时间 {}",timeNow);
	}

	/**
	 * 在一个时间对所有玩家发布一个事件
	 * @param key
	 * @param
	 */
	@DistrMethod
	public void fireEvent(int key){
		shceduleMap.put(key, Port.getTime());
	}

	/**
	 * 禁言/封号
	 * @param humanId
	 * @param type (1:禁言   2:封号	3:喇叭禁言)
	 * @param endTime
	 */
	@DistrMethod
	public void sealAccount(long humanId, int type, long endTime) {
		HumanGlobalInfo info = datas.get(humanId);

		//更新数据
		if(info != null){
			HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			prx.sealAccount(type, endTime);
		}else {
			Human human = (Human) EntityManager.getEntity(Human.class, humanId);
			if (human == null) {
				return;
			}
			EntityManager.getEntityListAsync(Account.class, human.getAccount(), res -> {
				if (res.failed()) {
					Log.temp.error("accountManager.loadRedis failed: account={}, serverId={}", human.getAccount(), res.cause());
					return;
				}
				List<Account> accountList = res.result();
				for (Account accountTemp : accountList) {
					if (accountTemp.getId() == humanId) {
						if (type == 1) {
							accountTemp.setSilenceEndTime(endTime);
						} else if (type == 2) {
							accountTemp.setSealEndTime(endTime);
							RankManager.inst().removeRank(humanId);
						}
						accountTemp.update();
						return;
					}
				}
			});
		}
	}


	/**
	 * 禁言
	 * @param operCampType 操作者的阵营类型
	 * @param humanId
	 * @param keepTime 持续时间(单位:毫秒)
	 */
	@DistrMethod
	public void silence(long operHumanId, int operCampType, long humanId, long keepTime){
		HumanGlobalInfo info = datas.get(humanId);
		if(info != null) {
			//在线
			if(operCampType > 0 && operCampType != info.campType){
//				Inform.sendInform(operHumanId, 7317);
				return;
			}
			HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId,info.id);
			prx.silence(keepTime);
			//禁言成功
//			Inform.sendInform(operHumanId, 7316);
		}else{

			//不在线，添加代办
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("operCampType",operCampType);
			paramMap.put("keepTime",keepTime);
			Pocket.add(humanId, PocketLineEventSubKey.SILENCE, Utils.toJSONString(paramMap));
		}
	}

	/**
	 * 每6分钟一次，用户体力恢复
	 */
	@ScheduleMethod("0 0/6 * * * ?")
	public void schdEvery6Min() {
		long timeNow = Port.getTime();
		shceduleMap.put(EventKey.HUMAN_RESET_6MIN, timeNow);
	}


	public void onSchdule() {
		if(shceduleMap.size() == 0) {
			return;
		}

		int i = 0;
		long now = Port.getTime();
		//todo: 这段代码的控制可以进一步改进
		for (HumanGlobalInfo obj : datas.values()) {
			boolean sendSchd = false;
			for (Entry<Integer, Long> entry: shceduleMap.entrySet()) {
				if(obj.timeLogin < entry.getValue()) {
					//发送到玩家
					HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(obj.nodeId, obj.portId, obj.id);
					proxy.onSchedule(entry.getKey(), entry.getValue());
					sendSchd = true;
				}
			}

			obj.timeLogin = now;

			if(sendSchd) {
				i++;
				//超过数量返回
				if(i > COUNT100Per10SEC) {
					return;
				}
			}

		}
		if(i == 0) {
			shceduleMap.clear();
		}
	}

	/**
	 * 统计注册人数和在线人数，写日志
	 */
	public void writeOnlineLog() {
		if(S.isRedis){
			//添加在线日志
			logOnlineAndRegisterNum();
			return;
		}
		//获得数量
		DB db = DB.newInstance(Human.tableName);
		db.countAll(false);
		db.listenResult(this::_result_writeOnlineLog);
	}

	public void logOnlineAndRegisterNum(){
		Map<Integer, Integer> serverIdNumMap = new HashMap<Integer, Integer>();
		for(HumanGlobalInfo info : datas.values()){
			serverIdNumMap.put(info.serverId, serverIdNumMap.getOrDefault(info.serverId, 0) + 1);
		}

		for(Entry<Integer, Integer> entry : serverIdNumMap.entrySet()){
			LogOp.log(LogOpChannel.ONLINE,
					0,
					entry.getValue(),
					Port.getTime(),
					Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
					entry.getKey()
			);
		}
	}

	public void _result_writeOnlineLog(Param results, Param context) {
		int registerCount = results.get();
		int onlineCount = datas.size();
		//添加在线日志
		LogOp.log(LogOpChannel.ONLINE,
				registerCount,
				onlineCount,
				Port.getTime(),
				Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
				Config.GAME_SERVER_ID
		);
	}


	@DistrMethod
	public void onAuctionTimeOut(long humanId, int sn, int num){
		HumanGlobalInfo info = datas.get(humanId);
		if(info != null){//只推在线，离线玩家上线后会检查
			HumanObjectServiceProxy prx = HumanObjectServiceProxy
					.newInstance(info.nodeId, info.portId, info.id);
//			prx.auctionTimeOut(humanId, sn, num);
		}else{
			Map<String, Object> paramMap = new HashMap<String, Object>();
			paramMap.put("sn", sn);
			paramMap.put("num", num);
//			Pocket.add(humanId, PocketLineEventSubKey.AUCTION_ITEM_TIMEUP,Utils.toJSONString(paramMap));
		}
	}


	@DistrMethod
	public void updateSign(long humanId, String sign){
		HumanGlobalInfo info = datas.get(humanId);
		if(info != null) {
			info.sign = sign;
		}
	}

	/**
	 * 更新渠道的注册人数
	 */
	@DistrMethod
	public void channelCountAdd(String channel){
		//总注册数+1
		ChannelMax totalCx = channelMap.get("total");
		totalCx.now = totalCx.now + 1;

		//渠道注册数+1
		ChannelMax cx = channelMap.get(channel);
		if(cx == null){
			return;
		}
		cx.now = cx.now + 1;

	}




	@DistrMethod
	public void leaveRep(long humanId) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info != null) {
			HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, humanId);
			prx.doLeaveRep();
		}
	}

	@DistrMethod
	public void onTeamMemberKick(long teamId, long memberId) {

	}

	@DistrMethod
	public void freeSlave(long humanId, long slaveId) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info != null) {
			HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, humanId);
			prx.freeSlave(slaveId);
		}
		EntityManager.getEntityAsync(Capture.class, humanId, res->{
			if(res.failed()){
				Log.game.error("释放从属失败，查询Capture对象错误，humanId={}");
				return;
			}
			Capture capture = res.result();
			if (capture == null){
				Log.game.error("释放从属失败，找不到Capture对象，humanId={}");
				return;
			}
			CaptureSlaveManager.inst().freeSlave(capture, slaveId);
		});
	}

	@DistrMethod
	public void captureAddEnemy(long humanId, long enemyId) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info != null) {
			HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, humanId);
			prx.captureAddEnemy(enemyId);
		}
		Capture capture = (Capture) EntityManager.getEntity(Capture.class, humanId);
		if (capture != null) {
			CaptureSlaveManager.inst().addEnemy(capture, enemyId);
		}
	}



	/**
	 * 向后台推送在线人数
	 */
	public void getOnlineNumber () {
		// 跨服不处理
		if(S.isBridge) {
			return;
		}

//		JSONObject upData = new JSONObject();
//		String gameServerId = Config.GAME_SERVER_ID;
//		int number = datas.size();
//		upData.put("serverId",gameServerId);
//		upData.put("number",number);
//		String portHttpAsync = ConstPf.PORT_HTTP_ASYNC_PREFIX + new Random().nextInt(ConstPf.PORT_STARTUP_NUM_CHAT);
//		HttpAsyncSendServiceProxy asyncSendProxy = HttpAsyncSendServiceProxy.newInstance(ConstPf.NODE_ID, portHttpAsync, ConstPf.SERV_HTTP_SEND);
//		asyncSendProxy.httpPostAsync(PLATFORM_URL, upData, false);
	}

	@DistrMethod
	public void backMethod1(String param) {
	}

	@DistrMethod
	public void backMethod2(String param) {

	}

	@DistrMethod
	public void backupMethod3(String param) {

	}

	@DistrMethod
	public void backupMethod4(String param) {

	}


	@DistrMethod
	public void reserveMethod3(String param){

	}
	@DistrMethod
	public void reserveMethod4(String param){

	}



	//	/**
//	 * 切换地图
//	 * @param humanId
//	 * @param stageId
//	 * @param mapSn
//	 * @param pos
//	 * @param dir
//	 */
//	@DistrMethod
//	public void switchToMap(long humanId, long stageId, int mapSn, Vector2D pos, Vector2D dir){
//		HumanGlobalInfo info = datas.get(humanId);
//		if(info == null) {
//			return;
//		}
//		HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId,humanId);
//		prx.switchToMap(stageId, mapSn, pos, dir);
//	}



	/**
	 * 将玩家全局信息同步到跨服服务器
	 * @param humanId
	 * @param stageIdNew
	 * @param stageSn
	 * @param stageName
	 * @param nodeId
	 * @param portId
	 */
	@DistrMethod
	public void registerToBridge(long humanId, long stageIdNew, int stageSn, String stageName, String nodeId, String portId,int lineNum, boolean isLeague) {
		HumanGlobalInfo info = datas.get(humanId);

		//特殊处理
		if(info == null) {
			Log.human.error("向跨服服务器注册玩家全局信息时出错，玩家数据不存在："
							+ "humanId={}, stageIdNew={}, stageName={}, nodeId={}, portId={}",
					humanId, stageIdNew, stageName, nodeId, portId);
			return;
		}
		String bridgeNodeId = isLeague ? D.NODE_BRIDGE_LEAGUE : D.NODE_BRIDGE_DEFAULT;
		//调用已有接口进行注册和数据修改
		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance(bridgeNodeId);
//		prx.register(info);
//		prx.stageIdModify(humanId, stageIdNew, stageSn, stageName, nodeId, portId,lineNum);
	}


	/**
	 * 添加待办
	 * @param humanId
	 * @param pockLineTypeKey
	 * @param param
	 */
	@DistrMethod
	public void pockLineAdd(long humanId, String pockLineTypeKey, String param){
		Log.temp.error("===humanId={}, pocketTypeKey={}, param={}, id={}", humanId, pockLineTypeKey, param);
		// 创建待办
		PocketLine pocketLine = PocketLineManager.inst().addPocketLine(humanId, pockLineTypeKey, param);

		// 处理待办
		pockLineProcess(humanId, pocketLine);
	}

	@DistrMethod
	public void addPocketLine(long humanId, PocketLine pocketLine){
		// 通知humanObject service处理待办
		pockLineProcess(humanId, pocketLine);
	}

	/**
	 * 通知处理待办
	 * @param humanId
	 * @param pocketLine
	 */
	public void pockLineProcess(long humanId, PocketLine pocketLine){
		HumanGlobalInfo info = datas.get(humanId);
		if(info == null) {
			return;
		}
		// 通知humanObject service处理待办
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, humanId);
		proxy.pockLineProcess(pocketLine);
	}

	/**
	 * 让玩家进入跨服地图
	 *
	 * @param humanId
	 * @param stageId
	 */
	@DistrMethod
	public void bridgeStageEnter(long humanId, long stageId, Object... params) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			Log.temp.info("===humanId={}, stageId={}, params={}, info={}", humanId, stageId, params, info);
			return;
		}
		//进入跨服地图
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.switchTo(stageId, params);
	}


	@DistrMethod
	public void bridgeStageInSet(long humanId, boolean stageIn) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			return;
		}

		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.bridgeStageInSet(stageIn);
	}

	/**
	 * 进入跨服地图，跨服地图设置
	 *
	 * @param humanId
	 */
	@DistrMethod
	public void bridgeStageHistorySet(long humanId, long stageId, int stageSn, String stageType, Vector2D pos) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			return;
		}

		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.bridgeStageHistorySet(stageId, stageSn, stageType, pos);
	}

	/**
	 * 在跨服服务器掉线，需要在游戏服务器下线
	 *
	 * @param humanId
	 */
	@DistrMethod
	public void bridgeLogout(long humanId) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			return;
		}

		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.bridgeLogout();
	}


	/**
	 * 跨服地图位置坐标更新，暂时只认为玩家跨服时只在一张地图
	 *
	 * @param humanId
	 */
	@DistrMethod
	public void bridgeStagePosUpdate(long humanId, Vector2D pos) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			return;
		}

		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.bridgeStagePosUpdate(pos);
	}


	/**
	 * 跨服地图位置坐标更新，暂时只认为玩家跨服时只在一张地图
	 *
	 * @param humanId
	 */
	@DistrMethod
	public void bridgeItemUse(long humanId, int packType , int itemSn , int num) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			Log.item.error("跨服使用道具时，没有找到玩家信息 humanId {} , itemId {} , num {}",humanId, itemSn,num);
			return;
		}

		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.bridgeItemUse(packType, itemSn,num);
	}

	@DistrMethod
	public void bridgeItemUse(long humanId, int packType, int itemSn, int num, MoneyItemLogKey log){
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			Log.item.error("跨服使用道具时，没有找到玩家信息 humanId {} , itemId {} , num {}",humanId,itemSn,num);
			return;
		}

		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.bridgeItemUse(packType,itemSn,num,log);
	}



	/**
	 * 事件触发，对于统一进度的成就更新，例如宗门祈福累计x人，这个时候参加祈福的所有人都要更新成统一进度
	 * @param humanIds
	 * @param eventKey
	 * @param param
	 */
	@DistrMethod
	public void eventFire(List<Long> humanIds, int eventKey, Param param) {
		for (Long humanId : humanIds) {
			HumanGlobalInfo info = datas.get(humanId);
			if (info != null) {
				HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id).eventFire(eventKey, param);
			}
		}
	}

	@DistrMethod(argsImmutable=true)
	public void sendMsgServerIdToAll(int serverId, List<Long> excludeIds, Message msg) {
		if(excludeIds ==  null){
			excludeIds = new ArrayList<>();
		}
		int group1 = Util.getServerIdGroup(serverId);
		// 给所有玩家发送消息
		for(HumanGlobalInfo info : datas.values()) {
			// 排除不需要发送的玩家id
			if(excludeIds.contains(info.id)) {
				continue;
			}
			int group2 = Util.getServerIdGroup(info.serverId);
			// 是否同一服务器组
			if(group2 != group1){
				continue;
			}
			HumanGlobalManager.inst().sendMsg(info.connPoint, msg);
		}
	}

	@DistrMethod
	public void syncInfoTime(long humanId){
		HumanGlobalInfo info = datas.get(humanId);
		if(info != null){
			info.syncTime = Port.getTime();
		}
	}

	@DistrMethod
	public void humanLoginGameState(long humanId){
		HumanGlobalInfo info = datas.get(humanId);
		if(info == null){
			Log.temp.error("===humanLoginGameState玩家不在线，humanId={}", humanId);
			return;
		}
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.humanLoginGameState();
	}

	@DistrMethod
	public void produceAdd(long humanId, int itemSn, int itemNum, MoneyItemLogKey log, Object... obj){
		HumanGlobalInfo info = datas.get(humanId);
		if(info == null){
			Log.temp.error("===produceAdd，humanId={}, itemSn={}, itemNum={}, log={}, obj={}", humanId, itemSn, itemNum, log, obj);
			return;
		}
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.produceAdd(itemSn, itemNum, log, obj);
	}

	@DistrMethod
	public void produceAdd(long humanId, Map<Integer, Integer> itemNumMap, MoneyItemLogKey log, Object... obj){
		HumanGlobalInfo info = datas.get(humanId);
		if(info == null){
			Log.temp.error("===produceAdd，humanId={}, itemNumMap={}, log={}, obj={}", humanId, itemNumMap, log, obj);
			return;
		}
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.produceAdd(itemNumMap, log, obj);
	}

	@DistrMethod
	public void leaveGuild(long humanId, long guildId){
		HumanGlobalInfo info = datas.get(humanId);
		if(info == null){
			Param param = new Param();
			param.put("humanId", humanId);
			param.put("guildId", guildId);
			param.put("time", Port.getTime());
			// 待办
			PocketLineManager.inst().addPocketLine(humanId, PocketLineEventSubKey.GUILD_KICK, param.toJsonString());
			return;
		}
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.leaveGuild(guildId);
	}

	@DistrMethod
	public void denyApply(long humanId, String guildName){
		HumanGlobalInfo info = datas.get(humanId);
		if(info == null){
			return;
		}
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.denyApply(guildName);

	}


	@DistrMethod
	public void leaveGuild(List<Long> humanIdList, long guildId){
		for(long humanId : humanIdList){
			leaveGuild(humanId, guildId);
		}
	}

	@DistrMethod
	public void updateGuildInfo(List<Long> humanIdList, Guild guild) {
		for (long humanId : humanIdList) {
			HumanGlobalInfo info = datas.get(humanId);
			if (info == null) {
				Param param = new Param("humanId", humanId, "guildId", guild.getId());
				PocketLineManager.inst().addPocketLine(humanId, PocketLineEventSubKey.GUILD_UPDATE, param.toJsonString());
			} else {
				HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
				proxy.updateGuildInfo(guild);
			}
		}
	}

	@DistrMethod
	public void help(long humanId, int type, int subType) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			Param param = new Param();
			param.put("humanId", humanId);
			param.put("type", type);
			param.put("targetId", subType);
			param.put("time", Port.getTime());
			// 待办
			PocketLineManager.inst().addPocketLine(humanId, PocketLineEventSubKey.GUILD_HELP, param.toJsonString());
			return;
		}
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.help(type, subType);
	}
	@DistrMethod
	public void updateBuildPorpCalc(long humanId){
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			return;
		}
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.updateBuildPorpCalc();
	}

	@DistrMethod
	public void checkAndConsume(long humanId, Map<Integer, Integer> costItemMap, MoneyItemLogKey logKey) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			port.returns("result", new ReasonResult(false));
			return;
		}

		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.checkAndConsume(costItemMap, logKey);
		proxy.listenResult(this::_result_checkAndConsume, "pid", port.createReturnAsync());
	}

	private void _result_checkAndConsume(Param results, Param context) {
		ReasonResult rr = Utils.getParamValue(results, "result", new ReasonResult(false));
		long pid = Utils.getParamValue(context, "pid", 0L);
		port.returnsAsync(pid, "result", rr);
	}

	@DistrMethod
	public void areaEnter(){

	}

	@DistrMethod
	public void areaExit(){

	}

	@DistrMethod
	public void openActivity(int serverId, List<ActivityVo> activityVos){
		for(HumanGlobalInfo info : datas.values()){
			if(info.serverId != serverId){
				continue;
			}
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			proxy.openActivity(activityVos);
		}
	}

	@DistrMethod
	public void closeActivity(int serverId, List<Integer> activityIdList){
		for(HumanGlobalInfo info : datas.values()){
			if(info.serverId != serverId){
				continue;
			}
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			proxy.closeActivity(activityIdList);
		}
	}

	@DistrMethod
	public void endShowActivity(int serverId, List<Integer> endShowList) {
		for(HumanGlobalInfo info : datas.values()){
			if(info.serverId != serverId){
				continue;
			}
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			proxy.endShowActivity(endShowList);
		}
	}
	@DistrMethod
	public void guildGveJoin(List<Long> humanIdList, int gveSn, int mailSn, long time) {
		for (long humanId : humanIdList) {
			HumanGlobalInfo info = datas.get(humanId);
			if (info == null) {
				JSONObject jo = new JSONObject();
				jo.put("gveSn", gveSn);
				jo.put("mailSn", mailSn);
				jo.put("id", humanId);
				jo.put("time", time);
				Pocket.add(humanId, PocketLineEventSubKey.GUILD_GVE_ONLINE, jo.toJSONString());
				continue;
			}
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			proxy.guildGveJoin(gveSn, mailSn, time);
		}
	}

	@DistrMethod
	public void sendGveKillMail(List<Long> humanIdList, int gveSn, int mailSn) {
		for (long humanId : humanIdList) {
			HumanGlobalInfo info = datas.get(humanId);
			if (info == null) {
				JSONObject jo = new JSONObject();
				jo.put("gveSn", gveSn);
				jo.put("mailSn", mailSn);
				jo.put("id", humanId);
				Pocket.add(humanId, PocketLineEventSubKey.GUILD_GVE_KILL, jo.toJSONString());
				continue;
			}
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			proxy.sendGveKillMail(gveSn, mailSn);
		}
	}

	@DistrMethod
	public void gveSettle(Set<Long> humanIdList, int gveSn) {
		for (long humanId : humanIdList) {
			HumanGlobalInfo info = datas.get(humanId);
			if (info == null) {
				JSONObject jo = new JSONObject();
				jo.put("gveSn", gveSn);
				Pocket.add(humanId, PocketLineEventSubKey.GUILD_GVE_SETTLE, jo.toJSONString());
				continue;
			}
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			proxy.gveSettle(gveSn);
		}
	}

	@DistrMethod
	public void updateGuildPosition(long humanId, int guildPosition){
		HumanGlobalInfo info = datas.get(humanId);
		if(info == null){
			return;
		}
		info.guildPosition = guildPosition;
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.updateGuildPosition(guildPosition);
	}

	@DistrMethod
	public void sendGuildTreasureBoxInfo(List<Long> humanIdList) {
		for (Long humanId : humanIdList) {
			HumanGlobalInfo info = datas.get(humanId);
			if (info == null) {
				return;
			}
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			proxy.sendGuildTreasureBoxInfo();
		}
	}

	@DistrMethod
	public void triggerMsgIdOperate(long humanId, int msgId){
		HumanGlobalInfo info = datas.get(humanId);
		if(info == null){
			return;
		}
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.triggerMsgIdOperate(msgId);
	}

	@DistrMethod
	public void operation(int type, Object... objs){
		switch(type){
			case ParamKey.operationType_1:// 公会状态改变
				for(HumanGlobalInfo info : datas.values()){
					HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
					proxy.triggerMsgIdOperate(MsgIds.gvg_info_c2s);
				}
				break;
		}
	}

    @DistrMethod
    public void sendArenaRankedSettle(Map<Long, ArenaRankedBrief> map) {
		if (map == null || map.isEmpty()) {
			Log.temp.warn("===sendArenaRankedSettle: map为空或无数据");
			port.returns("result", false);
			return;
		}
		Log.temp.info("===sendArenaRankedSettle, size={}", map.size());
		// TODO 先发在线玩家量少还行，但量大需要考虑全区全服同时在处理，干脆全批量发
//		for(HumanGlobalInfo hInfo : datas.values()){
//			if(hInfo == null){
//				continue;
//			}
//			ArenaRankedBrief info = map.get(hInfo.id);
//			if (info != null) {
//				ArenaManager.inst().sendRankedSettle(info);
//				map.remove(hInfo.id);
//			}
//		}
//		if (!map.isEmpty()) {
			arenaRankedBriefList.addAll(map.values());
			ttSec.reStart();
			Log.temp.info("===sendArenaRankedSettle, listSize={}", arenaRankedBriefList.size());
//		}
		port.returns("result", true);
    }

	private void sendArenaRankedSettle() {
		if (arenaRankedBriefList == null || arenaRankedBriefList.isEmpty()) {
			ttSec.stop();
			return;
		}
		int num = Math.min(10, arenaRankedBriefList.size());
		for(int i = 0; i < num; i++){
			if(arenaRankedBriefList == null || arenaRankedBriefList.isEmpty()){
				break;
			}
			ArenaRankedBrief info = arenaRankedBriefList.poll();
			ArenaManager.inst().sendRankedSettle(info);
		}
		if(arenaRankedBriefList.isEmpty()){
			ttSec.stop();
		}
	}

	@DistrMethod
	public void getHumanBrief(long humanId){
		long pid = port.createReturnAsync();
		EntityManager.getEntityAsync(HumanBrief.class, humanId, (res)->{
			if(res.succeeded()){
				HumanBrief brief = res.result();
				if(brief != null && brief.getRoleFigure().length > 0 && brief.getBattleRole().length > 0){
					HumanManager.inst().syncHumanBrief(brief);
					port.returnsAsync(pid, "humanBrief", brief);
					return;
				}
			}
			Log.temp.error("获取玩家简要信息时出错, id={}, e={}", humanId, res.cause());
			List<Long> humanIdList = new ArrayList<>();
			humanIdList.add(humanId);
			HumanManager.inst().loadHumanIdSyncCross(humanIdList, res2->{
				if(res2.failed()){
					Log.temp.info("loadHumanIdSyncCross fail", res.cause());
					port.returnsAsync(pid, "humanBrief", null);
					return;
				}
				List<HumanBrief> briefList = res2.result();
				if(briefList == null || briefList.isEmpty()){
					Log.temp.info("loadHumanIdSyncCross briefList is empty");
					port.returnsAsync(pid, "humanBrief", null);
					return;
				}
				HumanBrief humanBrief =  briefList.get(0);
				Log.temp.error("从humanData构造humanBrief成功, id={}", humanId);
				port.returnsAsync(pid, "humanBrief", humanBrief);
			});
		});
	}

	@DistrMethod
	public void getHumanBrief2(long humanId){
		long pid = port.createReturnAsync();
		EntityManager.getEntityAsync(HumanBrief.class, humanId, (res)-> {
			if (res.succeeded()) {
				HumanBrief brief = res.result();
				if (brief != null && brief.getRoleFigure().length > 0 && brief.getBattleRole().length > 0) {
					HumanManager.inst().syncHumanBrief(brief);
					port.returnsImmutableAsync(pid, "humanBrief", brief);
					return;
				}
			}
			Log.temp.error("获取玩家简要信息2时出错, id={}", humanId, res.cause());
			HumanData.getHumanDataAsync(humanId, HumanManager.humanClasses, ret2 -> {
				if (ret2.failed()) {
					port.returnsAsync(pid, "humanBrief", null);
					Log.temp.error("获取玩家简要信息2取HumanData时出错, id={}, e={}", humanId, ret2.cause());
					return;
				}
				HumanData humanData = ret2.result();
				if (humanData == null || humanData.human == null || humanData.human2 == null) {
					port.returnsAsync(pid, "humanBrief", null);
					Log.temp.error("获取玩家简要信息2取HumanData时出错, id={}, e={}", humanId, ret2.cause());
					return;
				}
				try {
					HumanBrief brief = HumanManager.inst().createHumanBrief(humanData, true);
                    HumanManager.inst().syncHumanBrief(brief);
					port.returnsImmutableAsync(pid, "humanBrief", brief);
					return;
				} catch (Exception e) {
					Log.temp.info("==构建humanBrief数据出问题 {}", e);
				}
				port.returnsAsync(pid, "humanBrief", null);
				Log.temp.error("获取玩家简要信息2取HumanData时出错, id={}, e={}", humanId, ret2.cause());
			});
		});
	}

	/**
	 * 构造p_battle_role消息
	 * @param humanId				玩家id
	 * @param containBattleData		是否包含战斗数据
	 */
	@DistrMethod
	public void getPBattleRoleMsg(long humanId, boolean containBattleData) {
		HumanGlobalInfo info = datas.get(humanId);
		long pid = port.createReturnAsync();
		if (info == null) {
			Port currPort = port;
			Class<?>[] queryTable = containBattleData ? HumanManager.humanClasses : HumanManager.humanClasses2;
			HumanData.getHumanDataAsync(humanId, queryTable, handler -> {
				if (handler.failed()) {
					Log.human.error("获取玩家p_battle_role消息时出错, id={}, e={}", humanId, handler.cause().getMessage(), handler.cause());
					currPort.returnsAsync(pid, "p_battle_role", null);
					return;
				}
				Define.p_battle_role msg;
				HumanData humanData = handler.result();
				if (containBattleData) {
					TeamMember member = new TeamMember(humanData);
					msg = member.p_battle;
				} else {
					msg = HumanManager.inst().to_p_battle_role(humanData.human, humanData.human2);
				}
				currPort.returnsAsync(pid, "p_battle_role", msg);
			});
		} else {
			HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			prx.getPBattleRoleMsg(containBattleData);
			prx.listenResult((result, context) -> {
				Define.p_battle_role msg = result.get("p_battle_role");
				port.returnsAsync(pid, "p_battle_role", msg);
			});
		}
	}

	/**
	 * 跨服战击杀玩家
	 * @param humanId
	 */
	@DistrMethod
	public void crossWarKillPlayer(long humanId, boolean isInvade) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			return;
		}
		Log.crossWar.info("===crossWarKillPlayer humanId={} isInvade={}", humanId, isInvade);
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.crossWarKillPlayer(isInvade);
	}

	/**
	 * 跨服战击杀怪物
	 * @param humanId
	 */
	@DistrMethod
	public void crossWarKillMonster(long humanId, boolean isInvade) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			return;
		}
		Log.crossWar.info("===crossWarKillMonster humanId={} isInvade={}", humanId, isInvade);
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.crossWarKillMonster(isInvade);
	}

	/**
	 * 跨服战单次入侵结束
	 * @param humanId
	 * @param score
	 */
	@DistrMethod
	public void crossWarInvadeEnd(long humanId, int score) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			return;
		}
		Log.crossWar.info("===crossWarInvadeEnd humanId={} score={}", humanId, score);
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.crossWarInvadeEnd(score);
	}

	/**
	 * 跨服战结算
	 */
	@DistrMethod
	public void onCrossWarEnd() {
		Log.crossWar.info("跨服战结算! 更新跨服战活动状态 playerNum={}", datas.size());
		for(HumanGlobalInfo info : datas.values()) {
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			proxy.onCrossWarEnd();
		}
	}

	@DistrMethod
	public void createAreanHistory(long humanId, long vid){
		String key = RedisKeys.arenaRankHistory + humanId;
		RedisTools.pushToListHeadAndTrim(EntityManager.redisClient,key,String.valueOf(vid), GlobalConfVal.arenaHistoryNum,res->{
			if(res.failed()){
				Log.temp.error("===保存排位赛历史记录失败，key={},vid={}", key, vid);
			}
		});
//		List<String> keyList = new ArrayList<>();
//		keyList.add(key);
//		keyList.add(String.valueOf(Utils.getTimeSec()));
//		keyList.add(String.valueOf(vid));
//		EntityManager.redisClient.zadd(keyList, r->{
//			if(!r.succeeded()){
//				Log.temp.error("===保存失败，keyList={}", keyList);
//			}
//		});
//		Utils.redisZrem(key);
	}

	@DistrMethod
	public void gmCallHumanServiceMethod(Param param){
		long humanId = param.getLong("humanId");
		HumanGlobalInfo info = datas.get(humanId);
		if(info == null){
			return;
		}
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.gmCallHumanServiceMethod(param);
	}

	@ScheduleMethod(DataResetService.CRON_WEEK_4_1_30_MIN)
	public void CRON_WEEK_4_1_30_MIN(){
		CrossRedis.get(RedisKeys.arenaRankedSeason, f -> {
			if (f.failed()) {
				Log.temp.error("===获取{}数据失败。e={}", RedisKeys.arenaRankedSeason, f.cause());
				return;
			}
			int seasonNow = Utils.intValue(f.result());
			String redisKey = RedisKeys.arenaCrossRewardList + seasonNow + Config.SERVER_ID;
			CrossRedis.getFullSet(redisKey, ret -> {
				if (ret.failed()) {
					return;
				}
				JsonArray jsonArray = ret.result();
				for(int i = 0; i < jsonArray.getList().size(); i++){
					String json = String.valueOf(jsonArray.getList().get(i));
					ArenaRankedBrief info = new ArenaRankedBrief(json);
					arenaRankedBriefList.add(info);
				}
				ttSec.reStart();
				Log.temp.info("===跨服奖励未下发成功，补发奖励， seasonNow={}， serverId={}, redisKey={}, size={}",
						seasonNow, Config.SERVER_ID, redisKey, arenaRankedBriefList.size());
			});
			// 删除上上季度数据
			CrossRedis.del(RedisKeys.arenaCrossRewardList + (seasonNow - 2) + Config.SERVER_ID);
		});
	}

	@DistrMethod
	public void sendLeagueSettleScore(Map<Long, LeagueVO> leagueVOMap){
		Log.temp.info("收到sendLeagueSettleScore ， key={}", leagueVOMap.size());
		for(LeagueVO vo : leagueVOMap.values()){
			long humanId = vo.voId;
			Log.temp.info("===发送humanId={}, voJSON={}", humanId, vo.toJSON());
			try {
				if (vo.intValue > 0) {
					MailManager.inst().sendMail(humanId, MailManager.SYS_SENDER, vo.intValue, "", "", vo.param.getString("itemJSON"), null);
				}
				if (vo.param.containsKey("addScore")) {
					int addScore = vo.param.getInt("addScore");
					// 排行榜加积分，对应的物品要加
					Param paramLine = new Param();
					paramLine.put("addScore", addScore);
					pockLineAdd(humanId, PocketLineEventSubKey.GUILD_LEAGUE_ADD_SCORE, paramLine.toJsonString());
				}
				if (vo.param.containsKey("killNum")) {
					int killNum = vo.param.getInt("killNum");
					// 家族乱斗击杀敌人
					HumanGlobalInfo info = datas.get(humanId);
					if(info != null){
						// 处理在线的玩家
						HumanObjectServiceProxy humanPrx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId,info.id);
						Log.battle.info("humanId={}在线，跨服家族乱斗累计战胜X名对手 killNum={}",humanId, killNum);
						humanPrx.gvgBattleKillEnemy(killNum);
					}
					else{
						// 处理不在线的玩家
						JSONObject jo = new JSONObject();
						jo.put("taskType", TaskConditionTypeKey.TASK_TYPE_110);
						jo.put("objs", new int[]{killNum});
						Log.battle.info("humanId={}不在线，跨服家族乱斗累计战胜X名对手 成就类型={} killNum={}", humanId, TaskConditionTypeKey.TASK_TYPE_110, killNum);
						Pocket.add(humanId, PocketLineEventSubKey.ACHIEVEMENT_PROGRESS, jo.toJSONString());
					}
				}
			}catch (Exception e){
				Log.temp.error("===发送联盟结算邮件失败，vo={}", vo.toJSON(), e);
			}
		}
		port.returns("result", true);
	}

	@DistrMethod
	public void sendSettleHumanReward(Map<Long, LeagueVO> leagueVOMap){
		Log.temp.info("===sendSettleHumanReward 收到 num={}", leagueVOMap.size());
		humanLeagueVoList.addAll(leagueVOMap.values());
		ttLeagueSec.reStart();
		port.returns("result", true);
	}

	private void sendLeagueReward(){
		if (humanLeagueVoList == null || humanLeagueVoList.isEmpty()) {
			ttLeagueSec.stop();
			return;
		}
		int num = Math.min(50, humanLeagueVoList.size());
		for(int i = 0; i < num; i++){
			if(humanLeagueVoList == null || humanLeagueVoList.isEmpty()){
				break;
			}
			LeagueVO info = humanLeagueVoList.poll();
			if(info == null){
				continue;
			}
			ConfFamiliybrawlRankReward conf = ConfFamiliybrawlRankReward.get(info.intValue);
			if(conf == null){
				Log.temp.error("===ConfFamiliybrawlRankReward错误，info={}", info.toJSON());
				continue;
			}
			String itemJSON = Utils.mapIntIntToJSON(Utils.intArrToIntMap(new HashMap<>(), conf.rank_reward));
            JSONObject jo = new JSONObject();
            JSONObject joTemp = new JSONObject();

            joTemp.put(MailManager.MAIL_K_4, Utils.intValue(info.longValue));
            jo.put(MailManager.MAIL_PARAM_1, joTemp);
			int mailSn = Utils.intValue(info.param.get("mailSn"));
            MailManager.inst().sendMail(info.voId, MailManager.SYS_SENDER, mailSn,"", jo.toJSONString(), itemJSON, null);
		}
		if(humanLeagueVoList.isEmpty()){
			ttLeagueSec.stop();
		}
	}

	@DistrMethod
	public void sendSettleGuildReward(Map<Long, LeagueVO> leagueVOMap){
		Log.temp.info("===sendSettleGuildReward 收到 num={}", leagueVOMap.size());
		GuildServiceProxy proxy = GuildServiceProxy.newInstance();
		proxy.sendSettleGuildReward(leagueVOMap);
		port.returns("result", true);
	}

	@DistrMethod
	public void getSyncHumanBrief(List<Long> humanIdList){
		EntityManager.batchGetEntity(HumanBrief.class, humanIdList, res->{
			if(res.failed()){
				HumanManager.inst().loadHumanIdSyncCross(humanIdList);
				return;
			}
			List<Long> idList = new ArrayList<>();
			List<HumanBrief> humanList = res.result();
			List<Request> reqList = new ArrayList<>();
			for(HumanBrief brief : humanList){
				if(brief != null){
					idList.add(brief.getId());
					List<Object> list = new ArrayList<>();
					list.add(HumanBrief.getRedisKeyStr(brief.getId()));
					list.addAll(brief.getKeyValueList());
					Request req = Request.cmd(Command.HSET, list.toArray());
					reqList.add(req);
				}
			}
			if(!reqList.isEmpty()){
				RedisTools.batch(CrossRedis.redis, reqList, h -> {
					if(h.failed()){
						Log.temp.error("==同步玩家数据出问题 {}", h.cause());
					}
				});
			}
			List<Long> allList = new ArrayList<>(humanIdList);
			allList.removeAll(idList);
			if(!humanList.isEmpty()){
				try {
					CrossManager.getInstance().callCrossFunc(CrossType.cross_chaos_battle, Config.SERVER_ID, res2 -> {
						try {
							if(res2.failed()){
								Log.temp.error("==跨服获取数据出问题 {}", res2.cause());
								return;
							}
							CrossPoint result = res2.result();
							GuildLeagueServiceProxy proxy = GuildLeagueServiceProxy.newInstance(result.getNodeId(), D.SERV_GUILD_LEAGUE);
							proxy.updateHumanBriefList(humanList);
						} catch (Exception e){
							Log.temp.error("==跨服链接出问题 ", e);
						}
					});
				} catch (Exception e){
					Log.temp.error("====跨服获取数据出问题", e);
				}
			}
			if(!allList.isEmpty()){
				HumanManager.inst().loadHumanIdSyncCross(allList);
			}
		});
	}

	@DistrMethod
	public void sendArenaCrossSettle(Map<Long, ArenaRankedBrief> map) {
		if (map == null || map.isEmpty()) {
			Log.temp.warn("===sendArenaCrossSettle: map为空或无数据");
			port.returns("result", false);
			return;
		}
		Log.temp.info("===跨服竞技场sendArenaCrossSettle, size={}", map.size());
		arenaCrossBriefList.addAll(map.values());
		ttArenaSec.reStart();
		Log.temp.info("===sendArenaCrossSettle, listSize={}", arenaCrossBriefList.size());
		port.returns("result", true);
	}

	private void sendArenaCrossBrief() {
		if (arenaCrossBriefList == null || arenaCrossBriefList.isEmpty()) {
			ttArenaSec.stop();
			return;
		}
		int num = Math.min(10, arenaCrossBriefList.size());
		for(int i = 0; i < num; i++){
			if(arenaCrossBriefList == null || arenaCrossBriefList.isEmpty()){
				break;
			}
			ArenaRankedBrief info = arenaCrossBriefList.poll();
			ArenaManager.inst().sendArenaCrossSettle(info);
		}
		if(arenaCrossBriefList.isEmpty()){
			ttArenaSec.stop();
		}
	}

	/**
	 * 跨服战拉机器人数据
	 */
	@DistrMethod
	public void getCrossWarRobotBrief(int serverId){
		List<int[]> robotRefreshInfo = CrossWarUtils.getRobotRefreshInfo();
		long pid = port.createReturnAsync();
		getHumanBriefByLvRange(robotRefreshInfo, serverId, res->{
			if(res.failed()){
				Log.crossWar.info("getHumanBriefByLvRange fail", res.cause());
				port.returnsAsync(pid, "briefList", null);
				return;
			}
			List<HumanBrief> briefList = res.result();
			Log.crossWar.info("getHumanBriefByLvRange success! briefList = {}", briefList.size());
			port.returnsAsync(pid, "briefList", briefList);
		});
	}

	/**
	 * 根据等级范围拉取humanBrief
	 */
	public void getHumanBriefByLvRange(List<int[]> levelRangeInfo, int serverId, Handler<AsyncResult<List<HumanBrief>>> onComplete){
		if(levelRangeInfo == null || levelRangeInfo.isEmpty()){
			AsyncActionResult.fail(port, onComplete, new Exception("levelRangeInfo is Empty"));
			return;
		}
		List<HumanBrief> totalBriefList = new ArrayList<>();
		AtomicInteger loadNum = new AtomicInteger(0);
		// 能拉到几个机器人就拉几个，报错的打日志，跳过
		for(int[] info : levelRangeInfo){
			int num = info[0];
			int minLv = info[1];
			int maxLv = info[2];
			loadNum.incrementAndGet();
			String redisKey = RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeLevel_1001, serverId);
			RedisTools.getRankListByScore(EntityManager.redisClient, redisKey, minLv, maxLv, false, (ret)->{
				if(!ret.succeeded()) {
					Log.temp.error("getCrossWarRobotBrief fail, redisKey={}", redisKey);
					loadNum.decrementAndGet();
					if(loadNum.get() <= 0){
						AsyncActionResult.success(port, onComplete, totalBriefList);
					}
					return;
				}
				JsonArray json = ret.result();
				List<Long> humanIdList = new ArrayList<>();
				int size = json.getList().size();
				Log.crossWar.info("getCrossWarRobotBrief redisKey={} minLv={} maxLv={} size={}", redisKey, minLv, maxLv, size);
				for(int i = 0; i < size; i++) {
					long id = Utils.longValue(json.getList().get(i));
					humanIdList.add(id);
				}
				if(humanIdList.isEmpty()){
					loadNum.decrementAndGet();
					if(loadNum.get() <= 0){
						AsyncActionResult.success(port, onComplete, totalBriefList);
					}
					return;
				}
				Collections.shuffle(humanIdList);
				humanIdList = humanIdList.subList(0, Math.min(num, humanIdList.size()));
				EntityManager.batchGetEntity(HumanBrief.class, humanIdList, res->{
					if(res.failed()){
						loadNum.decrementAndGet();
						if(loadNum.get() <= 0){
							AsyncActionResult.success(port, onComplete, totalBriefList);
						}
						return;
					}
					List<HumanBrief> humanBriefList = res.result();
					totalBriefList.addAll(humanBriefList);
					loadNum.decrementAndGet();
					if(loadNum.get() <= 0){
						AsyncActionResult.success(port, onComplete, totalBriefList);
					}
				});
			});
		}
	}


	@DistrMethod
	public void sendMsg_guild_schedule_s2c(List<Long> guildIdList, int state){
//		GuildServiceProxy proxy = GuildServiceProxy.newInstance();
//		proxy.sendMsg_guild_schedule_s2c(guildIdList, state);
	}

	@DistrMethod
	public void sendCrossWarReward(Map<Long, CrossWarRewardVO> crossWarRewardVOMap){
		Log.crossWar.info("===sendCrossWarReward 收到 num={} ids={}", crossWarRewardVOMap.size(), crossWarRewardVOMap.keySet());
		ConfGlobal conf = ConfGlobal.get(ConfGlobalKey.跨服战服务器开启区间.SN);
		int minServerId = conf.intArray[0] + Config.GAME_SERVER_PREFIX_VALUE;
		int maxServerId = conf.intArray[1] + Config.GAME_SERVER_PREFIX_VALUE;
		if(Config.SERVER_ID >= minServerId && Config.SERVER_ID <= maxServerId){
			crossWarRewardVoList.addAll(crossWarRewardVOMap.values());
			ttSendCrossSec.reStart();
		}
		port.returns("result", true);
	}
	private void sendCrossWarMail(){
		if (crossWarRewardVoList.isEmpty()) {
			ttSendCrossSec.stop();
			return;
		}
		long nowTime = Port.getTime();
		int num = Math.min(50, crossWarRewardVoList.size());
		for(int i = 0; i < num; i++){
			if(crossWarRewardVoList.isEmpty()){
				break;
			}
			CrossWarRewardVO info = crossWarRewardVoList.poll();
			if(info == null){
				continue;
			}
			String rankKey1028 = CrossWarUtils.getCrossWarRankTypeKey(CrossWarConst.rank_1028);
			String rankKey1042 = CrossWarUtils.getCrossWarRankTypeKey(CrossWarConst.rank_1042);
			String rankKey1031 = CrossWarUtils.getCrossWarRankTypeKey(CrossWarConst.rank_1031);
			String rankKey1032 = CrossWarUtils.getCrossWarRankTypeKey(CrossWarConst.rank_1032);
			try{
				if(info.param.containsKey(rankKey1028)){
					int confSn = info.param.get(CrossWarUtils.getCrossWarRankSnConfSnKey(CrossWarConst.rank_1028));
					int mailSn = info.param.get(CrossWarUtils.getCrossWarMailSnKey(CrossWarConst.rank_1028));
					int rank = info.param.get(CrossWarUtils.getCrossWarRankKey(CrossWarConst.rank_1028));
					sendCrossWarRewardRankMail(info.voId, confSn, mailSn, rank);
				}

				if(info.param.containsKey(rankKey1042)){
					int confSn = info.param.get(CrossWarUtils.getCrossWarRankSnConfSnKey(CrossWarConst.rank_1042));
					int mailSn = info.param.get(CrossWarUtils.getCrossWarMailSnKey(CrossWarConst.rank_1042));
					int rank = info.param.get(CrossWarUtils.getCrossWarRankKey(CrossWarConst.rank_1042));
					sendCrossWarRewardRankMail(info.voId, confSn, mailSn, rank);
				}

				if(info.param.containsKey(rankKey1031)){
					int confSn = info.param.get(CrossWarUtils.getCrossWarRankSnConfSnKey(CrossWarConst.rank_1031));
					int mailSn = info.param.get(CrossWarUtils.getCrossWarMailSnKey(CrossWarConst.rank_1031));
					int rank = info.param.get(CrossWarUtils.getCrossWarRankKey(CrossWarConst.rank_1031));
					sendCrossWarRewardRankMail(info.voId, confSn, mailSn, rank);
				}

				if(info.param.containsKey(rankKey1032)){
					long openServerTime = Util.getOpenServerTime(Config.SERVER_ID);
					int week = Utils.getDayOfWeek(openServerTime);//周几
					ConfActivityAdjust confAdjust = ConfActivityAdjust.get(33, week);
					// 改了confAdjust的持续时间为7天，现在取开始的偏移
					long timeEnd = Utils.getOffDayTime(openServerTime, confAdjust.day[0][0], 0);
					if(nowTime < timeEnd){
						continue;
					}

					int confSn = info.param.get(CrossWarUtils.getCrossWarRankSnConfSnKey(CrossWarConst.rank_1032));
					int mailSn = info.param.get(CrossWarUtils.getCrossWarMailSnKey(CrossWarConst.rank_1032));
					int rank = info.param.get(CrossWarUtils.getCrossWarRankKey(CrossWarConst.rank_1032));

					ConfCrossWarRankReward conf = ConfCrossWarRankReward.get(confSn);
					if(conf == null){
						Log.crossWar.error("===ConfCrossWarRankReward配置不存在，confSn={}", confSn);
						continue;
					}
					Map<Integer, Integer> itemMap = Utils.intArrToIntMap(new HashMap<>(), conf.reward);

					int[] itemSnArr = new int[itemMap.size()];
					int[] itemNumArr = new int[itemMap.size()];
					int index = 0;
					for(Map.Entry<Integer, Integer> entry : itemMap.entrySet()){
						itemSnArr[index] = entry.getKey();
						itemNumArr[index] = entry.getValue();
						index++;
					}
					int serverId = Utils.intValue(info.voId);
					MailManager.inst().createFillMail(serverId, mailSn, Port.getTime(), Port.getTime() + 3 * Time.DAY, rank, 0, 0, itemSnArr, itemNumArr);
				}
			}catch (Exception e){
				Log.temp.error("===sendCrossWarMail 出错，info={}", info, e);
			}

		}
    }

	private void sendCrossWarRewardRankMail(long humanId, int confSn, int mailSn,int rank){
		ConfCrossWarRankReward conf = ConfCrossWarRankReward.get(confSn);
		if(conf == null){
			Log.crossWar.error("===ConfCrossWarRankReward配置不存在，confSn={}", confSn);
			return;
		}
		String itemJSON = Utils.mapIntIntToJSON(Utils.intArrToIntMap(new HashMap<>(), conf.reward));

		JSONObject jo = new JSONObject();
		JSONObject joTemp = new JSONObject();
		joTemp.put(MailManager.MAIL_K_4, rank);
		jo.put(MailManager.MAIL_PARAM_1, joTemp);
		MailManager.inst().sendMail(humanId, MailManager.SYS_SENDER, mailSn, "", jo.toJSONString(), itemJSON, new Param());
	}

	/**
	 * 全服发送跑马灯
	 * @param param
	 */
	@DistrMethod
	public void sendSystemMarquee(Param param) {
		int cfgId = Utils.getParamValue(param, "cfgId", 0);
		Define.p_lang_info content = Utils.getParamValue(param, "content", null);
        int serverId = Utils.getParamValue(param, "serverId", 0);
		// 所有玩家
		List<HumanGlobalInfo> receiver = new ArrayList<>(datas.values());
		if (receiver.isEmpty()) {
			Log.inform.info("发送跑马灯，接收者为空, nodeId={}", Port.getCurrent().getNodeId());
			return;
		}
		MsgSystem.system_marquee_s2c.Builder msg = MsgSystem.system_marquee_s2c.newBuilder();
		msg.setTips(1);
		msg.setCfgId(cfgId);
		msg.setContent(content);
		for(HumanGlobalInfo r : receiver) {
			if(r.isCheckRobot || r.serverId != serverId) {
				continue;
			}
			Log.temp.info("sendSystemMarquee humanId={} name={} cfgId={} serverId={}", r.id, r.name, cfgId, serverId);
			HumanGlobalManager.inst().sendMsg(r.connPoint, msg.build());
		}
	}

	/**
	 * 武道会发放竞猜币
	 * @param humanId
	 * @param betNum
	 */
	@DistrMethod
	public void kungFuRaceAddBetCoin(long humanId, int betNum) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			Log.kungFuRace.info("===武道会竞猜成功，玩家离线，创建发放竞猜币代办 humanId={} betNum={}", humanId, betNum);
			JSONObject jo = new JSONObject();
			jo.put("betNum", betNum);
			Pocket.add(humanId, PocketLineEventSubKey.KUNG_FU_RACE_ADD_BET_COIN, jo.toJSONString());
			return;
		}
		Log.kungFuRace.info("===武道会竞猜成功，玩家在线，直接发放竞猜币 humanId={} betNum={}", humanId, betNum);
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.kungFuRaceAddBetCoin(betNum);
	}

	/**
	 * 武道会回收竞猜币
	 */
	@DistrMethod
	public void kungFuRaceRecycleBetCoin() {
		// 回收所有在线玩家的竞猜币，离线玩家在他们上线时处理
		Log.kungFuRace.info("===武道会回收所有在线玩家的竞猜币");
		for(HumanGlobalInfo info : datas.values()) {
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			proxy.kungFuRaceRecycleBetCoin();
		}
	}

	/**
	 * 武道会积分达到X分
	 * @param humanId
	 * @param newScore
	 */
	@DistrMethod
	public void kungFuRaceBattleNewScore(long humanId, int newScore) {
		HumanGlobalInfo info = datas.get(humanId);
		if (info == null) {
			Log.kungFuRace.info("===武道会humanId={}积分达到{}分，玩家离线，创建更新任务代办", humanId, newScore);
			JSONObject jo = new JSONObject();
			jo.put("season", KungFuRaceUtils.getCurrentSeason());
			jo.put("taskType", TaskConditionTypeKey.TASK_TYPE_99);
			jo.put("objs", new int[]{newScore});
			Pocket.add(humanId, PocketLineEventSubKey.KUNG_FU_RACE_UPDATE_TASK, jo.toJSONString());
			return;
		}
		Log.kungFuRace.info("===武道会humanId={}积分达到{}分，玩家在线，直接更新任务进度", humanId, newScore);
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.kungFuRaceBattleNewScore(newScore);
	}

	@DistrMethod
	public void sendKungFuRaceReward(Map<Long, KungFuRaceRewardVO> rewardVOMap){
		Log.kungFuRace.info("===sendKungFuRaceReward 收到 num={} ids={}", rewardVOMap.size(), rewardVOMap.keySet());
		m_kungFuRaceRewardVoList.addAll(rewardVOMap.values());
		m_ttSendKungFuRaceSec.reStart();
		port.returns("result", true);
	}

	private void sendKungFuRaceMail(){
		if (m_kungFuRaceRewardVoList.isEmpty()) {
			m_ttSendKungFuRaceSec.stop();
			return;
		}
		// 1. 获取并移除前N个元素
		List<KungFuRaceRewardVO> firstNVo = new LinkedList<>();
		for (int i = 0; i < 50; i++) {
			if(m_kungFuRaceRewardVoList.isEmpty()) {
				break;
			}
			firstNVo.add(m_kungFuRaceRewardVoList.removeFirst()); // 移除并获取第一个元素
		}
		// 2. 对前N个元素进行后续处理
		for (KungFuRaceRewardVO vo : firstNVo) {
			try{
				sendKungFuRaceRewardRankMail(vo.humanId, vo.confSn);
			}catch (Exception e){
				Log.temp.error("===sendKungFuRaceRewardRankMail 出错，vo={}", vo, e);
			}
		}
	}

	private void sendKungFuRaceRewardRankMail(long humanId, int confSn){
		ConfBattleCompetitionReward conf = ConfBattleCompetitionReward.get(confSn);
		if(conf == null){
			Log.kungFuRace.error("===ConfBattleCompetitionReward 配置不存在，confSn={}", confSn);
			return;
		}
		String itemJSON = Utils.mapIntIntToJSON(Utils.intArrToIntMap(new HashMap<>(), conf.reward));

		String content = "";
		JSONObject jo = new JSONObject();
		if(conf.rank > 0){
			JSONObject joTemp = new JSONObject();
			joTemp.put(MailManager.MAIL_K_4, conf.rank);
			jo.put(MailManager.MAIL_PARAM_1, joTemp);
			content = jo.toJSONString();
		}
		int mailSn = conf.mail_id;
		MailManager.inst().sendMail(humanId, MailManager.SYS_SENDER, mailSn, "", content, itemJSON, new Param());
	}

	@DistrMethod
	public void sendActivityCrossHumanRankSettle(List<ActivityCrossRankSettleInfo> infoList, int actSn, int round, int rankSn) {
		ConfActivityControl confAct = ConfActivityControl.get(actSn);
		ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(confAct.type, round);
		List<Integer> confRewardList = GlobalConfVal.getConfActivityRankRewardSnList(confAct.type, confTerm.group_id);
		if (confRewardList == null || confRewardList.isEmpty()) {
			Log.activity.error("跨服活动排行榜结算有问题，找不到对应的奖励表格。actSn={}, round={}", actSn, round);
			return;
		}
		ConfRanktype confRanktype = ConfRanktype.get(rankSn);
		for (ActivityCrossRankSettleInfo info : infoList) {
			long id = info.id;
			long rank = info.rank;
			int sn = -1;
			for (Integer confSn : confRewardList) {
				ConfActivityRankReward confReward = ConfActivityRankReward.get(confSn);
				if (rank >= confReward.rank_range[0] && rank <= confReward.rank_range[1]) {
					sn = confSn;
					break;
				}
			}
			if (sn == -1) {
				Log.activity.error("玩家结算跨服活动排行榜时, 没有找到对应的奖励, id={}, actSn={}, round={}, rank={}", id, actSn, round, rank);
				return;
			}
			ConfActivityRankReward confRankReward = ConfActivityRankReward.get(sn);
			RankManager.inst().distributeReward(String.valueOf(id), (int) rank, actSn, confRanktype, confRankReward);
		}
	}

	/**
	 * 给玩家发团购礼包奖励
	 */
	@DistrMethod
	public void sendGroupGiftReward(List<Long> humanIdList, int groupSn) {
		ConfMergeGroupGift conf = ConfMergeGroupGift.get(groupSn);
		if (conf == null) {
			Log.activity.error("===ConfMergeGroupGift 配置不存在，groupSn={}", groupSn);
			return;
		}
		for (Long humanId : humanIdList) {
			HumanGlobalInfo info = datas.get(humanId);
			if (info == null) {
				// 玩家不在线，走代办
				Param param = new Param();
				param.put("sn", conf.paymall_id);
				PocketLineManager.inst().addPocketLine(humanId, PocketLineEventSubKey.PAY_FAKE, param.toJsonString());
			} else {
				// 玩家在线，直接发充值到账事件
				HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
				prx.eventFire(EventKey.PAY_NOTIFY, new Param("sn", conf.paymall_id, "isFake", true));
			}
		}
	}

	// region 黑市相关
	/**
	 * 黑市预购处理，isReward决定是发预购奖励还是退回预购消耗
	 */
	@DistrMethod
	public void sendBlackMarketPreReward(Map<Long, Integer> humanMap, int sn, int goodsIndex, boolean isReward) {
		ConfBlackMarket conf = ConfBlackMarket.get(sn);
		if (conf == null) {
			Log.activity.error("[黑市]===ConfBlackMarket 配置不存在，sn={}", sn);
			return;
		}
		int mallSn = conf.goods[goodsIndex][0];
		ConfMall confMall = ConfMall.get(mallSn);
		if (confMall == null) {
			Log.activity.error("[黑市]===ConfMall 配置不存在，mallSn={}", mallSn);
			return;
		}
		for (Entry<Long, Integer> entry : humanMap.entrySet()) {
			long humanId = entry.getKey();
			int num = entry.getValue();
			Map<Integer, Integer> itemMap = new HashMap<>();
			for (int i = 0; i < num; i++) {
				if (isReward)
					itemMap.put(confMall.goods[0], itemMap.getOrDefault(confMall.goods[0], 0) + confMall.goods[1]);
				else
					itemMap.put(confMall.price[0], itemMap.getOrDefault(confMall.price[0], 0) + confMall.price[1]);
			}
			int mailSn = 90004;
			String content = "";
			if (isReward) {
				mailSn = 90003;
				JSONObject joTemp = new JSONObject();
				joTemp.put(MailManager.MAIL_K_0, num);
				JSONObject jo = new JSONObject();
				jo.put(MailManager.MAIL_PARAM_1, joTemp);
				content = jo.toJSONString();
			}
			MailManager.inst().sendMail(humanId, MailManager.SYS_SENDER, mailSn, "", content, Utils.mapIntIntToJSON(itemMap), null);
		}
	}
	// endregion 黑市相关

    @DistrMethod
    public void update1(String json) {

    }

    @DistrMethod
    public void update2(Object... objs) {

    }

    @DistrMethod
    public void update3(Param param) {

    }

    @DistrMethod
    public void update4(String json) {

    }
}