package org.gof.demo.worldsrv.human;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pwrd.op.LogOp;
import com.pwrd.op.LogOpChannel;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.redis.client.Command;
import io.vertx.redis.client.Request;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.gof.core.*;
import org.gof.core.connsrv.ConnectionProxy;
import org.gof.core.db.DBKey;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.CrossRedis;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.*;
import org.gof.core.support.S;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.observer.Listener;
import org.gof.core.utils.StrUtil;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.manager.UnitManager;
import org.gof.demo.battlesrv.msgHandler.SkillData;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.stageObj.UnitDataPersistance;
import org.gof.demo.battlesrv.stageObj.UnitObject;
import org.gof.demo.battlesrv.stageObj.UnitPropPlusMap;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.distr.cross.CrossManager;
import org.gof.demo.distr.cross.domain.CrossPoint;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.distr.world.srvmerge.ServerMerge;
import org.gof.demo.seam.account.AccountManager;
import org.gof.demo.seam.account.AccountServiceProxy;
import org.gof.demo.support.statistics.StatisticsHuman;
import org.gof.demo.worldsrv.activity.ActivityControlType;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.angel.AngelManager;
import org.gof.demo.worldsrv.appearance.Personality;
import org.gof.demo.worldsrv.appearance.SkinVo;
import org.gof.demo.worldsrv.arena.ArenaCrossServiceProxy;
import org.gof.demo.worldsrv.arena.ArenaManager;
import org.gof.demo.worldsrv.arena.ArenaRankedServiceProxy;
import org.gof.demo.worldsrv.artifact.ArtifactManager;
import org.gof.demo.worldsrv.carPark.CarParkManager;
import org.gof.demo.worldsrv.character.*;
import org.gof.demo.worldsrv.charm.CharmData;
import org.gof.demo.worldsrv.charm.CharmManager;
import org.gof.demo.worldsrv.charm.CharmUtils;
import org.gof.demo.worldsrv.common.GameSelectServiceProxy;
import org.gof.demo.worldsrv.common.GameServiceProxy;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.doubleChapter.DoubleChapterManager;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.entity.Currency;
import org.gof.demo.worldsrv.equip.EquipInfo;
import org.gof.demo.worldsrv.equip.EquipManager;
import org.gof.demo.worldsrv.fate.FateManager;
import org.gof.demo.worldsrv.flyPet.FlyPetInfo;
import org.gof.demo.worldsrv.flyPet.FlyPetManager;
import org.gof.demo.worldsrv.friend.FriendManager;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.GuildManager;
import org.gof.demo.worldsrv.guild.GuildServiceProxy;
import org.gof.demo.worldsrv.guild.HumanBriefVO;
import org.gof.demo.worldsrv.guild.league.GuildLeagueServiceProxy;
import org.gof.demo.worldsrv.home.BuildVo;
import org.gof.demo.worldsrv.home.Fish.HomeFishManager;
import org.gof.demo.worldsrv.home.HomeManager;
import org.gof.demo.worldsrv.httpPush.HttpPushServiceProxy;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.inform.InformManager;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.kungFuRace.KungFuRaceManager;
import org.gof.demo.worldsrv.mount.MountManager;
import org.gof.demo.worldsrv.placingReward.PlacingRewardVo;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.jobs.JobsManager;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.mall.MallManager;
import org.gof.demo.worldsrv.msg.*;
import org.gof.demo.worldsrv.name.NameManager;
import org.gof.demo.worldsrv.name.NameServiceProxy;
import org.gof.demo.worldsrv.pet.PetData;
import org.gof.demo.worldsrv.pet.PetManager;
import org.gof.demo.worldsrv.pmSync.PMSyncDataManager;
import org.gof.demo.worldsrv.pmSync.ReadonlyFormatHuman;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.pocketLine.PocketLineManager;
import org.gof.demo.worldsrv.privilege.PrivilegeManager;
import org.gof.demo.worldsrv.privilege.PrivilegeType;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.relic.RelicManager;
import org.gof.demo.worldsrv.stage.*;
import org.gof.demo.worldsrv.support.*;
import org.gof.demo.worldsrv.support.enumKey.*;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventBridgeKey;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.task.TaskManager;
import org.gof.demo.worldsrv.task.type.activityData.ActivityTaskVO;
import org.gof.demo.worldsrv.task.type.maindata.MainTaskVO;
import org.gof.demo.worldsrv.team.TeamMember;
import org.gof.demo.worldsrv.wing.WingManager;
import org.gof.platform.ConstPf;
import org.gof.platform.http.HttpAsyncSendServiceProxy;
import org.gof.platform.http.HttpServerHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.*;

import static org.gof.demo.worldsrv.equip.EquipInfo.face;
import static org.gof.demo.worldsrv.equip.EquipInfo.weapon;

public class HumanManager extends ManagerBase {
    public final static Logger logger = LoggerFactory.getLogger(HumanManager.class);

    // 用于战斗（大量数据）
    public static final Class[] humanClasses = new Class[]{Human.class, Human2.class, Profession.class, UnitPropPlus.class, Equip.class, Artifact.class, Mount.class, Relic.class, Charm.class};

    // 用于外观（少量）// TODO 估计要加
    public static final Class[] humanClasses2 = new Class[]{Human.class, Human2.class, Charm.class};
    // 玩家初始主地图
    public static int stageInitSn = 1000; //1createUserSendGMT

    public static int stageInitRepSn = 20000; //1 新手引导的地图sn
    public static final int ONLINE_CHANGE_INTERVAL_SEC = 10; // 在线时间心跳间隔

    public static final int IllustratedType1 = 1; //1 图鉴类型1，技能
    public static final int IllustratedType2 = 2; //2 图鉴类型2，同伴

    public static final int MAX_PLAN_NAME_LEN = 8; //方案名字最大长度
    public static final int MAX_DEFAULT_PLAN = 5; //默认翻案最大数量

    public static final int PLAN_TYPE_ARENA_PVP = 1; //竞技场和排位赛
    public static final int PLAN_TYPE_GUILD_BOSS = 2; //公会boss
    public static final int PLAN_TYPE_HOME_BATTLE = 3; //家园战斗


    public static final int SPEED_UP_BOX = 1;//加速宝箱升级
    public static final int SPEED_UP_SCIENCE = 2;//加速科技
    public static final int SPEED_UP_FARM = 3;//加速农场建筑

    // 这边指的是升级完成，但跟加速是对应
    public static final int Upgrade_Finish_Box = SPEED_UP_BOX;
    public static final int Upgrade_Finish_Science = SPEED_UP_SCIENCE;
    public static final int Upgrade_Finish_Farm = SPEED_UP_FARM;


    public static final int DEFAULT_SKIN_2 = 9;//默认拥有时装

    public static final int wing = 5;//翅膀

    public static final int MIN_SET_TYPE = 10001; //设置最小类型
    public static final int MAX_SET_TYPE = 10050; //设置最大类型


    /**
     * 获取实例
     *
     * @return
     */
    public static HumanManager inst() {
        return inst(HumanManager.class);
    }


    /**
     * 战斗力提升
     *
     * @param param
     */
    @Listener(EventKey.HUMAN_COMBAT_CHANGE)
    public void _listener_HUMAN_COMBAT_CHANGE(Param param) {
        HumanObject humanObj = param.get("humanObj");
//        syncHumanGlobal(humanObj);
    }


    /**
     * 创建玩家
     *
     * <AUTHOR>
     * @Date 2024/2/26
     * @Param
     */
    public Human create(long showId, int serverId, String account, String channel, String name,
                        int zone, boolean test, boolean isCheck, Param param) {

        long id = Port.applyId();

        // 初始化玩家信息
        Human human = new Human();
        human.setId(id);
        human.setShowId(showId);
        human.setServerId(serverId);
        human.setZone(zone);
        human.setAccount(account);
        human.setChannel(channel);
        human.setName(name);
        human.setLevel(1);
        human.setTimeCreate(Port.getTime());
        human.setTimeLogin(0L);
        human.setTimeLogout(0L);
        human.setCheckRobot(isCheck);
        //初始化经验
        human.setExpCur(0);
        // 职业属性
        int jobSn = ConfGlobal.get(ConfGlobalKey.默认职业.SN).value;
        human.setJobModel(jobSn);

        //首次初始化技能
//		initHumanSkillGroup(human, skillGroupSn);
        // 持久化
        human.persist();

        Account accountData = new Account();
        accountData.setId(id);
        accountData.setAccount(account);
        accountData.setServerId(serverId);
        accountData.persist();


        /* 玩家属性加成 */
        UnitPropPlus pp = new UnitPropPlus();
        pp.setId(human.getId());

//        ConfJobs confJobs = ConfJobs.get(jobSn);
//        ConfUnit confUnit = ConfUnit.get(confJobs.model);
//        pp.setBase(GlobalConfVal.getPropCalc(confUnit).toJSONStr());

        //计算默认属性
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.initial_attr.SN);
        PropCalc initProp = new PropCalc();
        String[] strArr = Utils.splitStr(confGlobal.strValue, "\\|");
        for(int i = 0; i < strArr.length; i++){
            int[] intArr = Utils.strToIntArray(strArr[i], "\\,");
            initProp.plus(String.valueOf(intArr[0]), intArr[1]);
        }
        pp.setInitAttr(initProp.toJSONStr());

        pp.persist();

//        human.setModInit(Utils.setBitValueLong(human.getModInit(), EModuleTableInit.HumanBrief.getType(), 1));

        //添加注册日志
        LogOp.log(LogOpChannel.REGISTER,
                human.getId(),
                human.getName(),
                Port.getTime(),
                "0.0.0.0",
                Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
                0,//currency.getGold(),
                account,
                channel,
                serverId,
                human.getModelSn(),
                0,
                jobSn
        );

        // TODO 新创角的表要放这里传递出去
        Map<String, Object> tableMap = new HashMap<>();
        tableMap.put(Human.tableName, human);
        tableMap.put(Account.tableName, accountData);
        tableMap.put(UnitPropPlus.tableName, pp);

        param.put("tableMap", tableMap);

        Event.fire(EventKey.HUMAN_CREATE, "human", human);

        return human;
    }

    /**
     * 玩家登录游戏后 加载玩家的数据
     */
    public void loadData(HumanObject humanObj) {
        if(S.isRedis){
            loadRedisHumanObj(humanObj);
            return;
        }
        // 先将human主数据加载好
//        DB db = DB.newInstance(Human.tableName);
//        db.get(humanObj.id);
//        db.listenResult(this::_result_loadHumanDataMain, "humanObj", humanObj);
    }

    public void _result_loadHumanDataMain(Param results, Param context) {
        // 玩家
        HumanObject humanObj = context.get("humanObj");
        UnitDataPersistance data = humanObj.dataPers;

        // 处理返回数据
        data.unit = new Human((Record) results.get());

        // 发布事件 让其他各个模块开始加载数据
        Event.fire(EventKey.HUMAN_DATA_LOAD_BEGIN, humanObj);
    }


    public void loadRedisHumanObj(HumanObject humanObj){
        Log.game.info("loginTrace[{}] login_step_1600 account={}, humanId={}",humanObj.connPoint.servId, humanObj.account, humanObj.id);
        // 获取当前请求编号
        humanObj.loadingPID = Port.getCurrent().createReturnAsync();
        int queryTableNum = 41;
        humanObj.loginLoadPart.set(queryTableNum);

        long humanId = humanObj.id;
        EntityManager.getEntityAsync(Human.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Human.class,res.cause());
                return;
            }
            humanObj.dataPers.unit = (Human)res.result();
            if(humanObj.dataPers.unit == null){
                Log.human.error("===加载玩家数据失败 Human=null accountu={}, humanId={}", humanObj.account, humanObj.id);
                return;
            }
            humanObj.serverOpenTime = Util.getOpenServerTime(humanObj.getHuman().getServerId());
            humanObj.loginLoadPart.decrementAndGet();
            // 飞宠单位
            if (isModInit(humanObj, EModuleTableInit.FlyPet)) {
                EntityManager.getEntityListAsync(FlyPet.class, humanId, (res1) -> {
                    if (res.failed()) {
                        onLoadDataError(humanObj, FlyPet.class, res1.cause());
                        return;
                    }
                    List<FlyPet> result = res1.result();
                    if (result != null) {
                        humanObj.operation.initFlyPet(result);
                    }
                    humanObj.loginLoadPart.decrementAndGet();
                    checkLoadHumanDataIsFinished(humanObj);
                });
            } else {
                humanObj.loginLoadPart.decrementAndGet();
            }
            // 飞宠数据
            if (isModInit(humanObj, EModuleTableInit.FlyPetHumanData)) {
                EntityManager.getEntityAsync(FlyPetHumanData.class, humanId, (res1) -> {
                    if (res.failed()) {
                        onLoadDataError(humanObj, FlyPet.class, res1.cause());
                        return;
                    }
                    FlyPetHumanData entity = res1.result();
                    if (entity != null) {
                        humanObj.operation.initFlyPetHumanData(entity);
                    }
                    humanObj.loginLoadPart.decrementAndGet();
                    checkLoadHumanDataIsFinished(humanObj);
                });
            } else {
                humanObj.loginLoadPart.decrementAndGet();
            }
            checkLoadHumanDataIsFinished(humanObj);
        });


        String redisKey = HumanManager.getFillMailReceivedKey(humanObj.id);
        RedisTools.get(EntityManager.redisClient,redisKey,res->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, FillMail.class,res.cause());
                return;
            }
            String fillMailIdJSON = res.result();
            List<Long> fillMailIdList = Utils.strToLongList(fillMailIdJSON);
            humanObj.operation.fillMailIdList.addAll(fillMailIdList);
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });


        EntityManager.getEntityAsync(HumanExtInfo.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, HumanExtInfo.class,res.cause());
                return;
            }
            HumanExtInfo extInfo = (HumanExtInfo) res.result();
            humanObj.dataPers.extInfo = extInfo;
            if(extInfo != null){
                humanObj.idGen = extInfo.getIdGen();
                humanObj.operation.planVoMap = PlanVo.stringToMap(extInfo.getPlanMap());
                String placingStr = extInfo.getPlacingRewardMap();
                if (placingStr.startsWith(org.gof.demo.worldsrv.placingReward.PlacingRewardType.NEW_COMPRESSION_PREFIX)) {
                    placingStr = Utils.decompressJson(placingStr.substring(org.gof.demo.worldsrv.placingReward.PlacingRewardType.NEW_COMPRESSION_PREFIX.length()));
                }else {
                    placingStr = StringZipUtils.unzip(placingStr);
                }
                humanObj.operation.placingRewardMap = PlacingRewardVo.fromJsonStrMap(placingStr);
                humanObj.operation.equipBoxMap = EquipInfo.fromMapJsonString(extInfo.getEquipInfo());
                JSONArray equipBookList = Utils.toJSONArray(StringZipUtils.unzip(extInfo.getEquipBookList()));
                for (int i = 0; i < equipBookList.size(); i++) {
                    int existingSn = equipBookList.getIntValue(i);
                    ConfEquipment conf = ConfEquipment.get(existingSn);
                    if (conf != null) {
                        humanObj.operation.appearanceSet.add(conf.appearanceFilter);
                    }
                }
                humanObj.operation.equipFilterMap = Utils.jsonToMapIntInt(extInfo.getEquipFilterMap());
                humanObj.operation.mainRepKillIdMap = Utils.jsonToMapIntListLong(extInfo.getMainRepMonsterIdListMap());
                humanObj.initTreasureSkinMap();
            }
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(HumanSubjoinInfo.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, HumanSubjoinInfo.class,res.cause());
                return;
            }
            humanObj.dataPers.subjoinInfo = (HumanSubjoinInfo) res.result();
            humanObj.humanReset = new HumanReset(humanObj.dataPers.subjoinInfo);
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(Currency.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Currency.class,res.cause());
                return;
            }
            humanObj.currency = (Currency)res.result();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(UnitPropPlus.class, humanId, (res)-> {
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, UnitPropPlus.class,res.cause());
                return;
            }
            humanObj.dataPers.unitPropPlus.init((UnitPropPlus)res.result());
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });


        EntityManager.getEntityAsync(TaskInfo.class, humanId, (res)-> {
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, TaskInfo.class,res.cause());
                return;
            }
            humanObj.operation.taskData.load((TaskInfo)res.result());
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(TaskFinish.class, humanId, (res)-> {
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, TaskFinish.class,res.cause());
                return;
            }
            humanObj.operation.taskData.load((TaskFinish)res.result());
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });


        EntityManager.getEntityAsync(Mall.class, humanId, (res)-> {
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Mall.class,res.cause());
                return;
            }
            humanObj.operation.mall = (Mall)res.result();
            humanObj.operation.initPayCustomGift();
            humanObj.operation.initGearRangeMap();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityListAsync(Buff.class, humanId, (res)-> {
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Buff.class,res.cause());
                return;
            }
            List<Buff> buffList = res.result();
            for(Buff buff : buffList){
                humanObj.dataPers.buffs.put(buff.getSn(), buff);
            }
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityAsync(Item.class, humanId, (res)-> {
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Item.class,res.cause());
                return;
            }
            Item item = (Item)res.result();
            if(item == null){
                Log.human.error("===加载玩家数据失败 Item=null accountu={}, humanId={}", humanObj.account, humanObj.id);
            } else {
                humanObj.operation.itemData.init(item);
            }
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityAsync(HumanDailyResetTime.class, humanId, (res)-> {
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, HumanDailyResetTime.class,res.cause());
                return;
            }
            humanObj.setDailyResetRecord((HumanDailyResetTime)res.result());
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityListAsync(PayLog.class, humanId, (res)-> {
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, PayLog.class,res.cause());
                return;
            }
            List<PayLog> payLogList = res.result();
            for(PayLog payLog : payLogList){
                humanObj.dataPers.payLogs.add(payLog);
            }
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityListAsync(Mail.class, humanId, (res)-> {
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Mail.class,res.cause());
                return;
            }
            List<Mail> mailList = res.result();
            for(Mail mail : mailList){
                humanObj.operation.mailMap.put(mail.getId(), mail);
            }
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityListAsync(PocketLine.class, humanId, (res)-> {
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, PocketLine.class,res.cause());
                return;
            }
            List<PocketLine> lineList = res.result();
            for(PocketLine pocketLine : lineList){
                humanObj.dataPers.pocketLine.add(pocketLine);
            }
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityAsync(Profession.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Profession.class,res.cause());
                return;
            }
            humanObj.operation.profession = (Profession)res.result();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityAsync(ClientInfo.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, ClientInfo.class,res.cause());
                return;
            }
            humanObj.operation.clientInfo = (ClientInfo)res.result();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityListAsync(PayGift.class, humanId, (res)->{
            if(res.failed()){
                onLoadDataError(humanObj, PayGift.class,res.cause());
                return;
            }
            List<PayGift> payGiftList = res.result();
            if(payGiftList!=null) {
                for (PayGift payGift : payGiftList) {
                    humanObj.payGiftMap.put(payGift.getPayId(), payGift);
                }
            }
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityAsync(Privilege.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Privilege.class,res.cause());
                return;
            }
            humanObj.operation.privilege = (Privilege)res.result();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityListAsync(Equip.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Equip.class,res.cause());
                return;
            }
            List<Equip> equipList =res.result();
            for(Equip equip : equipList){
                humanObj.operation.equipsMap.put(equip.getTab(), equip);
            }
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityAsync(Friend.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Friend.class,res.cause());
                return;
            }
            humanObj.operation.friend = (Friend)res.result();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(Capture.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Capture.class,res.cause());
                return;
            }
            humanObj.operation.capture = (Capture)res.result();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(Artifact.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Artifact.class,res.cause());
                return;
            }
            humanObj.artifact.atf = (Artifact)res.result();
            ArtifactManager.inst().init(humanObj);
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });


        EntityManager.getEntityAsync(Mount.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Mount.class,res.cause());
                return;
            }
            humanObj.operation.mount = (Mount)res.result();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityAsync(Wing.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Wing.class,res.cause());
                return;
            }
            humanObj.operation.wing = (Wing)res.result();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(Relic.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Relic.class,res.cause());
                return;
            }
            humanObj.relic.relic = (Relic)res.result();
            RelicManager.inst().init(humanObj);
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(Mine.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Mine.class,res.cause());
                return;
            }
            humanObj.operation.mine = (Mine)res.result();

            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(Farm.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Farm.class,res.cause());
                return;
            }
            humanObj.farmData.farm = (Farm)res.result();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(Farm2.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Farm2.class,res.cause());
                return;
            }
            humanObj.farmData.farm2 = (Farm2)res.result();
            HomeManager.inst().initFarm(humanObj);
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityAsync(Fate.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Fate.class,res.cause());
                return;
            }
            humanObj.fate.fate = (Fate)res.result();
            FateManager.inst().init(humanObj);
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityAsync(Human2.class, humanId, res -> {
            if (res.failed()) {
                //加载失败，中断登陆
                HumanManager.this.onLoadDataError(humanObj, Human2.class, res.cause());
                return;
            }
            humanObj.operation.human2 = (Human2) res.result();
            humanObj.loginLoadPart.decrementAndGet();
            HumanManager.this.checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityAsync(Human3.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Human3.class,res.cause());
                return;
            }
            Human3 human3 = res.result();
            humanObj.operation.human3 = human3;
            if(human3 != null){
                humanObj.operation.emojiSnTimeMap = Utils.jsonToMapIntLong(human3.getEmojiSnExpiredMap());
                humanObj.operation.emojiSnList = new ArrayList<>(new HashSet<>(Utils.strToIntList(human3.getEmojiSnList())));
                humanObj.operation.initRepDiffcultyMap();
            }
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityListAsync(ActControlData.class, humanId, (res)->{
            if(res.failed()){
                onLoadDataError(humanObj, ActControlData.class,res.cause());
                return;
            }
            List<ActControlData> result = res.result();
            if(result !=null) {
                humanObj.operation.activityControlData.init(result, humanObj);
            }
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });

        EntityManager.getEntityAsync(CarPark2.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, CarPark2.class,res.cause());
                return;
            }
            humanObj.operation.carPark2 = (CarPark2)res.result();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(CrossWarInfo.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, CrossWarInfo.class,res.cause());
                return;
            }
            humanObj.operation.crossWarInfo = (CrossWarInfo)res.result();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(DoubleChapter.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, DoubleChapter.class,res.cause());
                return;
            }
            humanObj.operation.doubleChapter = res.result();
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(Angel.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, DoubleChapter.class,res.cause());
                return;
            }
            AngelManager.inst().initAngelData(humanObj, res.result());
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(Charm.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, Charm.class, res.cause());
                return;
            }
            CharmManager.initCharmData(humanObj, res.result());
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
        EntityManager.getEntityAsync(HomeFish.class, humanId, (res)->{
            if(res.failed()){
                //加载失败，中断登陆
                onLoadDataError(humanObj, HomeFish.class,res.cause());
                return;
            }
            HomeFishManager.inst().init(humanObj, res.result());
            humanObj.loginLoadPart.decrementAndGet();
            checkLoadHumanDataIsFinished(humanObj);
        });
    }

    public void createLoginHumanObj(HumanObject humanObj, Param param){
        try{
            boolean isLoginReturn = true;
            // 获取当前请求编号
            humanObj.loadingPID = Port.getCurrent().createReturnAsync();
            Map<String, Object> map = param.get("tableMap");
            for(Map.Entry<String, Object> entry : map.entrySet()){
                String tableName = entry.getKey();
                switch (tableName){
                    case Human.tableName:
                        humanObj.dataPers.unit = (Human)entry.getValue();
                        break;
                    case Account.tableName:
                        break;
                    case UnitPropPlus.tableName:
                        humanObj.dataPers.unitPropPlus.init((UnitPropPlus)entry.getValue());
                        break;
                    default:
                        isLoginReturn = false;
                        Log.temp.error("====创角新增表未实现代码。tableName={}, humanId={}, account={}", tableName, humanObj.id, humanObj.account);
                        break;
                }
            }
        if(!isLoginReturn){
            Log.human.error("====登录失败。humanId={}, account={}，创角直接登录失败， 原因：新增表未实现代码。",  humanObj.id, humanObj.account);
            return;
            }
            checkLoadHumanDataIsFinished(humanObj);
        } catch (Exception e){
            Log.human.error("===createLoginHumanObj", e);
        }
    }

    private void checkLoadHumanDataIsFinished(HumanObject humanObj){
        if(humanObj.loginLoadPart.get()>0){
            return;
        }
        Log.game.info("loginTrace[{}] login_step_1601 account={}, humanId={}",humanObj.connPoint.servId, humanObj.account, humanObj.id);
        HomeManager.inst().initScience(humanObj);
        // 检测后续功能的数据初始化
        checkHumanTable(humanObj);
        // 检查初始化的数据，如果不存在则增加（用于版本升级）
        checkAndInitData(humanObj);
        initLoad(humanObj);
        // 玩家数据加载完毕后 登录游戏
        login(humanObj);
        Port port = Port.getCurrent();
        port.returnsAsync(humanObj.loadingPID, "node", port.getNode().getId(), "port", port.getId());
        humanObj.loadingPID=0;
        //从登录队列中移除
        AccountServiceProxy accountServProxy = AccountManager.createAccountServProxy();
        accountServProxy.finishLogin(humanObj.account);
    }

    private void onLoadDataError(HumanObject humanObj, Class<?> cls, Throwable cause){
        Log.game.error("loginTrace[{}] login_step_1602 login load human data err, account={}, humanId={}, dataModel={}"
                ,humanObj.connPoint.servId, humanObj.account, humanObj.id, cls.getSimpleName(),cause);
        if(humanObj.loadingPID==0){
            return;
        }
        Port port = Port.getCurrent();
        port.returnsAsync(humanObj.loadingPID, "node", port.getNode().getId(), "port", port.getId(),"loadDataErr",cls.getSimpleName());
        humanObj.loadingPID=0;
    }



    private void firstLogin(HumanObject humanObj) {
        StopWatch watch = new StopWatch();
        watch.start();
        Human human = humanObj.getHuman();
        Human3 human3 = humanObj.getHuman3();
        Log.game.debug("首次登录到游戏 id={} , name={}", humanObj.id, humanObj.getHuman().getName());
        if (human.getHpCur() <= 0) {
            human.setHpCur(human.getHpMax());
            human.update();
        }
        humanObj.isCreateLogin = 1;
//		QuestManager.inst().initBeginerQuest(humanObj);

        if(!human3.isIsCreateReward()){
            human3.setIsCreateReward(true);
            human3.update();
            ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.创角首次登录奖励.SN);
            ProduceManager.inst().produceAdd(humanObj, Utils.parseIntArray2(confGlobal.strValue), MoneyItemLogKey.创角首次登录奖励);
        }


//		CalendarManager.inst().onHumanFirstLogin(humanObj);
        PocketLineManager.inst().loadPocketList(humanObj, false);


        Event.fire(EventKey.HUMAN_ALL_LIFE_FIRST_LOGIN, "humanObj", humanObj);

        if (!humanObj.isMirror) {
			PMSyncDataManager.inst().humanInfoChange(humanObj);
        }

        watch.stop();

    }


    /**
     * 玩家数据加载完毕后 登录游戏
     *
     * @param humanObj
     */
    private void login(HumanObject humanObj) {

        Human human = humanObj.getHuman();
        // 上次最后登录时间
        long timeLast = human.getTimeLogin();
        if (timeLast == 0) {
            firstLogin(humanObj);
            //冒险任务
            humanObj.operation.taskData.updatePorpCalc(humanObj);
        }

//        HttpPushServiceProxy.newInstance().humanLogin(humanObj.getHumanId(),humanObj.getHuman().getServerId(),humanObj.getHuman().getTimeLogout());
        // 更新最新属性版本号
        updateProp(humanObj);

        Log.game.info("loginTrace[{}] login_step_1701, 登录到游戏 id={} , name={}", humanObj.connPoint.servId, humanObj.id, humanObj.getHuman().getName());

        // 计算人物属性
        UnitManager.inst().propCalc(humanObj);

        humanObj.name = human.getName();
        humanObj.modelSn = human.getModelSn();
        humanObj.profession = humanObj.operation.profession.getJobSn();

        /* 发送玩家登录时间及修改玩家登录时间 */
        // 当前时间
        long timeNow = Port.getTime();
        // 设置登陆状态
        if (Utils.isSameDay(timeLast, timeNow)) {
            humanObj.loginStageState = 1;
            humanObj.humanLoginStage = 1;
        } else {
            humanObj.loginStageState = 2;
            humanObj.humanLoginStage = 2;
        }

        // 启动玩家
        humanObj.startup();

        Port port = humanObj.getPortNow();
        //设置访问玩家服务接口
        HumanObjectService humanObjServ = new HumanObjectService(humanObj, port);
        humanObjServ.startup();
        port.addService(humanObjServ);

        //同步玩家信息
        syncHumanGlobal(humanObj);
        //家族玩家登陆事件和代办任务事项

        onScheduleLogin(humanObj, timeNow);

        //登录校对原子钟
        HumanAtomClockManager.inst().checkAtomClock(humanObj);
        MallManager.inst().reduceChargeScore(humanObj, Utils.getDaysBetween(timeLast, timeNow));
        CarParkManager.inst().onHumanLogin(humanObj);
        // TODO 初始化请在事件前
        Event.fire(EventKey.HUMAN_LOGIN, "humanObj", humanObj, "timeLoginLast", timeLast);

        //第一次登入
        if (timeLast == 0) {
            //预约奖励下发
            giveLeveReward(humanObj, 0);
        }

        // TODO 加代码请在上面
        if (human.isCheckRobot()) {
            Event.fire(EventKey.HUMAN_LOGIN_FINISH, "humanObj", humanObj);
            // 登录检测完成后可以退出登录了
//			humanObj.connCloseClear();
            return;
        }
        // 登录结束
        humanObj.isLogin = false;
        // 登录日志
        loginLog(humanObj);
    }

    /**
     * 登录日志
     *
     * <AUTHOR>
     * @Date 2023/4/28
     * @Param
     */
    private void loginLog(HumanObject humanObj) {
        Human human = humanObj.getHuman();
        Human2 human2 = humanObj.getHuman2();
        //添加登陆日志svn://*************/prismtest/prism/trunk/server
		String positionName = human2.getPositionName().replaceAll("\\|", "");
		LogOp.log(LogOpChannel.LOGIN, human.getId(),
				Port.getTime(),
				humanObj.clientIP,
				Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
				human.getAccount(),
				human.getName(),
				human.getLevel(),
				human.getServerId(),
                human2.getGuildName(),
				positionName,
                new BigDecimal(human.getCombat()).longValue(),
				"",
				"",
				"",
                false,
                false,
                humanObj.operation.profession.getJobSn(),
                human.getHeadSn(),
                humanObj.operation.friend.getFriendList()
		);

        // 统计记录
        StatisticsHuman.login(humanObj);
    }

    /**
     * 玩家信息变更
     *
     * @param humanObj
     * @return
     */
    public boolean humanChange(HumanObject humanObj) {
        boolean change = false;
        HumanGlobalInfo oldInfo = humanObj.hgInfo;
        if (humanObj.isLine()) {
            // 玩家离线就不同步了
            return change;
        }
        if (oldInfo == null) {
            change = true;
        } else {
            Human human = humanObj.getHuman();
            Human2 human2 = humanObj.getHuman2();
            HumanExtInfo extInfo = humanObj.getHumanExtInfo();
            if (oldInfo.headSn != human.getHeadSn()) {
                change = true;
                PMSyncDataManager.inst().humanInfoChange(humanObj, ReadonlyFormatHuman.HEAD_SN_INDEX);
            } else if (!oldInfo.name.equals(human.getName())) {
                change = true;
                PMSyncDataManager.inst().humanInfoChange(humanObj, ReadonlyFormatHuman.USERNAME_INDEX);
            } else if (oldInfo.level != human.getLevel()) {
                change = true;
                PMSyncDataManager.inst().humanInfoChange(humanObj, ReadonlyFormatHuman.LEVEL_INDEX);
            } else if (!oldInfo.combat.equals(human.getCombat())) {
                change = true;
                PMSyncDataManager.inst().humanInfoChange(humanObj, ReadonlyFormatHuman.COMBAT_INDEX);
            } else if (oldInfo.timeLogin != human.getTimeLogin()) {
                change = true;
            } else if (oldInfo.guildId != human2.getGuildId()) {
                change = true;
                PMSyncDataManager.inst().humanInfoChange(humanObj, ReadonlyFormatHuman.FACTION_ID_INDEX);
            } else if (!oldInfo.guildName.equals(human2.getGuildName())) {
                change = true;
                PMSyncDataManager.inst().humanInfoChange(humanObj, ReadonlyFormatHuman.FACTION_NAME_INDEX);
            }else if (!oldInfo.modelSn.equals(human.getModelSn())) {
                change = true;
                PMSyncDataManager.inst().humanInfoChange(humanObj, ReadonlyFormatHuman.MODEL_SN_INDEX);
            } else if (oldInfo.bubble != extInfo.getCurrentChatBubbleSn()){
                change = true;
            } else if (oldInfo.fashionSn != human2.getCurrentSkin()){
                change = true;
            } else if (!oldInfo.humanModelSn.equals(human.getModelSn())) {
                change = true;
            } else if (oldInfo.titleSn != human.getCurrentTitleSn()){
                change = true;
                PMSyncDataManager.inst().humanInfoChange(humanObj, ReadonlyFormatHuman.TITLE_SN_INDEX);
            }else if (oldInfo.borderSn != human.getCurrentHeadFrameSn()){
                change = true;
                PMSyncDataManager.inst().humanInfoChange(humanObj, ReadonlyFormatHuman.HEAD_FRAME_INDEX);
            } else if (oldInfo.headSn != human.getHeadSn()){
                change = true;
            } else if(oldInfo.guildPosition != humanObj.guildPosition){
                change = true;
            }

        }
        if (change) {
            humanObj.hgInfo = newHumanGlobalInfo(humanObj);
        }
        return change;
    }

    /**
     * 同步玩家信息到全局
     * @param humanObj
     */
    public void syncHumanGlobal(HumanObject humanObj) {
        // 添加玩家全局信息
        HumanGlobalInfo hs = newHumanGlobalInfo(humanObj);
        humanObj.hgInfo = hs;
        HumanGlobalServiceProxy prxShs = HumanGlobalServiceProxy.newInstance();
        prxShs.register(hs);
    }

    private HumanGlobalInfo newHumanGlobalInfo(HumanObject humanObj) {
        Human human = humanObj.getHuman();
        Human2 human2 = humanObj.getHuman2();
        HumanExtInfo humanExtInfo = humanObj.getHumanExtInfo();
        // 添加玩家全局信息
        HumanGlobalInfo hs = new HumanGlobalInfo();
        hs.id = humanObj.id;
        hs.modelSn = human.getSn();
        hs.humanModelSn = human.getModelSn();
        hs.account = human.getAccount();
        hs.channel = human.getChannel();
        hs.name = human.getName();
        hs.nodeId = Port.getCurrent().getNode().getId();
        hs.portId = Port.getCurrent().getId();
        hs.level = human.getLevel();
        hs.combat = human.getCombat();
        hs.profession = humanObj.operation.profession.getJobSn();
        hs.connPoint = humanObj.connPoint;
        hs.timeLogin = human.getTimeLogin();
        hs.headSn = human.getHeadSn();
        hs.guildId = human2.getGuildId();
        hs.guildName = human2.getGuildName();
        hs.guildPosition = humanObj.guildPosition;
        hs.teamId = humanObj.getTeamBundleId();
        hs.syncTime = Port.getTime();
        hs.fashionSn = human2.getCurrentSkin();
        hs.borderSn = human.getCurrentHeadFrameSn();
        hs.bubble = humanExtInfo.getCurrentChatBubbleSn();
        hs.titleSn = human.getCurrentTitleSn();
//        hs.url = ;

        hs.serverId = humanObj.getHuman().getServerId();
        hs.isCheckRobot = human.isCheckRobot();

        return hs;
    }

    /**
     * 零点后需要对在线玩家进行一次登陆日志
     *
     * @param param
     */
    @Listener(EventKey.HUMAN_RESET_ZERO)
    public void writeLoginLog(Param param) {
        HumanObject humanObj = param.get("humanObj");
        long timeOpen = System.nanoTime();
        // 检测重置每日数据
        checkHumanDailyReset(humanObj);
        HumanManager.inst().sendMsg_dungeon_list_s2c(humanObj);
        // 检测功能新解锁
        checkFuncOpen(humanObj);

        long guildId = humanObj.getHuman2().getGuildId();
        if(guildId > 0){
            GuildServiceProxy proxy = GuildServiceProxy.newInstance();
            proxy.humanLogin(guildId, humanObj.id);
        }

        humanObj.getHumanExtInfo().setWheelNextTime((int)(Port.getTime()/Time.SEC));
        humanObj.getHumanExtInfo().setWheelNum(0);
        _msg_ad_wheel_info_c2s(humanObj);

        if(humanObj.isModUnlock(FuncOpenType.FUNC_SEVEN_DAY)){
            List<Integer> login7Day = Utils.strToIntList(humanObj.getHumanExtInfo().getLogin7Day());
            if(login7Day.size() < 7){
                login7Day.add(1);
                humanObj.getHumanExtInfo().setLogin7Day(Utils.listToString(login7Day));
                role_seven_login_info_c2s(humanObj);
            }
        }
//        humanObj.getHumanExtInfo().update();

        long timeEnd = System.nanoTime();
        Log.temp.info("===useTime={}, openTime={}, timeEnd={}", timeEnd - timeOpen, timeOpen, timeEnd);
    }

    /**
     * 5点重置
     *
     * @param param
     */
    @Listener(EventKey.HUMAN_RESET_FIVE)
    public void _HUMAN_RESET_FIVE(Param param) {
        HumanObject humanObj = param.get("humanObj");
        // 检测每日5点重置的数据
        checkHumanDailyReset(humanObj);
        // 重置完每日次数，再请求武道会信息，来更新可挑战红点
        KungFuRaceManager.inst()._msg_kungfu_race_info_c2s(humanObj, false);
    }

    /**
     * 用于玩家登录时候处理没有处理的时间时间
     *
     * @param humanObj
     * @param timeNow
     */
    public void onScheduleLogin(HumanObject humanObj, long timeNow) {
        Human h = humanObj.getHuman();

        long timeLast = h.getTimeLogin();
        long timeZero = Utils.getTimeBeginOfToday(timeNow);
        //更新玩家登录时间
        h.setTimeLogin(timeNow);

        //防止出现切换地图多次更新
        if (timeLast < timeZero && timeZero <= timeNow) {
            Event.fire(EventKey.HUMAN_RESET_ZERO, "humanObj", humanObj, "timeLoginLast", timeLast);
//			Log.game.debug("每日零点重置 id {} ,name {}",humanObj.id,humanObj.name);
        }else {
            //判断上次0点重置时间，没有处理则发送事件
            long zeroResetTime = humanObj.getHuman3().getZeroResetTime();
            if(zeroResetTime != 0 && zeroResetTime < Utils.getTimeBeginOfToday(timeNow)){
                Event.fire(EventKey.HUMAN_RESET_ZERO, "humanObj", humanObj, "timeLoginLast", timeLast);
                Log.game.info("玩家被挤下线了进行0点重置，id={}，timeLoginLast={},lastZeroResetTime={}", humanObj.id, timeLast, humanObj.getHuman3().getZeroResetTime());
            }
        }

        //发送本日五时首次登录事件
        long timeFive = timeZero + 5 * Time.HOUR;
        if (timeLast < timeFive && timeFive <= timeNow) {
            Event.fire(EventKey.HUMAN_RESET_FIVE, "humanObj", humanObj, "timeLoginLast", timeLast);
//			Log.game.debug("每日五点重置 id {} ,name {}",humanObj.id,humanObj.name);
        }

        //发送本周零时首次登录事件
        long timeWeek = Utils.getTimeBeginOfWeek(timeNow);
        if (timeLast < timeWeek && timeWeek <= timeNow) {
            Event.fire(EventKey.HUMAN_RESET_WEEK_ZERO, "humanObj", humanObj, "timeLoginLast", timeLast);
//			Log.game.debug("本周零时首次登录 id {} ,name {}",humanObj.id,humanObj.name);
        }
        //发送本周一时首次登录事件
        long timeWeekFive = timeWeek + 5 * Time.HOUR;
        if (timeLast < timeWeekFive && timeWeekFive <= timeNow) {
            Event.fire(EventKey.HUMAN_RESET_WEEK_5ST, "humanObj", humanObj, "timeLoginLast", timeLast);
//			Log.game.debug("本周一时首次 id {} ,name {}",humanObj.id,humanObj.name);
        }

        //发送本月一号时首次登录事件
        long timeBeginOfMonth = Utils.getTimeBeginOfMonth(timeNow);
        if (timeLast < timeBeginOfMonth && timeBeginOfMonth <= timeNow) {
//			Log.game.debug("每月一号零点发布事件  玩家id {}  当前登陆时间时间 {}",humanObj.id,timeLast);
            Event.fire(EventKey.HUMAN_RESET_MONTH_1ST, "humanObj", humanObj, "timeLoginLast", timeLast);
        }

        Event.fire(EventKey.HUMAN_RESET_CHECK, "humanObj", humanObj, "timeLoginLast", timeLast, "timeNow", Port.getTime(), "timeZero", timeZero);
    }

    @Listener({EventKey.HUMAN_RESET_ZERO, EventKey.HUMAN_RESET_CHECK})
    public void resetZero(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (!humanObj.humanReset.needResetModule(ModuleResetTypeKey.Human0)) {
            return;
        }
        long timeOpen = System.nanoTime();
        humanObj.humanReset.setModuleReset(ModuleResetTypeKey.Human0);
        humanObj.isSendGvgMsg = true;
        long timeEnd = System.nanoTime();
        Log.temp.info("===useTime={}, openTime={}, timeEnd={}", timeEnd - timeOpen, timeOpen, timeEnd);
    }


    @Listener({EventKey.HUMAN_RESET_FIVE, EventKey.HUMAN_RESET_CHECK})
    public void resetFive(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (!humanObj.humanReset.needResetModule(ModuleResetTypeKey.Human1)) {
            return;
        }
        humanObj.humanReset.setModuleReset(ModuleResetTypeKey.Human1);
    }

    public void onScheduleEvent(HumanObject humanObj, int key, long timeNow) {
        Human h = humanObj.getHuman();
        if (h.getTimeLogin() == 0) {
            firstLogin(humanObj);
        }
        h.setTimeLogin(timeNow);
        h.update();
        Event.fire(key, "humanObj", humanObj, "timeLoginLast", timeNow, "online", true);
    }

    public int stageHistoryCommonSn(HumanObject humanObj, boolean relive) {
        int result = -1;

        Human3 human = humanObj.getHuman3();

        List<StageHistory> stageHistoryList = StageHistory.stageHistoryList(human.getStageHistory());

        //循环遍历查找地图
        for (StageHistory stageHistory : stageHistoryList) {
            if (stageHistory.mapType.equals(StageMapTypeKey.common.getS())) {
                result = stageHistory.mapSn;
                break;
            }
        }
        return result;
    }

    /**
     * 获取上一次的位置
     *
     * @param humanObj
     * @return
     */
    public int stageHistorySn(HumanObject humanObj) {
        int result = -1;

        //循环遍历查找地图
        StageHistory stageHistory = StageHistory.getStageHistoryIndex(humanObj.getHuman3().getStageHistory(), 0);
        if (stageHistory != null) {
            result = stageHistory.mapSn;
        }
        return result;
    }

    /**
     * 给主角增加经验
     *
     * @param humanObj
     * @param exp
     * @param log
     */
    public void expAdd(HumanObject humanObj, long exp, MoneyItemLogKey log) {
//		Log.game.info("给主角增加经验 time={}",Port.getTime());

        Human human = humanObj.getHuman();
        long expNew = human.getExpCur() + exp;
        human.setExpCur(expNew);
        if (human.getLevel() == GlobalConfVal.MAX_LEVEL) {
            return;
        }
        //默认日志
        if (log == null) log = MoneyItemLogKey.未设置;

        boolean levelUp = false;
        int levelNow = human.getLevel();
        long expCurNow = human.getExpCur();
        ConfLevel confLevel = ConfLevel.get(levelNow + 1);
        if (confLevel == null) {
            return;
        }
        int expend = confLevel.expend;
        //如果达到升级条件，进行经验值累加
        while (levelNow != GlobalConfVal.MAX_LEVEL && expCurNow >= expend) {
            levelUp = true;
            levelNow++;
            ConfLevel confNext = ConfLevel.get(levelNow + 1);
            if (confNext == null) {
                break;
            }
            expend = confNext.expend;
        }
        if (levelNow < human.getLevel()) {
            Log.temp.error("===预计出bug了，等级回档。humanId={}, name={}, level={}, exp={}, expend={}, levelNew={}, expNew={}, expUpgradeNew={}",
                    humanObj.id, humanObj.name, human.getLevel(), human.getExpCur(), expend, levelNow, expCurNow, expend);
        }
        human.setLevel(levelNow);


        //如果升级 给前端发布消息
        if (levelUp) {
            ConfLevel conf = ConfLevel.get(levelNow);
            if (conf != null) {
                //updatePowerPar(humanObj, EModule.Level, Utils.longValue(conf.powerPar));
            }
            sendMsg_role_info_change_s2c(humanObj, RoleInfoKey.ROLE_ATTR_LVL, RoleInfoKey.ROLE_ATTR_EXP);
            //添加升级日志
            LogOp.log(LogOpChannel.UPGRADE, 	human.getId(),
                    human.getName(),
                    human.getLevel(),
                    Port.getTime(),
                    Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
                    human.getAccount(),
                    human.getServerId()
            );
            Event.fire(EventKey.HUMAN_UPGRADE, "humanObj", humanObj);
            HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.level);
        } else{
            sendMsg_role_info_change_s2c(humanObj,  RoleInfoKey.ROLE_ATTR_EXP);
        }
        if (S.isTestLog) {
            Log.temp.info("== humanLv={}, exp ={}", human.getLevel(), expCurNow);
        }
    }

    //货币结束**************************************************************


    private void giveLeveReward(HumanObject humanObj, int preLevel) {
        long timestamp = Port.getTime();
//		ConfGlobal timeEndConf = ConfGlobal.get(21103);
//		if(timeEndConf == null){
//			return;
//		}
//		long timeEnd = Utils.formatTimeToLong(timeEndConf.strValue);
//		if(timestamp > timeEnd){
//			return;
//		}
//		long timeStart = Utils.formatTimeToLong(ConfGlobal.get(21102).strValue);
//		if(timestamp < timeStart){
//			return;
//		}
//		ConfGlobal confReward = ConfGlobal.get(21104);
//		if(confReward == null || confReward.intArray==null || confReward.intArray.length != confReward.strArray.length){
//			return;
//		}
//		for(int i = 0; i < confReward.intArray.length; i++){
//			if(preLevel < confReward.intArray[i] && humanObj.getHuman().getLevel() >= confReward.intArray[i]){
//				MailManager.inst().sendMail(humanObj.getHumanId(),Utils.intValue(confReward.strArray[i]),null,null);
//			}
//		}
    }

    @Listener(EventKey.POCKET_LINE_HANDLE_END)
    public void readyToSendInitataToClient(Param param) {
        HumanObject humanObj = param.get();
        // 发送初始化消息给客户端
//		sendInitDataToClient(humanObj);// TODO 目前不走这个，这个会导致登录时客户端出问题
    }

    /**
     * 玩家登录游戏后 加载玩家的数据 对于简单的符合规则的数据 可以统一再这里加载
     *
     * @param param
     */
    @Listener(EventKey.HUMAN_DATA_LOAD_BEGIN)
    public void loadHumanData(Param param) {
        HumanObject humanObj = param.get();

        long humanId = humanObj.id;
        // 角色扩展信息
        LoadHumanDataUtils.loadFast(humanObj, HumanExtInfo.tableName,  HumanExtInfo.tableName, HumanExtInfo.K.id, humanId);
        //玩家附加信息（扩展）
        LoadHumanDataUtils.loadFast(humanObj, HumanSubjoinInfo.tableName, HumanSubjoinInfo.tableName, HumanSubjoinInfo.K.id, humanId);
        // 货币信息
        LoadHumanDataUtils.loadFast(humanObj, Currency.tableName, Currency.tableName, Currency.K.id, humanId);
        // 属性加成
        LoadHumanDataUtils.loadFast(humanObj, UnitPropPlus.tableName, UnitPropPlus.tableName, UnitPropPlus.K.id, humanId);
        // 任务
        LoadHumanDataUtils.loadFast(humanObj, TaskInfo.tableName, TaskInfo.tableName, TaskInfo.K.id, humanId);
        // 完成的任务
        LoadHumanDataUtils.loadFast(humanObj, TaskFinish.tableName, TaskFinish.tableName, TaskFinish.K.id, humanId);
        // 商店
        LoadHumanDataUtils.loadFast(humanObj, Mall.tableName, Mall.tableName, Mall.K.id, humanId);
        // buff
        LoadHumanDataUtils.loadFast(humanObj, Buff.tableName, Buff.tableName, Buff.K.idAffect, humanId);
        // 物品系列
        LoadHumanDataUtils.loadFast(humanObj, Item.tableName, Item.tableName, Item.K.id, humanId);
        //充值记录
        LoadHumanDataUtils.loadFast(humanObj, PayLog.tableName, PayLog.tableName, PayLog.K.roleId, String.valueOf(humanId));
        //加载邮件
        LoadHumanDataUtils.loadFast(humanObj, Mail.tableName, Mail.tableName, Mail.K.humanId, humanId);
        //加载每日重置信息
        LoadHumanDataUtils.loadFast(humanObj, HumanDailyResetTime.tableName, HumanDailyResetTime.tableName, HumanDailyResetTime.K.id, humanId);
        // 待办事件
        LoadHumanDataUtils.loadFast(humanObj, PocketLine.tableName, PocketLine.tableName, PocketLine.K.humanId, humanId);
        // 加载职业
        LoadHumanDataUtils.loadFast(humanObj, Profession.tableName, Profession.tableName, Profession.K.id, humanId);
        // 加载客户端数据
        LoadHumanDataUtils.loadFast(humanObj, ClientInfo.tableName, ClientInfo.tableName, ClientInfo.K.humanId, humanId);
        // 加载充值礼包
        LoadHumanDataUtils.loadFast(humanObj, PayGift.tableName, PayGift.tableName, PayGift.K.humanId, humanId);
        // 加载装备数据
        LoadHumanDataUtils.loadFast(humanObj, Equip.tableName, Equip.tableName, Equip.K.humanId, humanId);
        //好友
        LoadHumanDataUtils.loadFast(humanObj, Friend.tableName, Friend.tableName, Friend.K.id, humanId);
        //神器
        LoadHumanDataUtils.loadFast(humanObj, Artifact.tableName, Artifact.tableName, Artifact.K.id, humanId);
        //坐骑
        LoadHumanDataUtils.loadFast(humanObj, Mount.tableName, Mount.tableName, Mount.K.id, humanId);
        //背饰
        LoadHumanDataUtils.loadFast(humanObj, Wing.tableName, Wing.tableName, Wing.K.id, humanId);
        //遗迹
        LoadHumanDataUtils.loadFast(humanObj, Relic.tableName, Relic.tableName, Relic.K.id, humanId);
        //挖矿科技
        LoadHumanDataUtils.loadFast(humanObj, Mine.tableName, Mine.tableName, Mine.K.id, humanId);
        //庄园
        LoadHumanDataUtils.loadFast(humanObj, Farm.tableName, Farm.tableName, Farm.K.id, humanId);
        LoadHumanDataUtils.loadFast(humanObj, Farm2.tableName, Farm2.tableName, Farm2.K.id, humanId);
        // 武魂
        LoadHumanDataUtils.loadFast(humanObj, Fate.tableName, Fate.tableName, Fate.K.id, humanId);
        // 抓捕
        LoadHumanDataUtils.loadFast(humanObj, Capture.tableName, Capture.tableName, Capture.K.id, humanId);
        // 特权
        LoadHumanDataUtils.loadFast(humanObj, Privilege.tableName, Privilege.tableName, Privilege.K.id, humanId);

        // 活动
//		LoadHumanDataUtils.loadFast(humanObj, Activity.tableName, Activity.tableName, Activity.K.humanId, humanId);
        // 玩法活动
//		LoadHumanDataUtils.loadFast(humanObj, ActivityList.tableName, ActivityList.tableName, ActivityList.K.humanId, humanId);

//		//时装
//		LoadHumanDataUtils.loadFast(humanObj, Fashion.tableName, Fashion.tableName, Fashion.K.humanId, humanId);
//
//		//任务详情
//		LoadHumanDataUtils.loadFast(humanObj,  QuestItem.tableName, QuestItem.tableName, QuestItem.K.humanId, humanId);


//		//加载所有充值活动掉落包赠送次数信息
//		LoadHumanDataUtils.loadFast(humanObj, PayDropNum.tableName,  PayDropNum.tableName, PayDropNum.K.humanId, humanId);


    }

    private void checkAndInitData(HumanObject humanObj) {
        humanObj.operation.taskData.checkAdventureTask(humanObj);
        humanObj.operation.taskData.checkAchievementTask(humanObj);
        humanObj.operation.taskData.checkKungFuRaceTask(humanObj);
        int fixVersion = humanObj.getHuman3().getFixVersion();
        if(fixVersion >= FixVersionType.CURRENT){
            return;
        }
        if(fixVersion < FixVersionType.EQUIP_BOOK){
            EquipManager.inst().fixDuplicateEquipBook(humanObj);
        }
        if(fixVersion < FixVersionType.SKIN_LEVEL){
            skinFix(humanObj);
        }
        if(fixVersion < FixVersionType.FARM_REPORT){
            HomeManager.inst().farmReportFix(humanObj);
        }
        if(fixVersion < FixVersionType.ACTIVITY_TASK){
            //HOME_STATUE有的没有修复成功，再修复一次
            if(humanObj.farmData.farm2 != null){
                HomeManager.inst().unlockFarmStatueTab(humanObj);
                humanObj.farmData.farm2.update();
            }
            ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_20, 0);
            //初始化新加的zeroResetTime字段
            HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.pvpTicket.getType());
            if(info != null){
                humanObj.getHuman3().setZeroResetTime(info.getResetTime());
            }
        }
        if(fixVersion < FixVersionType.CAR_PARK_INIT){
            CarParkManager.inst().fixCarPark(humanObj);
        }
        if(fixVersion < FixVersionType.PRIVILEGE_CARD){
            PrivilegeManager.inst().updatePorpCalc(humanObj);
        }
        if(fixVersion < FixVersionType.CAR_PARK_210_SKIN){
            if(humanObj.getHuman().getServerId() == 30210){
                CarParkManager.inst().fixCarPark210(humanObj);
            }
        }
        if(fixVersion < FixVersionType.PAY_CHECK){
            HumanManager.inst().checkPay(humanObj);
        }
        if(fixVersion < FixVersionType.FARM_BUILD){
            HomeManager.inst().buildingPointFix(humanObj);
        }
        if(fixVersion < FixVersionType.EMOJI_NAME){
            NameManager.inst().emojiNameFix(humanObj);
        }
        if(fixVersion < FixVersionType.PRIVILEGE_ATTR){
            PrivilegeManager.inst().updatePorpCalc(humanObj);
        }
        humanObj.getHuman3().setFixVersion(FixVersionType.CURRENT);
    }

    private void skinFix(HumanObject humanObj){
        //皮肤等级从原来的1开始改成从0开始
        List<SkinVo> skinVoList = SkinVo.fromJSONArrayStrToList(humanObj.getHumanExtInfo().getSkinList());
        for (SkinVo skinVo : skinVoList) {
            if(ConfFashionSkin_0.get(skinVo.sn, skinVo.level+1)!=null){
                ++skinVo.level;
            }
        }
        humanObj.getHumanExtInfo().setSkinList(SkinVo.listToJSONArrayStr(skinVoList));
        humanObj.getHumanExtInfo().update();
    }

    /**
     * 玩家登录游戏后 加载玩家的数据 开始了一条新的数据加载
     *
     * @param param
     */
    @Listener(EventKey.HUMAN_DATA_LOAD_BEGIN_ONE)
    public void loadDataBeginOne(Param param) {
        HumanObject humanObj = param.get();
        humanObj.loadingNum++;
    }

    /**
     * 玩家登录游戏后 加载玩家的数据 完成了一条新的数据加载
     *
     * @param param
     */
    @Listener(EventKey.HUMAN_DATA_LOAD_FINISH_ONE)
    public void loadDataFinishOne(Param param) {
        HumanObject humanObj = param.get();
        humanObj.loadingNum--;

        // 玩家数据全部加载完毕 可以正式进行登录了
        if (humanObj.loadingNum <= 0) {
            // 检测后续功能的数据初始化
            checkHumanTable(humanObj);
            // 检查初始化的数据，如果不存在则增加（用于版本升级）
            checkAndInitData(humanObj);

            initLoad(humanObj);

            // 玩家数据加载完毕后 登录游戏
            login(humanObj);

            // 返回
            Port port = Port.getCurrent();
            port.returnsAsync(humanObj.loadingPID, "node", port.getNode().getId(), "port", port.getId());
        }
    }

    /**
     * 初始业务
     *
     * <AUTHOR>
     * @Date 2024/3/14
     * @Param
     */
    private void initLoad(HumanObject humanObj) {
        Human2 human = humanObj.getHuman2();
        Human3 human3 = humanObj.getHuman3();
        // 初始同伴数据
        humanObj.operation.petData.initPet(human, human3);
        // 初始技能数据
        humanObj.operation.skillData.initSkillData(human,human3);
        //双人本数据要在伙伴初始化后面
        if(humanObj.operation.doubleChapter == null && humanObj.isModUnlock(FuncOpenType.FUNC_DOUBLE_LADDER)) {
            DoubleChapterManager.inst().createDoubleChapter(humanObj);
        }
    }

    /**
     * 检测后续功能的数据初始化
     *
     * <AUTHOR>
     * @Date 2021/8/25
     * @Param
     */
    private void checkHumanTable(HumanObject humanObj) {

        if (humanObj.operation.clientInfo == null) {
            ClientInfo clientInfo = new ClientInfo();
            clientInfo.setHumanId(humanObj.id);
            clientInfo.setId(humanObj.id);
            clientInfo.persist();
            humanObj.operation.clientInfo = clientInfo;
        }

        if(humanObj.operation.human3 == null){
            Human3 human3 = new Human3();
            human3.setId(humanObj.id);
            human3.setSkillIllustratedMap(Utils.mapIntIntToJSON(GlobalConfVal.getInitSkillIllustratedMap()));
            human3.setPetIllustratedMap(Utils.mapIntIntToJSON(GlobalConfVal.getInitPetIllustratedMap()));
            human3.setFixVersion(FixVersionType.CURRENT);
            human3.persist();
            humanObj.operation.human3 = human3;
            humanObj.operation.initRepDiffcultyMap();
        }

        if(humanObj.operation.human2 == null){
            Human2 human2 = new Human2();
            human2.setId(humanObj.id);
            human2.persist();
            humanObj.operation.human2 = human2;
        }

        if (humanObj.operation.itemData.isNull()) {
            Item item = new Item();
            item.setId(humanObj.id);
            item.persist();
            humanObj.operation.itemData.init(item);
        }
        if (humanObj.getHumanExtInfo() == null) {
            //扩展表
            HumanExtInfo extInfo = new HumanExtInfo();
            extInfo.setId(humanObj.id);
            extInfo.setEquipBoxLv(1);
            //聊天气泡
            List<Personality> bubbleList = new ArrayList<>();
            Personality bubble = new Personality(1,0,0);
            bubbleList.add(bubble);
            extInfo.setChatBubbleList(Personality.listToJSONArrayStr(bubbleList));
            extInfo.setCurrentChatBubbleSn(1);
            //时装
            List<SkinVo> skinVoList = new ArrayList<>();
            SkinVo skinVo = new SkinVo(DEFAULT_SKIN_2, 2);
            skinVoList.add(skinVo);
            extInfo.setSkinList(SkinVo.listToJSONArrayStr(skinVoList));
            //行装
            Map<Integer, PlanVo> planMap = new HashMap<>();
            planMap.put(1, new PlanVo(1));
            extInfo.setPlanMap(PlanVo.mapToString(planMap));
            extInfo.setPlan(1);
            humanObj.operation.planVoMap = planMap;
            int[] arr = new int[MAX_DEFAULT_PLAN];
            Arrays.fill(arr, 1);
            extInfo.setDefaultPlanArr(Utils.arrayIntToStr(arr));
            // 持久化
            extInfo.persist();
            humanObj.dataPers.extInfo = extInfo;
            humanObj.idGen = extInfo.getIdGen();
        }

        if (humanObj.operation.human2 == null) {
            Human2 humanBasic = new Human2();
            humanBasic.setId(humanObj.id);
            humanBasic.persist();
            humanObj.operation.human2 = humanBasic;
        }

        if (humanObj.operation.taskData.isNull()) {
            TaskInfo taskInfo = new TaskInfo();
            taskInfo.setId(humanObj.id);
            JSONArray ja = new JSONArray();
            ConfMainTask conf = ConfMainTask.get(GlobalConfVal.initMainTask);
            MainTaskVO vo = new MainTaskVO(conf);
            ja.add(vo.toString());
            taskInfo.setMainTaskJSON(ja.toJSONString());
            taskInfo.persist();
            humanObj.operation.taskData.load(taskInfo);

            ConfAdventureLevel confAdvLv = ConfAdventureLevel.get(humanObj.getHumanExtInfo().getAdventureTitleLv());
            if(confAdvLv != null && confAdvLv.taskGroup != null && confAdvLv.taskGroup.length > 0){
                humanObj.operation.taskData.acceptAdventureTask(humanObj, confAdvLv.taskGroup);
            }

        }

        checkCreateHumanDailyReset(humanObj);

        if (humanObj.dataPers.subjoinInfo == null) {
            //玩家附加信息（扩展）
            HumanSubjoinInfo subjoinInfo = new HumanSubjoinInfo();
            subjoinInfo.setId(humanObj.id);
            subjoinInfo.persist();
            humanObj.dataPers.subjoinInfo = subjoinInfo;
            humanObj.humanReset = new HumanReset(subjoinInfo);
        }

        if (humanObj.currency == null) {
            // 玩家货币信息
            Currency currency = new Currency();
            currency.setId(humanObj.id);
            currency.persist();
            humanObj.currency = currency;
        }
        if (humanObj.operation.profession == null) {
            int jobSn = ConfGlobal.get(ConfGlobalKey.默认职业.SN).value;
            ConfJobs confJobs = ConfJobs.get(jobSn);

            Profession profession = new Profession();
            profession.setId(humanObj.id);
            profession.setJobSn(jobSn);
            humanObj.getHuman().setJobModel(jobSn);
            profession.setJobLv(confJobs.skill[0]);
            profession.setJobSkillSn(confJobs.skill[1]);
            profession.setJobSkillLv(confJobs.skill[2]);
            humanObj.getHuman2().setHairColor(confJobs.skin);
            profession.persist();
            humanObj.operation.profession = profession;
        }
        if (humanObj.operation.mall == null) {
            Mall mall = new Mall();
            mall.setId(humanObj.id);
            mall.persist();
            humanObj.operation.mall = mall;
        }
        if(humanObj.operation.friend == null) {
            Friend friend = new Friend();
            friend.setId(humanObj.id);
            friend.persist();
            humanObj.operation.friend = friend;
        }

        if(humanObj.artifact.atf == null && humanObj.isModUnlock(47)){
            //初始化神器
            Artifact artifact = new Artifact();
            artifact.setId(humanObj.id);
            artifact.setLevel(1);
            artifact.persist();
            humanObj.artifact.atf = artifact;
            ArtifactManager.inst().init(humanObj);
            ArtifactManager.inst().updatePorpCalcPower(humanObj);
        }

        if(humanObj.operation.crossWarInfo == null && humanObj.isModUnlock(FuncOpenType.FUNC_CROSS_WAR)) {
            CrossWarInfo crossWarInfo = new CrossWarInfo();
            crossWarInfo.setId(humanObj.id);
            crossWarInfo.persist();
            humanObj.operation.crossWarInfo = crossWarInfo;
        }

        if(humanObj.operation.angelData == null && humanObj.isModUnlock(FuncOpenType.FUNC_ANGEL)) {
            AngelManager.inst().createAngel(humanObj);
        }

        if(humanObj.operation.charmData == null) {
            Charm charm = new Charm();
            charm.setId(humanObj.id);
            charm.persist();
            humanObj.operation.charmData = new CharmData(charm);
        }

        if(humanObj.operation.fishData == null && humanObj.isModUnlock(FuncOpenType.FUNC_HOME_FISH)) {
            HomeFishManager.inst().createFishData(humanObj);
        }
    }

    @Listener(EventKey.HUMAN_LOGOUT)
    public void onLogout(Param param) {
        HumanObject humanObj = param.get("humanObj");
        Human3 human = humanObj.getHuman3();
        // 检测stageHistory
        checkStageHistory(human);

        //离线放置奖励
        InstanceManager.inst().initPlacingRewardOffline(humanObj);
        // 更新退出时间
        humanObj.getHuman().setTimeLogout(Port.getTime());
        human.update();
        HttpPushServiceProxy prx = HttpPushServiceProxy.newInstance();
        prx.humanLogout(humanObj.getHumanId(),humanObj.getHuman().getServerId(),humanObj.getHuman().getTimeLogout());
    }

    public void checkStageHistory(Human3 human) {
        // 同步玩家位置
        JSONArray ja = Utils.toJSONArray(human.getStageHistory());
        if (!ja.isEmpty()) {
            return;
        }
        // 设置地图信息
        int stageInitSn = this.stageInitSn;
//		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.创建角色的出生点.SN);
//		Vector2D vecTemp = MathUtils.randomPosInCircle(StageManager.inst().getBirthPosFromMapSn(stageInitSn), 0, 3);
//		if (confGlobal != null){
//			stageInitSn = confGlobal.value;
//			vecTemp = new Vector2D(confGlobal.intArray[0], confGlobal.intArray[2]);
//		}
//		ConfMap confMap = ConfMap.get(stageInitSn);
//		StageHistory stageHistory = new StageHistory(confMap.sn, confMap.sn, vecTemp.x, vecTemp.y, confMap.type);
//		human.setStageHistory(stageHistory.initToArrayJSON());
//		Log.temp.error("===humanId={}, stageHistoryNew={}", human.getId(), human.getStageHistory());
    }

    /**
     * 玩家完成加载登录到地图中时进行操作
     *
     * @param params
     */
    @Listener(value = {EventKey.HUMAN_STAGE_ENTER_BEFORE, EventBridgeKey.BRIDGE_HUMAN_STAGE_ENTER})
    public void onHumanStageEnterBefore(Param params) {
        HumanObject humanObj = params.get("humanObj");

        // 设置状态 代表客户端已经准备好了
        humanObj.isClientStageReady = true;

    }

    public void changeModel(HumanObject humanObj, String modelSn) {
        boolean canChange = false;
        //判断是否有自己的武将
//		for (UnitObject uo : humanObj.slaves.values()) {
//			if(uo == null) {
//				continue;
//			}
//			if(uo.confModel.sn.equals(modelSn)) {
//				canChange = true;
//				break;
//			}
//		}

        if (!canChange) {
//			Inform.user(humanObj.id, Inform.提示操作, "没有对应的武将！");
            return;
        }

        humanObj.getUnit().setModelSn(modelSn);
//		SCChangeModel.Builder msg = SCChangeModel.newBuilder();
//		msg.setHumId(humanObj.id);
//		msg.setModel(modelSn);
//		StageManager.inst().sendMsgToArea(msg, humanObj.stageObj, humanObj.getPosNow());
    }

    @Listener(EventKey.ITEM_CHANGE_ADD)
    public void onItemAdd(Param param) {
        HumanObject humanObj = Utils.getParamValue(param, "humanObj", null);
        int itemSn = Utils.getParamValue(param, "itemSn", 0);
        int itemNum = Utils.getParamValue(param, "itemNum", 0);
        if(!GlobalConfVal.itemSnEmojiSnMap.containsKey(itemSn)){
            return;
        }
        if(itemNum <= 0){
            Log.temp.error("表情包道具，humanId={}, itemSn={}, itemNum={}", humanObj.id, itemSn, itemNum);
            return;
        }
        Human3 human3 = humanObj.getHuman3();
        ConfGoods conf = ConfGoods.get(itemSn);
        if(conf.type != ItemConstants.表情包){
            Log.temp.error("非表情包道具，忽略");
            return;
        }
        boolean isUp = false;
        List<Integer> emojiSnList = humanObj.operation.emojiSnList;
        Map<Integer, Long> snTimeMap = humanObj.operation.emojiSnTimeMap;
        for(int i = 0; i <  conf.effect.length; i++){
            int emojiSn =  conf.effect[i][0];
            int hour = conf.effect[i][1];
            if(hour > 0){ // 策划要求凑整小时
                long endTime = 0;
                if(snTimeMap.containsKey(emojiSn)) {
                    endTime = snTimeMap.get(emojiSn);
                }
                long timeNow = Port.getTime();
                if(endTime < timeNow){
                    endTime = timeNow;
                }
                endTime += (hour * itemNum * Time.HOUR);
                int endHour = Utils.getHourOfDay(endTime);
                long endTimeNew = Utils.getTimeHourOfToday(endTime, endHour);
                if(endTime > endTimeNew){
                    endTimeNew += Time.HOUR;
                }
                snTimeMap.put(emojiSn, endTimeNew);
                isUp = true;
            }
            if(!emojiSnList.contains(emojiSn)) {
                emojiSnList.add(emojiSn);
                isUp = true;
            }
        }
        if(isUp){
            human3.setEmojiSnList(Utils.listToString(emojiSnList));
            human3.setEmojiSnExpiredMap(Utils.mapIntLongToJSON(snTimeMap));
            human3.update();
        }

        sendMsg_emoji_info_s2c(humanObj);
    }

    @Listener(EventKey.HUMAN_RESET_EVERY_HOUR)
    public void _HUMAN_RESET_EVERY_HOUR(Param param) {
        HumanObject humanObj = param.get("humanObj");
        long openTime = System.nanoTime();
        checkEmoji(humanObj);
        long timeEnd = System.nanoTime();
        Log.temp.info("===useTime={}, openTime={}, timeEnd={}", timeEnd - openTime, openTime, timeEnd);
    }

    private void checkEmojiReset(HumanObject humanObj){
        Human3 human3 = humanObj.getHuman3();
        boolean isUpdate = false;
        List<Integer> emojiSnList = humanObj.operation.emojiSnList;
        for(Map.Entry<Integer, Integer> entry : GlobalConfVal.itemSnEmojiSnMap.entrySet()){
            int itemSn = entry.getKey();
            int value = humanObj.operation.itemData.getItemNum(itemSn);
            if(value > 0){
                ConfGoods conf = ConfGoods.get(itemSn);
                for(int i = 0; i < conf.effect.length; i++){
                    int emojiSn = conf.effect[i][0];
                    int hour = conf.effect[i][1];
                    if(hour == 0) {
                        if(!emojiSnList.contains(emojiSn)){
                            emojiSnList.add(emojiSn);
                            isUpdate = true;
                        }
                    }
                }

            }
        }
        if(isUpdate){
            emojiSnList = new ArrayList<>(new HashSet<>(emojiSnList));
            human3.setEmojiSnList(Utils.listToString(emojiSnList));
            human3.update();
            sendMsg_emoji_info_s2c(humanObj);
        }
    }

    private void checkEmoji(HumanObject humanObj) {
        Human3 human3 = humanObj.getHuman3();
        Map<Integer, Long> snTimeMap = humanObj.operation.emojiSnTimeMap;//Utils.jsonToMapIntLong(human.getEmojiSnExpiredMap());
        if(snTimeMap.isEmpty()){
            return;
        }
        long timeNow = Port.getTime();
        List<Integer> removeSnList = new ArrayList<>();
        for(Map.Entry<Integer, Long> entry : snTimeMap.entrySet()){
            if(timeNow >= entry.getValue()){
                removeSnList.add(entry.getKey());
            }
        }
        if(!removeSnList.isEmpty()){
            for(int sn : removeSnList){
                snTimeMap.remove(sn);
            }
            human3.setEmojiSnExpiredMap(Utils.mapIntLongToJSON(snTimeMap));
            humanObj.operation.emojiSnList.removeAll(removeSnList);
            human3.setEmojiSnList(Utils.listToString(humanObj.operation.emojiSnList));
            sendMsg_emoji_info_s2c(humanObj);
        }

    }

    public void sendMsg_emoji_info_s2c(HumanObject humanObj){
        Map<Integer, Long> snTimeMap = humanObj.operation.emojiSnTimeMap;
        List<Integer> emojiSnList = humanObj.operation.emojiSnList;
        Set<Integer> set = new HashSet<>(emojiSnList);
        MsgChat.emoji_info_s2c.Builder msg = MsgChat.emoji_info_s2c.newBuilder();
        for(int sn : set){
            Define.p_emoji.Builder p = Define.p_emoji.newBuilder();
            p.setCfgId(sn);
            if(snTimeMap.containsKey(sn)){
                p.setEndTime((int)(snTimeMap.get(sn) / Time.SEC));
            } else {
                p.setEndTime(0);
            }
            msg.addEmojiList(p);
        }
        humanObj.sendMsg(msg);

        Log.temp.info("===sendMsg_emoji_info_s2c, humanId={}, msg={}", humanObj.id, msg.build());

    }


    @Listener(value = EventKey.ITEM_CHANGE_DEL, subInt = ItemConstants.道具)
    public void onItemDel(Param param) {

    }

    @Listener(value = EventKey.ITEM_CHANGE_MOD, subInt = ItemConstants.道具)
    public void onItemChange(Param param) {
    }

    @Listener(value = {EventKey.HUMAN_BE_KILLED, EventBridgeKey.BRIDGE_HUMAN_BE_KILLED})
    public void onHumanDie(Param param) {
        HumanObject humanObj = param.get("dead");
        int stageSn = param.get("stageSn");
        UnitObject kUnit = param.get("killer");

    }

    /**
     * 发送复活相关消息
     *
     * @param humanObj
     * @param kUnit
     * @param reviveType
     * @param maxReviveCount
     */
    public void sendReviveMsg(HumanObject humanObj, UnitObject kUnit, int[] reviveType, int[] maxReviveCount) {
        // 演武场不发消息
//		if(humanObj.stageObj instanceof StageObjectCompetition){
//			return;
//		}
//		if (humanObj.stageObj.getConfRep()!=null && humanObj.stageObj.getConfRep().repType==INSTANCE_TYPE_FACTION_ESCORT && humanObj.camp==FACTION_ESCORT_PLUNDER) return;
//		int cd = 0;
//		Log.game.info("{} 死亡,死亡地图={} 复活类型", humanObj.name, humanObj.stageObj.confMap.sn, Utils.intToIntegerList(reviveType));
//		SCDieStage.Builder msg = SCDieStage.newBuilder();
//		msg.setDeadId(humanObj.id);
//		if(kUnit!=null && kUnit.isHumanObj())
//			msg.setKiller(kUnit.name);
//		ConfRevive confRev;
//		for(int i=0;i<reviveType.length;i++){
//
//			int maxRevice = humanObj.stageObj.confMap.maxReviveCount[i];
//			confRev = ConfRevive.get(reviveType[i]);
//			if(confRev!=null){
//				int reviceCount = humanObj.getReviveCount(confRev.type);
////				if(maxReviveCount.length>=i && maxReviveCount[i]>0 && reviceCount >=maxReviveCount[i]) //已到上限则不下发复活的方式
////					continue;
//
//				DReviveInfo.Builder reviveInfo = DReviveInfo.newBuilder();
//				reviveInfo.setSn(reviveType[i]);
//				reviveInfo.setReviveCount(reviceCount);
//				reviveInfo.setSurplus(maxRevice - reviceCount);// <0则为无限复活
//				reviveInfo.setPrice(ConfReviveUtils.getRevivePrice(confRev, humanObj.getReviveCount(confRev.type)));
//				reviveInfo.setPriceType(confRev.priceType);
//
//				cd =(int)((humanObj.getReviveTime(confRev.type) - humanObj.getTime())/DateTimeUtils.SEC);
//
//				cd = Math.max(cd,0);
//				reviveInfo.setCd(cd);
//
//				msg.addReviveInfo(reviveInfo);
//			}
//		}
//		humanObj.sendMsg(msg);
    }

    /**
     * 每日首次登录重置部分信息
     *
     * @param param
     */
    @Listener(EventKey.HUMAN_LOGIN_FINISH_FIRST_TODAY)
    public void onDayFirstLogin(Param param) {
        HumanObject humanObj = param.get("humanObj");
    }

    /**
     * 玩家这辈子首次登录
     */
    @Listener(EventKey.HUMAN_ALL_LIFE_FIRST_LOGIN)
    public void onFirstLogin(Param param) {
//		HumanObject humanObj = param.get("humanObj");
//		int[] giftCodes = ConfGlobalUtils.getIntArray(ConfGlobalKey.玩家首次登录赠送的礼包码编号);
//		for(int i=0; i< giftCodes.length; i++){
//			ConfGiftCode confGift = ConfGiftCode.get(String.valueOf(giftCodes[i]));
//			if(confGift != null){
////				MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, confGift.title, confGift.content, confGift.itemId, confGift.itemNum, true);
//			}
//		}
    }


    @Listener(EventKey.HUMAN_STAGE_ENTER)
    public void onHumanStageEnter(Param params) {
        HumanObject humanObj = params.get("humanObj");

        humanObj.continueKillNum = 0;
        humanObj.continueAssist = 0;
        //移除仇恨
        humanObj.loginOut();

    }


    /**
     * 消息球
     *
     * @param param
     */
    @Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.NOTICE_BALL)
    public void factionNotice(Param param) {
        HumanObject humanObj = param.get("humanObj");
        PocketLine pocket = param.get("pocketLine");
        // 待办参数
        @SuppressWarnings("unchecked")
        Map<String, Object> resMap = JSON.parseObject(pocket.getParam(), Map.class);
        int type = (int) resMap.get("type");
        long senderId = (long) resMap.get("senderId");
        String senderName = (String) resMap.get("senderName");
        int senderLevel = (int) resMap.get("senderLevel");
        String teamId = resMap.get("teamId").toString();
        String factionId = resMap.get("factionId").toString();
        String factionName = (String) resMap.get("factionName");
        long senderCampType = (int) resMap.get("senderCampType");

    }

    /**
     * 禁言
     *
     * @param param
     */
    @Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.SILENCE)
    public void silence(Param param) {
        HumanObject humanObj = param.get("humanObj");
        PocketLine pocket = param.get("pocketLine");
        // 待办参数
        @SuppressWarnings("unchecked")
        Map<String, Object> resMap = JSON.parseObject(pocket.getParam(), Map.class);

        int operCampType = (int) resMap.get("operCampType");
        long keepTime = (long) resMap.get("keepTime");

        if (operCampType > 0 && operCampType == humanObj.getCampType()) {
            humanObj.silence(keepTime);
        }
    }

    @Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.GUILD_HELP)
    public void pocketLine_GUILD_HELP(Param param) {
        HumanObject humanObj = param.get("humanObj");
        PocketLine p = param.get("pocketLine");
        JSONObject jo = Utils.toJSONObject(p.getParam());
        int type = jo.getIntValue("type");
        int targetId = jo.getInteger("targetId");
        GuildManager.inst().help(humanObj, type, targetId);
    }


    @Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.GUILD_UPDATE)
    public void pocketLine_GUILD_UPDATE(Param param) {
        HumanObject humanObj = param.get("humanObj");
        PocketLine p = param.get("pocketLine");
        JSONObject jo = Utils.toJSONObject(p.getParam());
        long guildId = jo.getLongValue("guildId");
        GuildServiceProxy prx = GuildServiceProxy.newInstance();
        prx.getGuild(guildId);
        prx.listenResult(this::_result_getGuild, "humanObj", humanObj);
    }

    private void _result_getGuild(Param result, Param context) {
        Guild guild = Utils.getParamValue(result, "guild", null);
        HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
        GuildManager.inst().updateHumanGuildInfo(humanObj, guild);
    }

    /**
     * 采集成功后记录日志
     *
     * @param param
     */
    @Listener(EventKey.GATHER_SUCCESS)
    public void gather_success(Param param) {
        HumanObject humanObj = param.get("humanObj");
    }


    /**
     * 更新最新属性版本号
     *
     * @param humanObj
     */
    public void updateProp(HumanObject humanObj) {
        Human3 human = humanObj.getHuman3();
        int version = 1;//ConfGlobalUtils.getValue(ConfGlobalKey.属性版本号);
        // 版本号没变化
        if (version <= human.getPropVerNum()) {
            return;
        }

        // 更新版本号
        human.setPropVerNum(version);
    }

    @Listener(EventKey.HUMAN_LOGIN_FINISH)
    public void onHumanExpChange(Param param) {
        HumanObject humanObj = param.get("humanObj");
        syncHumanGlobal(humanObj);
        humanObj.isClientStageReady = true;
        checkHumanDailyReset(humanObj);

        Privilege privilege = humanObj.operation.privilege;
        if (privilege == null) {
            return;
        }
        Map<Integer, List<Integer>> privilegeCardMap = Utils.jsonToMapIntListInt(privilege.getCardMap());// 特权卡状态map
        if(privilegeCardMap.containsKey(2)){
            return;
        }
        boolean isPay = false;
        for(PayLog payLog : humanObj.dataPers.payLogs){
            if(payLog.getPropId().equals("webglProductId_10602_198")){// 永久卡
                isPay = true;
                break;
            }
        }
        // 闪钻购买特权
        String json = humanObj.operation.mall.getPayMallBuyNumMap();
        Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(json);
        if(snBuyNumMap.containsKey(10602)){// 永久卡
            isPay = true;
        }

        if(isPay){
            PrivilegeManager.inst().givePrivilegeCard(humanObj, 2);
            Log.temp.info("===玩家{}补充永久卡，更新特权卡状态", humanObj.id);
        }
    }



    private void _result_callback_backup1(Param results, Param context) {

    }

    private void _result_callback_backup2(Param results, Param context) {

    }

    private void _result_callback_backup3(Param results, Param context) {

    }

    private void _result_callback_backup4(Param results, Param context) {

    }


    /**
     * 玩家上线是重新检测5点重置
     * * @param param
     */
    @Listener(EventKey.HUMAN_LOGIN)
    public void onHumanLogin(Param param) {
        HumanObject humanObj = param.get("humanObj");
        checkCreateHumanDailyReset(humanObj);
        // 每日重置
        checkHumanDailyReset(humanObj);
        syncHumanGlobal(humanObj);
        humanObj.isClientStageReady = true;
    }


    /**
     * 检测每日次数重置
     *
     * @param humanObj
     */
    public void checkHumanDailyReset(HumanObject humanObj) {
        Map<Integer, HumanDailyResetInfo> dailyResetInfoMap = humanObj.getDailyResetInfoMap();
        for (HumanDailyResetInfo info : dailyResetInfoMap.values()) {
            humanDailyReset(humanObj, info);
        }
        humanObj.saveDailyResetRecord();
    }

    /**
     * 每日重置信息
     */
    public void humanDailyReset(HumanObject humanObj, HumanDailyResetInfo info) {
        if (info == null) {
            Log.game.error("====重置信息出错了 info=null");
            return;
        }

        boolean isReset = false;
        long timeNow = Port.getTime();
        DailyResetTypeKey type = DailyResetTypeKey.getEnumByType(info.getResetType());
        if (type == null) {
            Log.human.error("===此类型无重置,name={},type={},resetTime={}", humanObj.name, info.getResetType(), info.getResetTime());
            return;
        }
        // TODO 特殊处理每周0点重置
        if (type == DailyResetTypeKey.weekPayMall || type == DailyResetTypeKey.weekMall || type == DailyResetTypeKey.weeklyGoodsRecover) {// 每周
            if (info.getResetTime() == 0 || !Utils.isSameWeek(timeNow, info.getResetTime())) {
                isReset = true;
            }
        } else if(type == DailyResetTypeKey.arenaSeasonEndNum || type == DailyResetTypeKey.arenaRankedSeasonEndNum){
            if (info.getResetTime() == 0){
                info.setResetTime(Port.getTime());
            }
            if(!Utils.isSameWeek(timeNow, info.getResetTime())){
                isReset = true;
            } else if(type == DailyResetTypeKey.arenaRankedSeasonEndNum){
                long endTime = Utils.getTimeOfWeek(timeNow, 4, 0);
                if(info.getResetTime() < endTime && timeNow >= endTime){
                    isReset = true;
                }
            }
        }else {
            int resetHour = DailyResetTypeKey.getResetHour(type);
            if (info.getResetTime() == 0 || !Utils.isSameDay(timeNow, info.getResetTime(), resetHour)) {
                isReset = true;
            }
        }

        switch (type) {
            // TODO 每时， 不走这里
            case dailyWorldBossHour:
                break;
            // TODO 每日
            case none:
                break;
            case dailyTask: {// TODO 注意这里有其他操作，别让其他跟他并列
                if (isReset) {
                    humanObj.operation.taskData.resetDailyTask();
                    info.setResetTime(timeNow);
                    info.setValue(0);
                }
            }
            break;
            case dailyDarkTrialChapter:{
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                }
            }
            break;
            case dailyDarkTrialMaxLv: {
                if (isReset) {
                    int difficulty = 1;
                    int level = humanObj.operation.getDarkRepMaxDiffculty();
                    if (level != 0) {
                        // 根据level直接取到难度
                        int sn = GlobalConfVal.getRepSn(InstanceConstants.DARKTRIALCHAPTER_28, level);
                        ConfDarkTrialChapter conf = ConfDarkTrialChapter.get(sn);
                        if (conf != null) {
                            difficulty = conf.difficulty;
                        }
                        // 根据level是不是五星等级，决定难度要不要+1
                        if (InstanceManager.inst().isFiveStar(InstanceConstants.DARKTRIALCHAPTER_28, level)) {
                            final int finalDiff = difficulty;
                            GameServiceProxy prx = GameServiceProxy.newInstance();
                            prx.getServerGlobal(humanObj.getHuman().getServerId());
                            prx.listenResult((result, context) -> {
                                ReasonResult rr = Utils.getParamValue(result, "result", new ReasonResult(false, 1));
                                if (!rr.success) {
                                    Log.human.error("查询ServerGlobal错误，result={}", rr);
                                    return;
                                }
                                ServerGlobal serverGlobal = Utils.getParamValue(result, "serverGlobal", null);
                                int serverGlobalLevel = humanObj.operation.getRepMaxDiffculty(serverGlobal.getDarkTrialChapterSn());
                                int diff = level == serverGlobalLevel ? finalDiff + 1 : finalDiff;
                                int maxLv = InstanceManager.inst().getDarkTrialChapterMaxSn(InstanceConstants.DARKTRIALCHAPTER_28, diff);
                                if (maxLv == 0) {
                                    maxLv = level;
                                }
                                info.setValue(maxLv);
                                info.setResetTime(timeNow);
                                humanObj.saveDailyResetRecord();// 这边要补保存
                            });
                            return;
                        }
                    }
                    // 获取到能挑战的最大level
                    int maxLv = InstanceManager.inst().getDarkTrialChapterMaxSn(InstanceConstants.DARKTRIALCHAPTER_28, difficulty);
                    if (maxLv == 0) {
                        maxLv = level == 0 ? 5 : level;
                    }
                    info.setValue(maxLv);
                    info.setResetTime(timeNow);
                }
            }
            break;
            case dailyDarkTrialMaxPassLevel: {
                if (isReset) {
                    // 每日重置今天之前的最大挑战等级
                    int difficulty = 1;
                    int level = humanObj.operation.getDarkRepMaxDiffculty();
                    if (level != 0) {
                        // 根据level直接取到难度
                        int sn = GlobalConfVal.getRepSn(InstanceConstants.DARKTRIALCHAPTER_28, level);
                        ConfDarkTrialChapter conf = ConfDarkTrialChapter.get(sn);
                        if (conf != null) {
                            difficulty = conf.difficulty;
                        }
                        // 根据level是不是五星等级，决定难度要不要+1
                        if (InstanceManager.inst().isFiveStar(InstanceConstants.DARKTRIALCHAPTER_28, level)) {
                            final int finalDiff = difficulty;
                            GameServiceProxy prx = GameServiceProxy.newInstance();
                            prx.getServerGlobal(humanObj.getHuman().getServerId());
                            prx.listenResult((result, context) -> {
                                ReasonResult rr = Utils.getParamValue(result, "result", new ReasonResult(false, 1));
                                if (!rr.success) {
                                    Log.human.error("查询ServerGlobal错误，result={}", rr);
                                    return;
                                }
                                ServerGlobal serverGlobal = Utils.getParamValue(result, "serverGlobal", null);
                                int serverGlobalLevel = humanObj.operation.getRepMaxDiffculty(serverGlobal.getDarkTrialChapterSn());
                                int diff = level == serverGlobalLevel ? finalDiff : finalDiff - 1;
                                int maxLv = InstanceManager.inst().getDarkTrialChapterMaxSn(InstanceConstants.DARKTRIALCHAPTER_28, diff);
                                if (maxLv == 0) {
                                    maxLv = level;
                                }
                                info.setValue(maxLv);
                                info.setResetTime(timeNow);
                                humanObj.saveDailyResetRecord();// 这边要补保存
                            });
                            return;
                        }
                    }
                    info.setValue(level);
                    info.setResetTime(timeNow);
                }
            }
            break;
            case guildDonate:
            case pvpTicket:
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                }
            break;
            case dailyLogin: {
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(info.getValue() + 1);
                    Event.fire(EventKey.HUMAN_LOGIN_FINISH_FIRST_TODAY, "humanObj", humanObj);
                }
            }
            break;
            case teamChapterNum: { // 组队副本打次数
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                    ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.legacy_team_chapter_recover.SN);
                    int privilegeValue = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.SCRIPT,InstanceConstants.LEGACYTEAMCHAPTER_8);
                    for(int i = 0; i < confGlobal.intArray.length; i+=2){
                        int itemSn = confGlobal.intArray[i];
                        int addNum = confGlobal.intArray[i + 1]+ privilegeValue;
                        int num = humanObj.operation.itemData.getItemNum(itemSn);
                        if(num < addNum){
                            ProduceManager.inst().produceAdd(humanObj, itemSn, addNum - num, MoneyItemLogKey.每日恢复);
                        }
                    }
                }
            }
            break;
            case dailyRepSum:
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                    Map<Integer, Integer> repTypeNumMap = GlobalConfVal.repTypeNumMap;
                    for(Map.Entry<Integer, Integer> entry : repTypeNumMap.entrySet()){
                        int[] costArr = GlobalConfVal.getRepCost(entry.getKey());
                        if(costArr == null || costArr.length < 1){
                            continue;
                        }
                        int itemSn = costArr[0];
                        int privilegeValue = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.SCRIPT,entry.getKey());
                        if(entry.getKey() == InstanceConstants.PVPCHAPTER_5){
                            privilegeValue = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.CHALLENGE);
                        }
                        int value = entry.getValue() + privilegeValue;
                        int num = humanObj.operation.itemData.getItemNum(itemSn);
                        if(num < value){
                            ProduceManager.inst().produceAdd(humanObj, itemSn, value - num, MoneyItemLogKey.每日恢复);
                        }
                    }
                    ConfGlobal conf = ConfGlobal.get(ConfGlobalKey.legacy_team_chapter_assist_time.SN);
                    int num1010 = humanObj.operation.itemData.getItemNum(conf.intArray[0]);
                    if(num1010 < conf.value){
                        ProduceManager.inst().produceAdd(humanObj, conf.intArray[0], conf.value - num1010, MoneyItemLogKey.每日恢复);
                    }
                }
                break;
            case dailyArenaBridgeRankAdd:
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                    int maxValue = ConfGlobal.get(ConfGlobalKey.cross_pvp_battle_max.SN).value;
                    int[] cost = GlobalConfVal.getRepCost(InstanceConstants.CROSSPVPCHAPTER_15);
                    int num = humanObj.operation.itemData.getItemNum(cost[0]);
                    if(num < maxValue) {
                        ProduceManager.inst().produceAdd(humanObj, cost[0], maxValue - num, MoneyItemLogKey.每日恢复);
                    }
                }
                break;
            case guildDailyGveJoin:
            case guildDailyGvgSend:
            case dailyCrossWar:
            case dailyArenaBridgeBuyNum:
            case dailyArenaWinNum:	// 每日竞技场胜利次数，负数代表输的次数
            case dailyArenaBuyNum:
            case dailyWorldBoss:
            case teamAssistNum: // 组队援助次数
            case guildDailyBox:
            case guildDailyGve:
            case guildDailyBossBoxHigh:
            case guildDailyBossBox:
            case guildDailyHelp:
            case dailyMall:// 每日商店重置
            case dailyPayMall:// 每日充值
            case dailyArenaRankedr:
            case dailyDarkTrialResetNum:
            case dailyArenaRanked:
            case dailyGuildId:
            case dailyGuildLeagueSettle:
            case dailyCrossWarDefKillMonster:
            case dailyCrossWarDefKillPlayer:
            case dailyPoint:
            case dailyKungFuRaceQualifyingChallengeNum:
            case dailyKungFuRaceWorshipNum:
            case dailyRankLikeInfo:
            case dailyRechargePt:
            case dailyRechargeFreeReceiveNum:
            case dailyRechargeReceiveNum:
            case dailyRechargeCanReceiveNum:
            case dailyActCalendarFreeReceiveNum:
            case dailyClaimedLikeRewardNum:{
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                    info.setParam("");
                }
            }
            break;
            case dailyFateGungeonReward: {
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(1);
                }
            }
            break;
            // TODO 每周
            case weekMall:// 每周商店重置
            case weekPayMall:// 每周充值
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                    info.setParam("");
                }
            break;
            case dailyDraw1Count: {
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                    info.setParam("");
                    humanObj.setPushGift26DrawNum(1, false);
                }
            }
            break;
            case dailyDraw2Count: {
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                    info.setParam("");
                    humanObj.setPushGift26DrawNum(2, false);
                }
            }
            break;
            case dailyDraw3Count: {
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                    info.setParam("");
                    humanObj.setPushGift26DrawNum(3, false);
                }
            }
            break;
            case dailyGift26Expire: {
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                    info.setParam("");
                    humanObj.resetGift26Expire();
                }
            }
            break;
            case dailyGift26BuyNum: {
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                    info.setParam("");
                    humanObj.resetGift26BuyNum();
                }
            }
            break;
            case weeklyGoodsRecover: {
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                    info.setParam("");
                    ItemManager.inst()._listener_HUMAN_RESET_WEEK_5ST(new Param("humanObj", humanObj));
                }
            }
            break;
            case arenaSeasonEndNum:{
                if(isReset){
                    info.setResetTime(timeNow);
                    Map<Integer, Integer> repTypeNumMap = GlobalConfVal.repTypeNumMap;
                    int[] cost = GlobalConfVal.getRepCost(InstanceConstants.PVPCHAPTER_5);
                    int dailyValue = repTypeNumMap.get(InstanceConstants.PVPCHAPTER_5);
                    int privilegeValue = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.CHALLENGE);
                    int maxValue = dailyValue + privilegeValue;
                    int num = humanObj.operation.itemData.getItemNum(cost[0]);
                    if(num > maxValue){
                        ProduceManager.inst().checkAndCostItem(humanObj, cost[0], num - maxValue, MoneyItemLogKey.超上限恢复);
                    }
                }
            }
            break;
            case arenaRankedSeasonEndNum:{
                if(isReset){
                    info.setResetTime(timeNow);
                    int maxValue = ConfGlobal.get(ConfGlobalKey.cross_pvp_battle_max.SN).value;
                    int[] cost = GlobalConfVal.getRepCost(InstanceConstants.CROSSPVPCHAPTER_15);
                    int num = humanObj.operation.itemData.getItemNum(cost[0]);
                    if(num > maxValue) {
                        ProduceManager.inst().checkAndCostItem(humanObj, cost[0], num - maxValue, MoneyItemLogKey.超上限恢复);
                    }
                }
            }
            break;

            default:
                // 最后可以把此处放开，避免真临时加东西出问题
                if (isReset) {
                    info.setResetTime(timeNow);
                    info.setValue(0);
                    info.setParam("");
                }
                Log.game.error("====此功能类型未设置重置，请及时修改代码，humanId={},name={}, type={}", humanObj.id, humanObj.name, type);
                break;
        }
//        if (isReset) {
//            humanObj.saveDailyResetRecord();
//        }
    }

    /**
     * 检测是否有重置类型信息
     *
     * @param humanObj
     */
    public void checkCreateHumanDailyReset(HumanObject humanObj) {
        for (DailyResetTypeKey typeKey : DailyResetTypeKey.values()) {
            if (typeKey == null || typeKey == DailyResetTypeKey.none) {
                continue;
            }
            if (humanObj.containsResetType(typeKey.getType())) {
                continue;
            }

            humanObj.newHumanDailyResetInfo(typeKey.getType());
        }
    }

    /***
     * 玩家充值
     * @param param
     */
    @Listener(EventKey.PAY_NOTIFY)
    public void _listener_PAY(Param param) {
        try {
            HumanObject humanObj = param.get("humanObj");
            int sn = param.get("sn");
        } catch (Exception e) {
            Log.game.error("充值事件监听出错。e={}", e);
        }
    }

    /**
     * 玩家下线
     *
     * @param param
     */
    @Listener(EventBridgeKey.BRIDGE_HUMAN_LOGOUT)
    public void onBridgeLogout(Param param) {
        HumanObject humanObj = param.get("humanObj");

        //同步玩家跨服位置
        Vector2D pos = humanObj.getPosNow();
        HumanGlobalServiceProxy hgprx = HumanGlobalServiceProxy.newInstance(NodeAdapter.world(humanObj.realServerId));
//		hgprx.bridgeStagePosUpdate(humanObj.id, pos);

    }

    /**
     * 跨服地图坐标更新
     */
    public void bridgeStagePosUpdate(HumanObject humanObj, Vector2D pos) {
        Human3 human = humanObj.getHuman3();

        List<StageHistory> stageHistoryList = StageHistory.stageHistoryList(human.getBridgeStageHistory());
        StageHistory stageHistory = stageHistoryList.get(0);
        if (stageHistory != null) {
            stageHistory.posX = pos.x;
            stageHistory.posY = pos.y;
        }
        human.setBridgeStageHistory(StageHistory.toJSON(stageHistoryList));
    }

    /**
     * 跨服地图记录设置
     */
    public void bridgeStageHistorySet(HumanObject humanObj, long stageId, int stageSn, String stageType, Vector2D pos) {
        Human3 human = humanObj.getHuman3();

        //暂时只记录一条，后期有需要在修改方案，但结构多包一层，保持扩展性
        StageHistory stageHistory = new StageHistory(stageId, stageSn, pos.x, pos.y, stageType);
        human.setBridgeStageHistory(stageHistory.initToArrayJSON());
        human.setBridgeStageIn(true);
        //Log.stageCommon.error("bridgeStageHistorySet name {} bridge {} history {}", humanObj.name, human.isBridgeStageIn(), JSON.toJSONString(ja));
    }

    /**
     * 保留double型数据小数点后savebitnum位
     *
     * @param x
     * @param saveBitNum
     * @return
     */
    public double saveDoubleBitNum(double x, int saveBitNum) {
        NumberFormat ddf1 = NumberFormat.getNumberInstance();
        ddf1.setMaximumFractionDigits(saveBitNum);
        //去除千分位逗号
        ddf1.setGroupingUsed(false);
        String s = ddf1.format(x);
        return Utils.doubleValue(s);
    }


    @Listener(EventKey.HUMAN_UPGRADE)
    public void _on_humanPayBack(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        checkFuncOpen(humanObj);
        checkPlanOpen(humanObj);
        Human2 human = humanObj.getHuman2();
        if(human.getGuildId() > 0){
            Param paramTemp = new Param();
            paramTemp.put("level", humanObj.getHuman().getLevel());
            GuildServiceProxy proxy = GuildServiceProxy.newInstance();
            proxy.updateMemberCombat(human.getGuildId(), humanObj.id, paramTemp);
        }
    }


    @Listener({EventKey.HUMAN_COMBAT_CHANGE, EventKey.HUMAN_LOGIN_FINISH})
    public void onHumanCombatChange(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
    }

    /**
     * 客户端设置(纯客户端用)
     *
     * <AUTHOR>
     * @Date 2023/6/15
     * @Param
     */
    public void onCSClientSetInfo(HumanObject humanObj, String clientJSON, String clientJSONKey) {
        JSONObject jsonObject = JSONObject.parseObject(humanObj.operation.clientInfo.getClientJSON());
        jsonObject = (jsonObject == null) ? new JSONObject() : jsonObject;
        jsonObject.put(clientJSONKey, clientJSON);
        humanObj.operation.clientInfo.setClientJSON(jsonObject.toJSONString());
        sendSCClientSetInfo(humanObj);
    }


    @Listener(EventKey.HUMAN_LOGIN_FINISH)
    public void _listen_HUMAN_LOGIN_FINISH(Param params) {
        HumanObject humanObj = params.get("humanObj");
        // 检查玩家名字
        checkName(humanObj);
        sendSCClientSetInfo(humanObj);
        checkEmojiReset(humanObj);
        checkEmoji(humanObj);
        /// 登陆后上报玩家数据，用于更新op_human_info.timestamp
        PMSyncDataManager.inst().humanInfoChange(humanObj);
        humanObj.checkHumanActivityOpen();
        sendMsg_role_red_point_s2c(humanObj);
        updateHumanBrief(humanObj, false);
        checkPay(humanObj);
        checkHumanInitProp(humanObj);
        checkAndDelOutTimeSkin(humanObj);
    }

    /**
     * 检查玩家名字
     * @param humanObj
     */
    private void checkName(HumanObject humanObj){
        String whereSql = Utils.createStr(" WHERE id={}", humanObj.id);
        DB db = DB.newInstance(Human.tableName);
        List<String> coList = new ArrayList<>();
        coList.add(Human.K.name);
        db.findByQuery(false, whereSql, DBKey.COLUMN, coList);
        db.listenResult((results, context) -> {
            List<RecordTransient> list = results.get();
            if (list == null || list.size() != 1) {
                Log.temp.info("===checkName: 没有找到数据, {}", context);
                return;
            }
            RecordTransient record = list.get(0);
            if(record != null){
                String curName = humanObj.getHuman().getName();
                String mysqlName = record.get(Human.K.name);
                int serverId = humanObj.getHuman().getServerId();
                NameServiceProxy proxy = NameServiceProxy.newInstance();
                if(proxy != null && !curName.equals(mysqlName)){
                    // redis与mysql名字不一致
                    proxy.repeat(serverId, curName);
                    proxy.listenResult((results2, context2) -> {
                        boolean isRepeat = Utils.getParamValue(results, "repeat", false);
                        String newName = curName;
                        if(isRepeat){
                            // 重名了，强行改为旧名字+#serverId
                            newName = Utils.createStr("{}#{}", curName, Utils.getServerIdTo(serverId));
                            Log.temp.info("===checkName: 玩家名字={}重名了，改为{} humanId={}", curName, newName, humanObj.id);
                        }
                        // 更新mysql内名字，与redis保持一致
                        String updateSql = Utils.createStr("UPDATE {} SET `{}`=? WHERE `{}`=? AND `{}`=?",
                                Human.tableName, Human.K.name, Human.K.id, Human.K.name);
                        db.sql(false, true, updateSql, newName, humanObj.id, mysqlName);
                        proxy.change(serverId, mysqlName, newName);
                        Log.temp.info("===checkName: 玩家名字={}和mysql中名字={}不一致，mysql名字和nameList改为{} humanId={}", curName, mysqlName, newName, humanObj.id);
                    });
                }
            }
        });

    }

    private void checkHumanInitProp(HumanObject humanObj){
        // TODO 每次登陆就重算吧，不确定啥时候又改初始属性
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.initial_attr.SN);
        PropCalc initProp = new PropCalc();
        String[] strArr = Utils.splitStr(confGlobal.strValue, "\\|");
        for(int i = 0; i < strArr.length; i++){
            int[] intArr = Utils.strToIntArray(strArr[i], "\\,");
            initProp.plus(String.valueOf(intArr[0]), intArr[1]);
        }
        UnitPropPlusMap unitPropPlusMap = humanObj.dataPers.unitPropPlus;
        String pp = unitPropPlusMap.fieldRead(unitPropPlusMap.unitPropPlus, UnitPropPlus.K.initAttr);
        if(initProp.toJSONStr().equals(pp)){
            return;
        }
        unitPropPlusMap.setFieldRead(UnitPropPlus.K.initAttr, initProp.toJSONStr());
        UnitManager.inst().propCalc(humanObj);
        HumanManager.inst().sendMsg_role_total_sp_update_s2c(humanObj);
    }


    private void checkPay(HumanObject humanObj) {
        long timeNow = Port.getTime();
        if(timeNow >= 1737993600000L){// 超过2025年1月28日，不处理
            return;
        }
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(3);
        if(data == null){
            return;
        }
        long endTime = data.getActControlData().getCloseTime() * Time.SEC;
        if(timeNow >= endTime){
            return;
        }
        float paySum = humanObj.getHuman3().getTotalRMB() * 1f / 100f;
        boolean isPay = false;
        for(ActivityTaskVO vo : data.getActivityTask().values()){
            if(vo.isFinish()){
                continue;
            }
            final int DECIMAL_PRECISION = 100;
            float inputValue = Utils.floatValue(paySum);
            int integerPart = (int)inputValue;
            int decimalPart = (int)((inputValue - integerPart) * DECIMAL_PRECISION);

            int newParam1 = decimalPart;
            int additionalInteger = newParam1 / DECIMAL_PRECISION;
            int newDecimalPart = newParam1 % DECIMAL_PRECISION;

            int newPlan = integerPart + additionalInteger;
            if(vo.getPlan() < newPlan){
                vo.setParam1(newDecimalPart);
                vo.setPlan(newPlan);
                isPay = true;
                vo.checkStatus();
            }
        }
        if(isPay){
            data.setActivityTask();
        }

    }

    public boolean isSyncCrossHumanBrief(HumanObject humanObj){
        if (humanObj.isModUnlock(66)) {// 排位赛
            return true;
        }
        if (humanObj.getHuman3().isArenaGradingEnd()) {
            return true;
        }
        if(humanObj.getHuman2().getGuildId() > 0 && humanObj.isModUnlock(64)){
            return true;
        }
        if (humanObj.isModUnlock(FuncOpenType.FUNC_CROSS_WAR)) {// 跨服战
            return true;
        }
        if(humanObj.isArenaBridge){
            return true;
        }
        return false;
    }

    public HumanBrief updateHumanBrief(HumanObject humanObj){
        HumanBrief humanBrief = humanObj.operation.getBrief();
        if(humanBrief==null){
            getHumanBriefData(humanObj);
        }
        return humanObj.operation.getBrief();
    }

    public HumanBrief getHumanBrief(HumanObject humanObj, boolean update){
        HumanBrief humanBrief = humanObj.operation.getBrief();
        if(humanBrief==null||update){
            getHumanBriefData(humanObj);
        }else{
            //检查缓存时间
            if(!humanObj.isCd(CdType.human_brief_update_time)){
                humanObj.addCd(CdType.human_brief_update_time,Time.MIN);
                getHumanBriefData(humanObj);
            }
        }
        return humanObj.operation.getBrief();
    }

    private void getHumanBriefData(HumanObject humanObj) {
        Human human = humanObj.getHuman();
        boolean isInit = true;
        HumanBrief humanBrief = humanObj.operation.getBrief();
        if (humanBrief == null) {
            humanBrief = new HumanBrief();
            humanBrief.setId(humanObj.id);
            isInit = Utils.checkBitValueLong(human.getModInit(), EModuleTableInit.HumanBrief.getType());
            if(isInit) {
                humanBrief.reset();
            }
        }
        HumanBriefVO vo = new HumanBriefVO(humanObj);
        vo.buildHumanBrief(humanBrief);
        if(!isInit){
            humanBrief.persist();
            setModInit(humanObj,EModuleTableInit.HumanBrief,true);
        }
        humanObj.operation.setBrief(humanBrief);
    }

    public void setModInit(HumanObject humanObj, EModuleTableInit module,boolean value){
        Human human = humanObj.getHuman();
        human.setModInit(Utils.setBitValueLong(human.getModInit(), module.getType(), value?1:0));
    }

    public boolean isModInit(HumanObject humanObj, EModuleTableInit module){
        Human human = humanObj.getHuman();
        return Utils.checkBitValueLong(human.getModInit(), module.getType());
    }

    public void setFlags(HumanObject humanObj, EHumanFlags module,boolean value){
        Human3 human = humanObj.getHuman3();
        human.setFlags(Utils.setBitValueLong(human.getFlags(), module.getPos(), value?1:0));
    }

    public boolean getFlags(HumanObject humanObj, EHumanFlags module){
        Human3 human = humanObj.getHuman3();
        return Utils.checkBitValueLong(human.getFlags(), module.getPos());
    }

    public void updateHumanBrief(HumanObject humanObj, boolean isSync) {
        updateHumanBrief(humanObj, isSync, res->{});
    }

    public void updateHumanBrief(HumanObject humanObj, boolean isSync, Handler<AsyncResult<Boolean>> onComplete) {
        HumanBrief humanBrief = getHumanBrief(humanObj, isSync);
        if(!isSync){
            return;
        }
        // TODO 同步数据到跨服, 后续走策略
        if (!isSyncCrossHumanBrief(humanObj)) {
            return;
        }
        syncHumanBrief(humanBrief, onComplete);
    }

    public void syncHumanBrief(HumanBrief brief){
        syncHumanBrief(brief, res->{});
    }

    public void syncHumanBrief(HumanBrief brief, Handler<AsyncResult<Boolean>> onComplete){
        Port port = Port.getCurrent();
        if(brief == null){
            AsyncActionResult.success(port, onComplete, false);
            return;
        }
        if(brief.getRoleFigure().length == 0 || brief.getBattleRole().length == 0){
            Log.temp.error("角色形象数据为空，或无战斗属性。无法同步， {}", brief);
            AsyncActionResult.success(port, onComplete, false);
            return;
        }
        String redisKey = "HumanBrief." + brief.getId();
//        Log.temp.info("====同步角色数据, key={}, json={}", brief.getId(), brief.getAllObjNew());
        CrossRedis.setHashJsonObject(redisKey, brief.getAllObjNew(), h -> {
            if(h.failed()){
                AsyncActionResult.success(port, onComplete, false);
                return;
            }
            Boolean result = h.result();
            if(!result){
                LogCore.core.error("===result={}, key={}, json={}", result, redisKey, brief.getAllObjNew());
                AsyncActionResult.success(port, onComplete, false);
                return;
            }
            CrossRedis.expire(redisKey, Time.DAY_7_SEC);
            AsyncActionResult.success(port, onComplete, true);
        });

        try {
            CrossManager.getInstance().callCrossFunc(CrossType.cross_arena_region_rank, Config.SERVER_ID, res -> {
                try {
                    if(res.failed()){
                        Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                        return;
                    }
                    CrossPoint result = res.result();
                    String worldNodeId = result.getNodeId();
                    RemoteNode rn = Port.getCurrent().getNode().getRemoteNode(worldNodeId);
                    if(rn == null) {
                        Log.crossWar.warn("===ArenaRankedServiceProxy updateHumanBrief. worldNodeId={}未连接", worldNodeId);
                        return;
                    }
                    ArenaRankedServiceProxy proxy = ArenaRankedServiceProxy.newInstance(worldNodeId, D.SERV_ARENA_RANKED);
                    proxy.updateHumanBrief(brief);
                } catch (Exception e) {
                    Log.temp.error("==跨服链接出问题 ", e);
                }
            });
        } catch (Exception e) {
            Log.temp.error("==跨服链接出问题 ", e);
        }
    }



    /**
     * <AUTHOR>
     * @Date 2023/6/15
     * @Param
     */
    private void sendSCClientSetInfo(HumanObject humanObj) {
//		MsgCommon.SCClientSetInfo.Builder msg = MsgCommon.SCClientSetInfo.newBuilder();
//		msg.setClientJSON(humanObj.operation.clientInfo.getClientJSON());
//		humanObj.sendMsg(msg);
    }

    /**
     *
     */
    public void sendServerTimeToClientSync(HumanObject humanObj) {
//		MsgCommon.SCServerTime.Builder msg = MsgCommon.SCServerTime.newBuilder();
//		msg.setServerTime(Port.getTime());
//		humanObj.sendMsg(msg);
    }

    /**
     * ios切出保留玩家在线数据5分钟
     *
     * <AUTHOR>
     * @Date 2023/7/19
     * @Param
     */
    public void onCSIOSQuit(HumanObject humanObj) {
        Log.temp.info("===ios切出");
        boolean isAnew = false;
        if (humanObj.iosQuitTime == 0 && humanObj.isLine()) {
            isAnew = true;
        }
        humanObj.iosQuitTime = Port.getTime();
        if (isAnew) {
            // 从新算一次延迟下线时间
            humanObj.connDelayCloseClear();
        }

    }

    @Listener(EventKey.HUMAN_CHANGE_NAME)
    public void onHumanReName(Param param) {
        HumanObject humanObj = param.get("humanObj");
//        syncHumanGlobal(humanObj);
        checkCrossSyns(humanObj, HumanBrief.K.name);
    }

    /**
     * 玩家主动退出游戏
     *
     * <AUTHOR>
     * @Date 2023/9/25
     * @Param
     */
    public void onCSHumanLogout(HumanObject humanObj, boolean isKick) {

        Log.temp.info("主动退出humanId={}", humanObj.id);
        if (!isKick) {
            humanObj.connCloseClear();
        }

        // 关闭连接
        ConnectionProxy connPrxOld = ConnectionProxy.newInstance(humanObj.connPoint);
        connPrxOld.close();
    }

    /**
     * 处理角色职业形象
     */
    public void role_job_figure_c2s(HumanObject humanObj, int job) {
        ConfJobs confReq = ConfJobs.get(job);
        if (confReq == null) {
            return;
        }

        ConfJobs confJob = ConfJobs.get(humanObj.operation.profession.getJobSn());
        if (confJob == null || job == humanObj.getHuman().getJobModel()) {
            return;
        }
        List<Integer> jobList = new ArrayList<>();
        jobList.add(confJob.sn);
        while (confJob != null && confJob.front_job != 0) {
            jobList.add(confJob.front_job);
            confJob = ConfJobs.get(confJob.front_job);
        }

        if (!jobList.contains(job)) {
            return;
        }

        humanObj.getHuman().setJobModel(job);

        MsgRole.role_info_change_s2c.Builder msg = MsgRole.role_info_change_s2c.newBuilder();
        Define.p_role_change.Builder dChange = Define.p_role_change.newBuilder();
        dChange.addKv(to_p_key_value(humanObj, RoleInfoKey.ROLE_ATTR_STYLE.getKey()));
        msg.setChangeList(dChange);
        humanObj.sendMsg(msg);
    }

    /**
     * 头衔数据
     *
     * @Param
     */
    public void adventure_title_info_c2s(HumanObject humanObj) {
        MsgAdventureTitle.adventure_title_info_s2c.Builder msg = MsgAdventureTitle.adventure_title_info_s2c.newBuilder();
        msg.setLevel(humanObj.getHumanExtInfo().getAdventureTitleLv());
        humanObj.sendMsg(msg);
    }

    /**
     * 头衔升级
     *
     * @Param
     */
    public void adventure_title_level_up_c2s(HumanObject humanObj) {
        if (humanObj.operation.taskData.submitAllAdventureTask(humanObj) != true) {
            return;
        }
        int newLv = ConfAdventureLevel.get(humanObj.getHumanExtInfo().getAdventureTitleLv()).next_id;
        if(newLv == 0){
            return;
        }
        humanObj.getHumanExtInfo().setAdventureTitleLv(newLv);
        humanObj.operation.taskData.updatePorpCalc(humanObj);
        int[] taskGroup = ConfAdventureLevel.get(newLv).taskGroup;
        if(taskGroup != null && taskGroup.length > 0){
            humanObj.operation.taskData.acceptAdventureTask(humanObj, taskGroup);
        }
        MsgAdventureTitle.adventure_title_level_up_s2c.Builder msg = MsgAdventureTitle.adventure_title_level_up_s2c.newBuilder();
        msg.setLevel(newLv);
        humanObj.sendMsg(msg);

        ConfAdventureLevel conf = ConfAdventureLevel.get(newLv);
        if(conf != null && conf.avatarFrame != null && conf.avatarFrame.length > 0){
           ProduceManager.inst().produceAdd(humanObj, conf.avatarFrame, MoneyItemLogKey.冒险任务);
           InstanceManager.inst().sendMsg_goods_show_s2c(humanObj,InstanceConstants.showType_0,conf.avatarFrame);
        }
    }

    public void handleRoleSkinListC2S(HumanObject humanObj) {
        List<SkinVo> skinVoList = SkinVo.fromJSONArrayStrToList(humanObj.getHumanExtInfo().getSkinList());
        MsgRole.role_skin_list_s2c.Builder msg = MsgRole.role_skin_list_s2c.newBuilder();
        List<Integer> snList = GlobalConfVal.getSkinTypeOneSnASCList();
        if(snList != null && !snList.isEmpty()) {
            for (int confSkin : snList) {
                Define.p_role_skin_info.Builder builder = Define.p_role_skin_info.newBuilder();
                builder.setCfgId(confSkin);
                builder.setType(1);
                builder.setLevel(0);
                msg.addSkinList(builder);
            }
        }
        for (SkinVo skinVo : skinVoList) {
            msg.addSkinList(skinVo.build());
        }
        humanObj.sendMsg(msg);

    }
    public void role_change_skin_c2s(HumanObject humanObj, int id) {
        ConfSkin confSkin = ConfSkin.get(id);
        if(confSkin == null){
            Log.game.error("===皮肤不存在ConfSkin，skinId={}", id);
            return;
        }
        if(confSkin.type == 1){
            //发色
            if(humanObj.getHuman2().getHairColor() == id){
                return;
            }
            humanObj.getHuman2().setHairColor(id);
            MsgRole.role_change_skin_s2c.Builder msg = MsgRole.role_change_skin_s2c.newBuilder();
            msg.setFigure(to_p_key_value(1, id));
            humanObj.sendMsg(msg);
            HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure);
            return;
        }
        //当前已经是这个皮肤
        if(humanObj.getHuman2().getCurrentSkin() == id){
            return;
        }
        List<SkinVo> skinVoList = SkinVo.fromJSONArrayStrToList(humanObj.getHumanExtInfo().getSkinList());
        for (SkinVo skinVo : skinVoList) {
            if (skinVo.sn == id) {
                humanObj.getHuman2().setCurrentSkin(id);
                MsgRole.role_change_skin_s2c.Builder msg = MsgRole.role_change_skin_s2c.newBuilder();
                msg.setFigure(to_p_key_value(2, id));
                humanObj.sendMsg(msg);

                GuildManager.inst()._msg_guild_area_enter_c2s(humanObj, null, 1);
                HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure);
                return;
            }
        }
    }

    public void handleRoleUnlockSkinC2S(HumanObject humanObj, int skinId) {
        ConfFashionSkin_0 confFashionSkin0 = ConfFashionSkin_0.get(skinId,0);
        if(confFashionSkin0 == null){
            Log.game.error("===皮肤不存在ConfFashionSkin_0，skinId={}", skinId);
            return;
        }
        ConfSkin confSkin = ConfSkin.get(skinId);
        if(confSkin == null){
            Log.game.error("===皮肤不存在ConfSkin，skinId={}", skinId);
            return;
        }
        List<SkinVo> skinVoList = SkinVo.fromJSONArrayStrToList(humanObj.getHumanExtInfo().getSkinList());
        boolean isUnlock = false;
        for (SkinVo skinVo : skinVoList) {
            if (skinVo.sn == skinId) {
                isUnlock = true;
                break;
            }
        }
        if (isUnlock) {
            return;
        }

        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj,confFashionSkin0.expend[0],confFashionSkin0.expend[1],MoneyItemLogKey.皮肤解锁);
        if(!result.success){
            return;
        }
        skinVoList.add(new SkinVo(skinId,confSkin.type));

        humanObj.getHumanExtInfo().setSkinList(SkinVo.listToJSONArrayStr(skinVoList));
        humanObj.getHuman2().setCurrentSkin(skinId);
        MsgRole.role_change_skin_s2c.Builder msg = MsgRole.role_change_skin_s2c.newBuilder();
        Define.p_key_value.Builder kv = Define.p_key_value.newBuilder();
        kv.setK(2);
        kv.setV(skinId);
        msg.setFigure(kv);
        humanObj.sendMsg(msg);
        updateSkinPorpCalcPower(humanObj);
        HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_118);
        MallManager.inst().pushLimitGift26FashionSkin(humanObj,skinId,1);
        // 重新计算美观值
        CharmManager.reCalcCharmValue(humanObj, true);
    }

    public void handleRoleSkinUpgradeLvC2S(HumanObject humanObj, int skinId) {
        ConfSkin confSkin = ConfSkin.get(skinId);
        if(confSkin == null){
            Log.game.error("===皮肤不存在ConfSkin，skinId={}", skinId);
            return;
        }
        List<SkinVo> skinVoList = SkinVo.fromJSONArrayStrToList(humanObj.getHumanExtInfo().getSkinList());
        SkinVo skinVo = null;
        for (SkinVo vo : skinVoList) {
            if (vo.sn == skinId) {
                skinVo = vo;
                break;
            }
        }
        if (skinVo == null) {
            Log.game.error("===皮肤不存在，skinId={}", skinId);
            return;
        }
        ConfFashionSkin_0 confFashionSkin0 = ConfFashionSkin_0.get(skinId, skinVo.level);
        if(confFashionSkin0 == null || confFashionSkin0.expend == null || confFashionSkin0.expend.length < 2){
            return;
        }
        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj,confFashionSkin0.expend[0],confFashionSkin0.expend[1],MoneyItemLogKey.皮肤升级);
        if(!result.success){
            return;
        }
        skinVo.level++;
        humanObj.getHumanExtInfo().setSkinList(SkinVo.listToJSONArrayStr(skinVoList));
        MsgRole.role_skin_upgrade_lv_s2c.Builder msg2 = MsgRole.role_skin_upgrade_lv_s2c.newBuilder();
        msg2.setSkinId(skinId);
        msg2.setSkinLv(skinVo.level);
        humanObj.sendMsg(msg2);
        updateSkinPorpCalcPower(humanObj);
        MallManager.inst().pushLimitGift26FashionSkin(humanObj,skinId,skinVo.level);
        // 重新计算美观值
        CharmManager.reCalcCharmValue(humanObj, true);
    }

    public void updateSkinPorpCalcPower(HumanObject humanObj){
        PropCalc propCalc = new PropCalc();
        int power = 0;
        List<SkinVo> skinVoList = SkinVo.fromJSONArrayStrToList(humanObj.getHumanExtInfo().getSkinList());
        for (SkinVo skinVo : skinVoList) {
            ConfFashionSkin_0 confFashionSkin0 = ConfFashionSkin_0.get(skinVo.sn,skinVo.level);
            if(confFashionSkin0 == null || confFashionSkin0.attr == null || confFashionSkin0.attr.length < 2){
                continue;
            }
            propCalc.plus(confFashionSkin0.attr[0], BigDecimal.valueOf(confFashionSkin0.attr[1]));
            power += confFashionSkin0.power;
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.skin, propCalc.toJSONStr());
        HumanManager.inst().updatePowerPar(humanObj, EModule.SKIN, power);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.神器);
    }


    /**
     * 处理角色方案信息
     *
     */
    public void handleRolePlanInfoC2S(HumanObject humanObj) {
        MsgRole.role_plan_info_s2c.Builder msg = MsgRole.role_plan_info_s2c.newBuilder();
        msg.setPlanId(humanObj.getHumanExtInfo().getPlan());
        for (Map.Entry<Integer, PlanVo> entry : humanObj.operation.planVoMap.entrySet()) {
            msg.addPlanList(entry.getValue().build());
        }
        humanObj.sendMsg(msg);
    }

    /**
     * 处理角色选择方案
     */
    public void handleRoleChoosePlanC2S(HumanObject humanObj, int planId) {
        if(!humanObj.operation.planVoMap.containsKey(planId)){
            return;
        }
        humanObj.getHumanExtInfo().setPlan(planId);
        int oldFlyPetLineup = humanObj.getHuman2().getUseFlyPetLineup();// 取出之前用的飞宠行装id
        humanObj.getHuman2().setUseFlyPetLineup(planId);// 飞宠的方案等于行装方案
        PlanVo planVo = humanObj.operation.planVoMap.get(planId);
        for(int i = 0; i < PlanVo.TAB_NUM; i++){
            setTab(humanObj, i+1, planVo.getTabValue(i+1));
        }
        MsgRole.role_choose_plan_s2c.Builder msg = MsgRole.role_choose_plan_s2c.newBuilder();
        msg.setPlanId(planId);
        humanObj.sendMsg(msg);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_冒险任务, TaskConditionTypeKey.TASK_TYPE_7);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_7);
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_7, 0);

        FlyPetManager.inst().changeFlyPetLineupUse(humanObj, oldFlyPetLineup, planId);
    }

    public void setTab(HumanObject humanObj, int type, long tab) {
        switch (type) {
            case PlanVo.TAB_EQUIP:
                EquipManager.inst().equip_choose_tab_c2s(humanObj, (int) tab);
                break;
            case PlanVo.TAB_SKILL:
                SkillManager.inst()._msg_skill_choose_tab_c2s(humanObj, (int) tab);
                break;
            case PlanVo.TAB_PET:
                PetManager.inst()._msg_pet_choose_tab_c2s(humanObj, (int) tab);
                break;
            case PlanVo.TAB_STATUE:
                HomeManager.inst().handleFarmStatueTabUseC2S(humanObj, (int) tab);
                break;
            case PlanVo.TAB_RELIC:
                RelicManager.inst().handleRelicChooseTabC2S(humanObj, (int) tab);
                break;
            case PlanVo.TAB_WING:
                WingManager.inst().changeWingSkillUse(humanObj, (int) tab);
                break;
            case PlanVo.TAB_MOUNT:
                MountManager.inst().changeMountUseSkill(humanObj, (int) tab);
                break;
            case PlanVo.TAB_ARTF:
                ArtifactManager.inst().changeArtifactUseSkill(humanObj, (int) tab);
                break;
            case PlanVo.TAB_WING_TALENT:
                WingManager.inst().handleWingTalentChooseTabC2S(humanObj, (int) tab);
                break;
            case PlanVo.TAB_ANGEL:
                AngelManager.inst().changeAngelUseTab(humanObj, (int) tab);
                break;
            case PlanVo.TAB_FLYPET:
                FlyPetManager.inst().on_fly_pet_fight_c2s(humanObj, tab);
                break;
            default:
            // Default case if none of the cases match
             break;
        }

    }

    public void setPlanTab(HumanObject humanObj, int type, int tab) {
        setPlanTab(humanObj, type, (long) tab);
    }

    public void setPlanTab(HumanObject humanObj, int type, long tab) {
        PlanVo planVo = humanObj.operation.planVoMap.get(humanObj.getHumanExtInfo().getPlan());
        if(planVo == null){
            Log.game.error("===方案不存在，humanId={}, planId={}", humanObj.id, humanObj.getHumanExtInfo().getPlan());
            return;
        }
        planVo.setTab(type, tab);
        humanObj.getHumanExtInfo().setPlanMap(PlanVo.mapToString(humanObj.operation.planVoMap));
        MsgRole.role_update_plan_s2c.Builder msg = MsgRole.role_update_plan_s2c.newBuilder();
        Define.p_plan_detail.Builder detail = Define.p_plan_detail.newBuilder();
        detail.setPlanId(humanObj.getHumanExtInfo().getPlan());
        Define.p_key_value.Builder kv = Define.p_key_value.newBuilder();
        kv.setK(type);
        kv.setV(tab);
        detail.setName("");// TODO 方案名称必填
        detail.addDetail(kv);
        msg.setNewPlan(detail);
        humanObj.sendMsg(msg);
    }

    public void setMountAtfWingTabSkill(HumanObject humanObj, int index, int skill) {
        int tab = humanObj.getHumanExtInfo().getMountAtfWingTab();
        Map<Integer,Map<Integer,Integer>> map = Utils.jsonToIntMapIntInt(humanObj.getHumanExtInfo().getMountAtfWingTabMap());

        Map<Integer,Integer> tabMap = map.get(tab);
        if(tabMap != null && tabMap.getOrDefault(index, 1) == skill){
            return;
        }
        if(tabMap == null){
            tabMap = new HashMap<>();
            map.put(tab, tabMap);
        }
        tabMap.put(index, skill);
        humanObj.getHumanExtInfo().setMountAtfWingTabMap(Utils.mapIntMapIntIntToJSON(map));
    }

    /**
     * 处理角色修改方案名称
     *
     */
    public void handleRoleChangePlanNameC2S(HumanObject humanObj, int planId, String name) {
        if (name == null || name.length() > MAX_PLAN_NAME_LEN) {
            return;
        }

        if (humanObj.operation.planVoMap.containsKey(planId)) {
            PlanVo planVo = humanObj.operation.planVoMap.get(planId);
            planVo.setName(name);
            humanObj.getHumanExtInfo().setPlanMap(PlanVo.mapToString(humanObj.operation.planVoMap));
            MsgRole.role_change_plan_name_s2c.Builder msg = MsgRole.role_change_plan_name_s2c.newBuilder();
            msg.setPlanId(planId);
            msg.setName(name);
            humanObj.sendMsg(msg);
        }
    }

    /**
     * 处理角色更新方案
     *
     */
    public void handleRoleUpdatePlanC2S(HumanObject humanObj, int planId, List<Define.p_key_value> updateDetailList) {
        if (!humanObj.operation.planVoMap.containsKey(planId)) {
            return;
        }

        Define.p_plan_detail.Builder detail = Define.p_plan_detail.newBuilder();
        PlanVo planVo = humanObj.operation.planVoMap.get(planId);
        for (Define.p_key_value kv : updateDetailList) {
            int key = (int) kv.getK();
            long value = kv.getV();
            if (key > PlanVo.TAB_NUM) {
                continue;
            }
            planVo.setTab(key, value);

            Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
            kvBuilder.setK(key);
            kvBuilder.setV(value);
            detail.addDetail(kvBuilder);
            if (key == PlanVo.TAB_FLYPET && humanObj.getHumanExtInfo().getPlan() != planId) {
                FlyPetManager.inst().changeFlyPetLineup(humanObj, planId, value);
            }
        }
        humanObj.getHumanExtInfo().setPlanMap(PlanVo.mapToString(humanObj.operation.planVoMap));

        if(humanObj.getHumanExtInfo().getPlan() == planId){
            for(int i = 0; i < PlanVo.TAB_NUM; i++){
                setTab(humanObj, i+1, planVo.getTabValue(i+1));
            }
        }

        MsgRole.role_update_plan_s2c.Builder msg = MsgRole.role_update_plan_s2c.newBuilder();
        detail.setPlanId(planId);
        detail.setName(planVo.name);
        msg.setNewPlan(detail);
        humanObj.sendMsg(msg);
        if(planId == 0){
            DoubleChapter dc = humanObj.operation.doubleChapter;
            if(dc != null){
                dc.setBattleRole(humanObj.to_p_battle_role(0).toByteArray());
            }
        }else {
            syncHumanBrief(getHumanBrief(humanObj, true));
        }
    }

    public void initPlanTab(HumanObject humanObj, int tab) {
        humanObj.operation.planVoMap = PlanVo.stringToMap(humanObj.getHumanExtInfo().getPlanMap());
        for (PlanVo planVo : humanObj.operation.planVoMap.values()) {
            planVo.setTab(tab, 1);
        }
        humanObj.getHumanExtInfo().setPlanMap(PlanVo.mapToString(humanObj.operation.planVoMap));
    }

    /**
     * 处理角色设置默认方案
     *
     */
    public void handleRoleDefaultPlanInfoC2S(HumanObject humanObj) {
        MsgRole.role_default_plan_info_s2c.Builder msg = MsgRole.role_default_plan_info_s2c.newBuilder();
        int arr[] = Utils.arrayStrToInt(humanObj.getHumanExtInfo().getDefaultPlanArr());
        for (int i = 0; i < arr.length; i++) {
            Define.p_key_value.Builder kv = Define.p_key_value.newBuilder();
            kv.setK(i + 1);
            kv.setV(arr[i]);
            msg.addDefaultPlans(kv);
        }
        humanObj.sendMsg(msg);
    }

    /**
     * 处理角色更改默认计划
     */
    public void handleRoleChangeDefaultPlanC2S(HumanObject humanObj, List<Define.p_key_value> changeListList) {
        int arr[] = Utils.arrayStrToInt(humanObj.getHumanExtInfo().getDefaultPlanArr());
        List<Integer> upTypeList = new ArrayList<>();
        for (Define.p_key_value kv : changeListList) {
            int key = (int) kv.getK();
            int value = (int) kv.getV();
            if (key > arr.length) {
                continue;
            }
            arr[key - 1] = value;
            upTypeList.add(key);
        }
        humanObj.getHumanExtInfo().setDefaultPlanArr(Utils.arrayIntToStr(arr));
        handleRoleDefaultPlanInfoC2S(humanObj);

        getHumanBrief(humanObj, true);
        if (upTypeList.contains(HumanManager.PLAN_TYPE_GUILD_BOSS)) {
            // 修改了公会boss行装，处理公会boss缓存数据
            List<Long> humanIdList = new ArrayList<>();
            humanIdList.add(humanObj.id);
            HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
            prx.eventFire(humanIdList, EventKey.GUILD_GVE_CACHE_HUMANDATA, new Param());
        }
    }

    public int getPlanType(String planArrStr, int type){
        int arr[] = Utils.arrayStrToInt(planArrStr);
        int planType = 1;
        int index = type - 1;
        if(arr != null && arr.length > index && index >= 0){
            planType = arr[index];
        }
        return planType;
    }

    public void handleRoleQuickSetDoubleChapterPlanC2S(HumanObject humanObj) {
        MsgRole.role_quick_set_double_chapter_plan_s2c.Builder msg = MsgRole.role_quick_set_double_chapter_plan_s2c.newBuilder();
        if(humanObj.operation.doubleChapter == null){
            msg.setCode(1);
            humanObj.sendMsg(msg);
            return;
        }

        msg.setCode(0);
        int currentPlanId = humanObj.getHumanExtInfo().getPlan();
        PlanVo defaultPlanVo = humanObj.operation.planVoMap.getOrDefault(currentPlanId, new PlanVo(0));
        humanObj.operation.planVoMap.put(0, new PlanVo(0,defaultPlanVo));
        humanObj.getHumanExtInfo().setPlanMap(PlanVo.mapToString(humanObj.operation.planVoMap));
        humanObj.sendMsg(msg);
        HumanManager.inst().handleRolePlanInfoC2S(humanObj);
    }

    /**
     * 处理角色通用加速
     */
    public void handleRoleCommonSpeedC2S(HumanObject humanObj, int type, int goodsId, int useNum, int args1) {
        String confStr = ConfGlobal.get("accelerate_goods_effect").strValue;
        String[] confArr = confStr.split("\\|");
        //加速道具id
        int cfgGoodsId = Utils.intValue(confArr[0].split(",")[0]);
        //单个道具加速时间
        int speedUpTime = Utils.intValue(confArr[0].split(",")[1]);
        if(cfgGoodsId != goodsId){
            speedUpTime = Utils.intValue(confArr[1].split(",")[1]);
        }

        // 计算实际需要加速的时间
        if(type == SPEED_UP_FARM){
            int finalSpeedUpTime = speedUpTime;
            EntityManager.getEntityAsync(Farm.class, humanObj.id, res -> {
                if(res == null){
                    Log.game.error("farmBuildingSpeedUp:farm is load fail humanId={}",humanObj.id);
                    return;
                }
                Farm farm = res.result();
                if(farm == null){
                    Log.game.error("farmBuildingSpeedUp:farm is null humanId={}",humanObj.id);
                    return;
                }
                Map<Integer, BuildVo> buildVoMap = BuildVo.mapFromJsonString(farm.getBuildMap());
                BuildVo buildVo = buildVoMap.getOrDefault(args1, new BuildVo());
                if(buildVo.endTime == 0){
                    return;
                }
                int needSpeedUpTime = buildVo.endTime - (int)(Port.getTime()/Time.SEC) - buildVo.accTime;
                handleSpeedUp(humanObj, type, goodsId, useNum, args1, cfgGoodsId, finalSpeedUpTime, needSpeedUpTime);
            });
            return;
        }

        // 计算其他类型的加速时间
        int needSpeedUpTime = 0;
        if(type == SPEED_UP_BOX){
            if (humanObj.getHumanExtInfo().getEquipBoxETime() == 0) {
                return;
            }
            needSpeedUpTime = (int)(humanObj.getHumanExtInfo().getEquipBoxETime()/Time.SEC - (Port.getTime()/Time.SEC));
        }else if(type == SPEED_UP_SCIENCE){
            Mine mine = humanObj.operation.mine;
            if(mine == null){
                Log.game.error("scienceSpeedUp:mine is null humanId={}",humanObj.id);
                return;
            }
            if(mine.getScienceUpgrading() == 0){
                return;
            }
            needSpeedUpTime = mine.getScienceEndTime() - (int)(Port.getTime()/Time.SEC);
        }

        handleSpeedUp(humanObj, type, goodsId, useNum, args1, cfgGoodsId, speedUpTime, needSpeedUpTime);
    }

    /**
     * 处理加速逻辑
     */
    private void handleSpeedUp(HumanObject humanObj, int type, int goodsId, int useNum, int args1,
                               int cfgGoodsId, int speedUpTime, int needSpeedUpTime) {
        if(needSpeedUpTime <= 0) {
            return;
        }

        // 计算实际需要的道具数量
        int actualUseNum = (int)Math.ceil((double)needSpeedUpTime / speedUpTime);
        actualUseNum = Math.min(actualUseNum, useNum);

        if(goodsId != cfgGoodsId){
            //还有加速券不可使用货币加速
            ReasonResult result = ProduceManager.inst().canCostProduce(humanObj, cfgGoodsId, 1);
            if(result.success){
                return;
            }
        }

        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, goodsId, actualUseNum, MoneyItemLogKey.加速);
        if(!result.success){
            return;
        }

        // 计算实际加速时间
        int actualSpeedUpTime = actualUseNum * speedUpTime;

        if(type == SPEED_UP_BOX){
            EquipManager.inst().EquipBoxSpeedUp(humanObj, actualSpeedUpTime);
        }else if(type == SPEED_UP_SCIENCE){
            HomeManager.inst().scienceSpeedUp(humanObj, actualSpeedUpTime);
        }else if(type == SPEED_UP_FARM){
            HomeManager.inst().farmBuildingSpeedUp(humanObj, args1, actualSpeedUpTime);
        }
    }

    public boolean isMeetIllustrated(HumanObject humanObj, int[][] condition) {
        PetData petData = humanObj.operation.petData;
        SkillData skillData = humanObj.operation.skillData;
        for (int i = 0; i < condition.length; i++) {
            int[] arr = condition[i];
            if (arr == null || arr.length < 3) {
                Log.temp.error("===ConfIllustrated_0配表错误, condition={}", Utils.arrayIntToStr(arr));
                continue;
            }
            int type = arr[0];
            int needSn = arr[1];
            int needLv = arr[2];
            if (type == IllustratedType1) {// 技能
                if (!skillData.skillSnLvMap.containsKey(needSn)) {
                    return false;
                }
                int lv = skillData.skillSnLvMap.get(needSn);
                if (needLv > lv) {
                    return false;
                }
            } else if (type == IllustratedType2) {// 同伴
                if (!petData.petSnLvMap.containsKey(needSn)) {
                    return false;
                }
                int lv = petData.petSnLvMap.get(needSn);
                if (needLv > lv) {
                    return false;
                }
            } else {
                Log.temp.error("===未实现代码， type={}", type);
                return false;
            }
        }
        return true;
    }

    public void sendMsg_p_collection_list(HumanObject humanObj){
        MsgCollection.collection_info_s2c.Builder msg = MsgCollection.collection_info_s2c.newBuilder();
        Define.p_collection_list dInfoSkill = to_p_collection_list(humanObj.operation.skillData.skillIllustratedSnLvMap, MallManager.cardPoolType1);
        if(dInfoSkill != null){
            msg.addTypeList(dInfoSkill);
        }
        Define.p_collection_list dInfoPet = to_p_collection_list(humanObj.operation.petData.petIllustratedSnLvMap, MallManager.cardPoolType2);
        if(dInfoPet != null){
            msg.addTypeList(dInfoPet);
        }
        humanObj.sendMsg(msg);
    }

    private Define.p_collection_list to_p_collection_list(Map<Integer, Integer> illustratedSnLvMap, int type) {
        if (!illustratedSnLvMap.isEmpty()) {
            Define.p_collection_list.Builder dInfo = Define.p_collection_list.newBuilder();
            dInfo.setType(type);
            for (Map.Entry<Integer, Integer> entry : illustratedSnLvMap.entrySet()) {
                dInfo.addCollectionList(to_p_collection(entry.getKey(), entry.getValue(), 0));
            }
            return dInfo.build();
        }
        return null;
    }

    public Define.p_collection to_p_collection(int cfgId, int lv, int state){
        Define.p_collection.Builder dInfo = Define.p_collection.newBuilder();
        dInfo.setCfgId(cfgId);
        dInfo.setLv(lv);
        dInfo.setState(state);
        return dInfo.build();
    }

    public void _msg_collection_enhance_c2s(HumanObject humanObj, int cfgId) {
        MsgCollection.collection_enhance_s2c.Builder msg = MsgCollection.collection_enhance_s2c.newBuilder();
        int lvNow =  humanObj.operation.skillData.skillIllustratedSnLvMap.getOrDefault(cfgId, 0);
        ConfIllustrated_0 conf = ConfIllustrated_0.get(cfgId, lvNow);
        if(conf == null){
            Log.temp.error("===ConfIllustrated_0配置不存在, cfgId={}", cfgId);
            humanObj.sendMsg(msg);
            return;
        }
        if(conf.type == MallManager.cardPoolType1){
            int lv =  humanObj.operation.skillData.skillIllustratedSnLvMap.getOrDefault(cfgId, 0);
            boolean isResult = true;
            int loop = 200;
            while(isResult){
                isResult = SkillManager.inst().skillIllustrated(humanObj, cfgId, lv);
                ++lv;
                loop--;
                if(loop <= 0){
                    break;
                }
            }
            msg.setCollection(to_p_collection(cfgId, humanObj.operation.skillData.skillIllustratedSnLvMap.getOrDefault(cfgId, 0), 0));

            humanObj.operation.skillData.saveData(humanObj);
            updateIllustratedPropCalc(humanObj);
        } else if(conf.type == MallManager.cardPoolType2){
            int lv = humanObj.operation.petData.petIllustratedSnLvMap.getOrDefault(cfgId, 0);
            boolean isResult = true;
            int loop = 200;
            while(isResult){
                isResult = PetManager.inst().petIllustrated(humanObj, cfgId, lv);
                ++lv;
                loop--;
                if(loop <= 0){
                    break;
                }
            }
            msg.setCollection(to_p_collection(cfgId, humanObj.operation.petData.petIllustratedSnLvMap.getOrDefault(cfgId, 0), 0));

            humanObj.operation.petData.saveData(humanObj);
            updateIllustratedPropCalc(humanObj);
        } else {
            Log.temp.error("===未实现代码， type={}", conf.type);
        }
        humanObj.sendMsg(msg);

        UnitManager.inst().propCalc(humanObj, CombatChangeLog.图鉴);
    }

    public void updateIllustratedPropCalc(HumanObject humanObj){
        Map<Integer, Integer> skillSnLvMap = humanObj.operation.skillData.skillIllustratedSnLvMap;
        PropCalc propCalc = new PropCalc();
        long skillPower = 0;
        for (Map.Entry<Integer, Integer> entry : skillSnLvMap.entrySet()) {
            ConfIllustrated_0 conf = ConfIllustrated_0.get(entry.getKey(), entry.getValue());
            if (conf == null) {
                continue;
            }
            skillPower += conf.power;
            if(conf.attr != null){
                for(int i = 0; i < conf.attr.length; i+=2){
                    propCalc.plus(conf.attr[i], BigDecimal.valueOf(conf.attr[i+1]));
                }
            }
        }
        HumanManager.inst().updatePowerPar(humanObj, EModule.SkillIllustrated, skillPower);

        int petPower = 0;
        Map<Integer, Integer> petIllustratedSnLvMap = humanObj.operation.petData.petIllustratedSnLvMap;
        for (Map.Entry<Integer, Integer> entry : petIllustratedSnLvMap.entrySet()) {
            ConfIllustrated_0 conf = ConfIllustrated_0.get(entry.getKey(), entry.getValue());
            if (conf == null) {
                continue;
            }
            petPower += conf.power;
            if(conf.attr != null){
                for(int i = 0; i < conf.attr.length; i+=2){
                    propCalc.plus(conf.attr[i], BigDecimal.valueOf(conf.attr[i+1]));
                }
            }

        }
        HumanManager.inst().updatePowerPar(humanObj, EModule.PetIllustrated, petPower);

        int powerLv = 0;
        Map<Integer, Integer> petSnLvMap = humanObj.operation.petData.petSnLvMap;
        for (Map.Entry<Integer, Integer> entry : petSnLvMap.entrySet()) {
            ConfPetlevel_0 conf = ConfPetlevel_0.get(entry.getKey(), entry.getValue());
            if (conf == null) {
                continue;
            }
            powerLv += conf.power;
        }
        HumanManager.inst().updatePowerPar(humanObj, EModule.PetLv, powerLv);

        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.illustrated, propCalc.toJSONStr());

    }

    /**
     * 处理角色功能预告奖励
     */
    public void handleRolePreviewRewardC2S(HumanObject humanObj, int cfgId) {
        ConfPreFunc confPreFunc = ConfPreFunc.get(cfgId);
        if(confPreFunc == null){
            return;
        }
        List<Integer> rewardList = humanObj.operation.taskData.getFinishPreFuncList();
        if(rewardList.contains(cfgId)){
            return;
        }
        if(!humanObj.operation.taskData.canRewardPreFuncTask(humanObj, confPreFunc.condition)){
            return;
        }
        rewardList.add(cfgId);
        humanObj.operation.taskData.setFinishPreFuncTasks(rewardList);
        ProduceManager.inst().produceAdd(humanObj, confPreFunc.reward[0], confPreFunc.reward[1], MoneyItemLogKey.功能预告奖励);
        MsgRole.role_preview_reward_s2c.Builder msg = MsgRole.role_preview_reward_s2c.newBuilder();
        Define.p_preview.Builder preview = Define.p_preview.newBuilder();
        preview.setConfigId(cfgId);
        preview.setState(TaskConditionTypeKey.PREVIEW_STATUS_已领奖);
        msg.addPreviewInfo(preview);
        humanObj.sendMsg(msg);
        List<Define.p_reward> p_rewards = InstanceManager.inst().to_p_rewardList(confPreFunc.reward);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, 0, p_rewards);

        Human3 human = humanObj.getHuman3();
        human.setPreviewNum(human.getPreviewNum() + 1);
        human.update();

        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_27, 1);
    }

    //====================================================================================================

    /**
     * 发送登录初始化信息至客户端
     */
    public void sendInitDataToClient(HumanObject humanObj, boolean isLogin) {
        HumanManager.inst().syncHumanGlobal(humanObj);
        // 玩家基础信息
        humanObj.isClientStageReady = true;
        // TODO 登录后通知消息
        if (isLogin) {
            humanObj.isLoginOk = true;
            sendMsg_role_login_s2c(humanObj);
        }
        humanObj.funcOpenList = getFuncOpenList(humanObj);
        sendMsg_role_info_c2s(humanObj);
        sendMsg_role_guide_s2c(humanObj);// 强制引导

        ArtifactManager.inst().handleArtifactInfoC2S(humanObj);
        FateManager.inst().handleFateInfoC2S(humanObj);
        sendMsg_goods_info_s2c(humanObj);
        AngelManager.inst().handleAngelStarArrayC2S(humanObj);
        InstanceManager.inst().sendMsg_dungeon_type_s2c(humanObj, InstanceConstants.CHAPTER_1);
        InstanceManager.inst().sendMsg_main_chapter_result_s2c(humanObj, 0);
        sendMsg_role_set_setting_s2c(humanObj);
        TaskManager.inst().sendMsg_task_all_s2c(humanObj);
        // 通知成就统计信息
        TaskManager.inst().sendMsg_task_achievement_s2c(humanObj);
        sendMsg_role_total_sp_info_s2c(humanObj, 0);
        PetManager.inst().sendMsg_pet_list_s2c(humanObj);
        TaskManager.inst().sendMsg_task_daily_point_s2c(humanObj);
        MallManager.inst().sendMsg_pay_mall_info_s2c(humanObj);
        MallManager.inst()._msg_shop_info_c2s(humanObj, MallManager.MallType_Gem);
        // 基本信息
        SkillManager.inst()._msg_skill_list_c2s(humanObj);
        sendMsg_role_power_info_s2c(humanObj);

        sendMsg_role_setting_info_s2c(humanObj);

        sendMsg_role_used_skin_list_s2c(humanObj);


        sendMsg_role_guide_save_s2c(humanObj);
        sendMsg_role_preview_s2c(humanObj);
        sendMsg_role_show_control_s2c(humanObj);
        sendMsg_role_red_point_s2c(humanObj);
        sendMsg_adventure_title_info_s2c(humanObj);

        EquipManager.inst().equip_tab_info_c2s(humanObj);
        EquipManager.inst().equip_info_c2s(humanObj);
        EquipManager.inst().equip_box_info_c2s(humanObj);
        EquipManager.inst().equip_book_list_c2s(humanObj);
        EquipManager.inst().equip_box_skin_c2s(humanObj);
        HumanManager.inst().sendMsg_dungeon_list_s2c(humanObj);
        MallManager.inst().sendMsg_draw_info_s2c(humanObj);
        SkillManager.inst()._msg_skill_system_info_c2s(humanObj);
        HomeManager.inst().handleScienceInfoC2S(humanObj);
        HomeManager.inst().handleMineInfoC2S(humanObj);
//        SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj);
        sendMsg_client_data_s2c(humanObj);
        EquipManager.inst().equip_book_list_c2s(humanObj);
        EquipManager.inst().equip_figure_list_c2s(humanObj);
        role_used_skin_list_c2s(humanObj);
        JobsManager.inst()._msg_jobs_wakeup_info_c2s(humanObj);
        InstanceManager.inst()._msg_dungeon_world_boss_info_c2s(humanObj);
        MailManager.inst()._msg_mail_list_c2s(humanObj, 0);
        InformManager.inst()._msg_chat_friend_list_c2s(humanObj);
        GuildManager.inst().sendMsg_gvg_info_s2c(humanObj);
        GuildManager.inst()._msg_guild_info_c2s(humanObj, humanObj.getHuman2().getGuildId());
        FriendManager.inst().handleFriendGiftNumC2S(humanObj);
        sendMsg_p_collection_list(humanObj);
        sendMsg_emoji_info_s2c(humanObj);
        GuildManager.inst().sendMsg_guild_schedule_s2c(humanObj);
        GuildManager.inst()._msg_guild_help_status_c2s(humanObj);
        FriendManager.inst().sendFriendIdList(humanObj);
    }

    private void sendMsg_ship_total_sp_info_s2c(HumanObject humanObj) {
        MsgShip.ship_total_sp_info_s2c.Builder msg = MsgShip.ship_total_sp_info_s2c.newBuilder();
        msg.setType(0);

        humanObj.sendMsg(msg);
    }

    public void sendMsg_dungeon_list_s2c(HumanObject humanObj) {
        humanObj.checkOpenGuildBoss();
        long timeReset = Utils.getOffDayTime(Port.getTime(), 1, 0);
        int dayTime = (int) ((timeReset - Port.getTime()) / Time.SEC);
        GameServiceProxy prx = GameServiceProxy.newInstance();
        prx.getServerGlobal(humanObj.getHuman().getServerId());
        prx.listenResult((results, context) -> {
            ReasonResult rr = Utils.getParamValue(results, "result", new ReasonResult(false, 1));
            if (!rr.success) {
                Log.human.error("查询ServerGlobal错误，result={}", rr);
            }
            MsgDungeon.dungeon_list_s2c.Builder msg = MsgDungeon.dungeon_list_s2c.newBuilder();
            ServerGlobal serverGlobal = Utils.getParamValue(results, "serverGlobal", null);
            for (ConfChapterType conf : ConfChapterType.findAll()) {
                int type = conf.sn;
                int curLv = humanObj.operation.getRepMaxDiffculty(type);
                int maxLv = curLv + 1;
                List<Define.p_key_value> extList = HumanManager.inst().getDungeonExtList(humanObj, type);
                if (type == InstanceConstants.LEAGUESOLOCHAPTER_6) {
                    curLv = 1;
                    maxLv = 1;
                }
                if (type == InstanceConstants.DARKTRIALCHAPTER_28 || type == InstanceConstants.DARKTRIALCHAPTER_29 || type == InstanceConstants.DARKTRIALCHAPTER_30) {
                    if (serverGlobal == null || type != serverGlobal.getDarkTrialChapterSn()) {
                        continue;
                    }
                    int dailyMaxLv = humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyDarkTrialMaxLv.getType());
                    maxLv = curLv;
                    curLv = humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyDarkTrialChapter.getType()) + 1; // 今日打的进度
                    curLv = curLv <= 0 ? 1 : curLv;
                    curLv = Math.min(curLv, dailyMaxLv);
                }
                if (type == InstanceConstants.SEVENTRIALCHAPTER_23) {
                    // 23类型跳过
                    continue;
                }
                msg.addDungeonList(to_p_dungeon(type, maxLv, curLv, dayTime, extList));
            }
            if (!ActivityManager.inst().isActivityOpen(humanObj, ActivityControlType.Act_72)) {
                humanObj.sendMsg(msg);
                return;
            }
            InstanceManager.inst().fillDungeonExtList(humanObj, InstanceConstants.SEVENTRIALCHAPTER_23, (extList) -> {
                msg.addDungeonList(to_p_dungeon(InstanceConstants.SEVENTRIALCHAPTER_23, 1, 1, dayTime, extList));
                humanObj.sendMsg(msg);
            });
        });

    }



    private Define.p_dungeon.Builder to_p_dungeon(int type, int maxLevel, int curLevel, int dayTimes, List<Define.p_key_value> extList) {
        Define.p_dungeon.Builder dInfo = Define.p_dungeon.newBuilder();
        dInfo.setType(type);
        int confMaxLv = GlobalConfVal.getRepTypeMaxLv(type);
        if(maxLevel > confMaxLv && confMaxLv != 0){
            maxLevel = confMaxLv;
        }
        dInfo.setMaxLevel(maxLevel);
        dInfo.setCurLevel(curLevel);
        dInfo.setDayTimes(dayTimes);
        dInfo.addAllExt(extList);
        return dInfo;
    }

    public Define.p_reward to_p_reward(int gtid, long num) {
        Define.p_reward.Builder dInfo = Define.p_reward.newBuilder();
        dInfo.setGtid(gtid);
        dInfo.setNum(num);
        return dInfo.build();
    }

    private void sendMsg_adventure_title_info_s2c(HumanObject humanObj) {
        MsgAdventureTitle.adventure_title_info_s2c.Builder msg = MsgAdventureTitle.adventure_title_info_s2c.newBuilder();
        msg.setLevel(0);
        humanObj.sendMsg(msg);
    }

    private void sendMsg_role_show_control_s2c(HumanObject humanObj) {
        MsgRole.role_show_control_s2c.Builder msg = MsgRole.role_show_control_s2c.newBuilder();
        msg.setPlatform("");
        msg.setChannel("");

        humanObj.sendMsg(msg);
    }

    public void sendMsg_role_power_info_s2c(HumanObject humanObj) {
        MsgRole.role_power_info_s2c.Builder msg = MsgRole.role_power_info_s2c.newBuilder();
        Define.p_role_power_info.Builder dInfo = Define.p_role_power_info.newBuilder();
        dInfo.setType(0);
        dInfo.setPower(new BigDecimal(humanObj.getHuman().getCombat()).longValue());
        msg.addPowerInfo(dInfo);
        humanObj.sendMsg(msg);
    }

    private void sendMsg_role_preview_s2c(HumanObject humanObj) {
        humanObj.operation.taskData.sendRole_preview_s2c(humanObj);
    }

    private void sendMsg_role_guide_save_s2c(HumanObject humanObj) {
        MsgRole.role_guide_save_s2c.Builder msg = MsgRole.role_guide_save_s2c.newBuilder();

        humanObj.sendMsg(msg);
    }

    private void sendMsg_role_guide_s2c(HumanObject humanObj) {
        MsgRole.role_guide_s2c.Builder msg = MsgRole.role_guide_s2c.newBuilder();
        msg.setId(humanObj.operation.clientInfo.getGuideId());
        humanObj.sendMsg(msg);
    }
    private void sendMsg_role_used_skin_list_s2c(HumanObject humanObj) {
        MsgRole.role_used_skin_list_s2c.Builder msg = MsgRole.role_used_skin_list_s2c.newBuilder();
//        to_p_key_value(); TODO

        humanObj.sendMsg(msg);
    }

    private void sendMsg_role_setting_info_s2c(HumanObject humanObj) {
        Map<Integer, Long> settingMap = Utils.jsonToMapIntLong(humanObj.getHumanExtInfo().getSettingMap());
        MsgRole.role_setting_info_s2c.Builder msg = MsgRole.role_setting_info_s2c.newBuilder();
        for (Map.Entry<Integer, Long> entry : settingMap.entrySet()) {
            msg.addList(to_p_key_value(entry.getKey(), Utils.intValue(entry.getValue())));
        }
//        for (RoleTypeKey type : RoleTypeKey.values()) {
//            // 部分设置客户端初始化
//            if (settingMap.containsKey(type.getKey())) {
//                msg.addList(to_p_key_value(type.getKey(), Utils.intValue(settingMap.get(type.getKey()))));
//            }
//        }
        humanObj.sendMsg(msg);
    }

    private void sendMsg_client_data_s2c(HumanObject humanObj) {
        MsgRole.client_data_s2c.Builder msg = MsgRole.client_data_s2c.newBuilder();
        JSONObject jo = Utils.toJSONObject(humanObj.operation.clientInfo.getClientJSON());
        for (String key : jo.keySet()) {
            Define.p_client_data.Builder dInfo = Define.p_client_data.newBuilder();
            dInfo.setSysId(Utils.intValue(key));
            JSONObject joInfo = Utils.toJSONObject(jo.getString(key));
            for (String k : joInfo.keySet()) {
                Define.p_key_string3.Builder data = Define.p_key_string3.newBuilder();
                data.setK(k);
                data.setS(joInfo.getString(k));
                dInfo.addData(data);
            }
            msg.addList(dInfo);
        }
        humanObj.sendMsg(msg);
    }

    private void sendMsg_game_centre_reward_info_s2c(HumanObject humanObj) {
        // 游戏中心
        MsgGameCentre.game_centre_reward_info_s2c.Builder msg = MsgGameCentre.game_centre_reward_info_s2c.newBuilder();

    }

    private void sendMsg_role_set_setting_s2c(HumanObject humanObj) {
        MsgRole.role_set_setting_s2c.Builder msg = MsgRole.role_set_setting_s2c.newBuilder();
        msg.setType(RoleTypeKey.INVITED_LIMIT.getKey());
        msg.setValue(0);
        humanObj.sendMsg(msg);
    }

    private void sendMsg_ban_chat_s2c(HumanObject humanObj) {
        // 禁言的玩家
        MsgChat.ban_chat_s2c.Builder msg = MsgChat.ban_chat_s2c.newBuilder();
        Define.p_key_value.Builder dProp = Define.p_key_value.newBuilder();
        dProp.setK(0);// 玩家id
        dProp.setV(0);// 禁言结束时间（秒）
        humanObj.sendMsg(msg);
    }

    private void sendMsg_goods_info_s2c(HumanObject humanObj) {
        MsgGoods.goods_info_s2c.Builder msg = MsgGoods.goods_info_s2c.newBuilder();

        Map<Integer, Integer> snNumMap = humanObj.operation.itemData.getItemSnNumMap();
        for (Map.Entry<Integer, Integer> entry : snNumMap.entrySet()) {
            int itemNum = entry.getValue();
            if (itemNum <= 0) {
                continue;
            }
            int itemSn = entry.getKey();
            if (TokenItemType.isLimit(itemSn)) {
                continue;
            }
            msg.addGoodsList(to_p_goods(itemSn, itemNum, 0));
        }
        for (int moneySn : TokenItemType.getTokenItemValues()) {
            if (TokenItemType.isLimit(moneySn)) {
                continue;
            }
            long itemNum = MoneyManager.inst().getProduceReduce(humanObj, moneySn);
            msg.addGoodsList(to_p_goods(moneySn, (int) itemNum, 0));
            continue;
        }
        humanObj.sendMsg(msg);
    }

    public Define.p_goods to_p_goods(int itemSn, int num, int expireTime) {
        ConfGoods confGoods = ConfGoods.get(itemSn);
        if (confGoods == null) {
            Log.temp.error("===ConfGoods not find sn={}", itemSn);
            return null;
        }
        Define.p_goods.Builder dInfo = Define.p_goods.newBuilder();
        dInfo.setGoodsGtid(itemSn);
        dInfo.setGoodsId(itemSn);
        dInfo.setNum(num);
        dInfo.setType(confGoods.type);
        dInfo.setSubtype(confGoods.subtype);
        dInfo.setQuality(confGoods.quality);
        dInfo.setExpireTime(expireTime);
        return dInfo.build();
    }

    private void sendMsg_role_login_s2c(HumanObject humanObj) {
        Human human = humanObj.getHuman();
        MsgLogin.role_login_s2c.Builder msg = MsgLogin.role_login_s2c.newBuilder();
        msg.setCode(0);
        msg.setRoleId(humanObj.id);
        msg.setServerId(human.getServerId());
        msg.setServTime((int) (Port.getTime() / Time.SEC));
        msg.setRoleName(humanObj.name);
        msg.setServerName(Util.getServerNameReal(human.getServerId()));
        msg.setTimeZone((int)(TimeZone.getDefault().getRawOffset() / Time.HOUR));// 时区
        msg.setOpenTime((int) (Util.getOpenServerTime(human.getServerId()) / Time.SEC));// TODO 开服时间

        Define.p_role_change.Builder dInfo = Define.p_role_change.newBuilder();
        dInfo.addKs(HumanManager.inst().to_p_key_string(RoleInfoKey.ROLE_ATTR_NAME.getKey(), human.getName()));
        dInfo.addKv(HumanManager.inst().to_p_key_value(RoleInfoKey.ROLE_ATTR_LVL.getKey(), human.getLevel()));

        dInfo.addKv(HumanManager.inst().to_p_key_value(0, humanObj.isCreateLogin));
        msg.setInfo(dInfo);
        humanObj.sendMsg(msg);
        humanObj.isCreateLogin = 0;
    }

    public void sendMsg_role_info_c2s(HumanObject humanObj) {
        MsgRole.role_info_s2c.Builder msg = MsgRole.role_info_s2c.newBuilder();
        msg.setRoleInfo(to_p_role_info(humanObj));
        humanObj.sendMsg(msg);
    }

    private Define.p_role_info.Builder to_p_role_info(HumanObject humanObj) {
        Define.p_role_info.Builder dInfo = Define.p_role_info.newBuilder();
        dInfo.addAllFunctionList(humanObj.funcOpenList);
        dInfo.setInfoList(to_p_role_change(humanObj));
        return dInfo;
    }

    private List<Integer> getFuncOpenList(HumanObject humanObj) {
        List<Integer> openList = new ArrayList<>();
        for (ConfNewFuncOpen conf : ConfNewFuncOpen.findAll()) {
            if (humanObj.isModUnlock(conf)) {
                openList.add(conf.sn);
                Event.fire(EventKey.FUNCTION_OPEN_LOGIN, "humanObj", humanObj, "sn", conf.sn);
            }
        }
        return openList;
    }

    public void role_used_skin_list_c2s(HumanObject humanObj) {
        MsgRole.role_used_skin_list_s2c.Builder msg = MsgRole.role_used_skin_list_s2c.newBuilder();
        Define.p_key_value.Builder kv1 = Define.p_key_value.newBuilder();
        kv1.setK(1);
        if(humanObj.getHuman2().getHairColor() == 0){
            List<Integer> snList = GlobalConfVal.getSkinTypeOneSnASCList();
            if(snList != null && !snList.isEmpty()){
                int sn = snList.get(snList.size() - 1);
                kv1.setV(sn);
            }
        }else {
            kv1.setV(humanObj.getHuman2().getHairColor());
        }
        msg.addFigureList(kv1);
        Define.p_key_value.Builder kv2 = Define.p_key_value.newBuilder();
        kv2.setK(2);
        if(humanObj.getHuman2().getCurrentSkin() == 0){
            List<SkinVo> skinVoList1 = SkinVo.fromJSONArrayStrToList(humanObj.getHumanExtInfo().getSkinList());
            kv2.setV(skinVoList1.size() == 0 ? DEFAULT_SKIN_2 : skinVoList1.get(skinVoList1.size() - 1).sn);
        }else {
            kv2.setV(humanObj.getHuman2().getCurrentSkin());
        }
        msg.addFigureList(kv2);
        humanObj.sendMsg(msg);
    }


    @Listener(EventKey.FINISH_TASK)
    public void _on_FINISH_TASK(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
//        int taskSn = Utils.getParamValue(param, "taskSn", 0);
        checkFuncOpen(humanObj);
    }


    /**
     * 检测功能是否有新解锁
     *
     * <AUTHOR>
     * @Date 2024/4/12
     * @Param
     */
    public void checkFuncOpen(HumanObject humanObj) {
        List<Integer> openList = new ArrayList<>();
        for (ConfNewFuncOpen conf : ConfNewFuncOpen.findAll()) {
            if (humanObj.funcOpenList.contains(conf.sn)) {
                continue;
            }
            if (humanObj.isModUnlock(conf)) {
                openList.add(conf.sn);
                humanObj.funcOpenList.add(conf.sn);
            }
        }
        if(openList.isEmpty()){
            return;
        }
        sendMsg_role_open_function_s2c(humanObj, openList);
        long timeOpen = System.nanoTime();
        Event.fire(EventKey.FUNCTION_OPEN, "humanObj", humanObj, "openList", openList);
        long timeEnd = System.nanoTime();
        Log.temp.info("===useTime={}, openTime={}, endTime={}", timeEnd - timeOpen, timeOpen, timeEnd);
    }

    private void checkPlanOpen(HumanObject humanObj){
        int level = humanObj.getHuman().getLevel();
        int maxTab = getPlanLimit(level);
        boolean isUpdate = false;
        for (int tab = 1; tab <= maxTab ; tab++) {
            if (!humanObj.operation.planVoMap.containsKey(tab)){
                humanObj.operation.planVoMap.put(tab, new PlanVo(tab));
                isUpdate = true;
            }
        }
        if (isUpdate) {
            humanObj.getHumanExtInfo().setPlanMap(PlanVo.mapToString(humanObj.operation.planVoMap));
            handleRolePlanInfoC2S(humanObj);
        }
    }

    private int getPlanLimit(int level){
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.suit_tab_limit.SN);
        String[] strArray = confGlobal.strValue.split("\\|");
        int maxTab = 1;
        for(String str : strArray){
            String[] strs = str.split(",");
            if(level >= Utils.intValue(strs[1])){
                maxTab = Utils.intValue(strs[0]);
            }else {
                break;
            }
        }
        return maxTab;
    }

    private void sendMsg_role_open_function_s2c(HumanObject humanObj, List<Integer> funcList) {
        MsgRole.role_open_function_s2c.Builder msg = MsgRole.role_open_function_s2c.newBuilder();
        msg.addAllFuncList(funcList);
        humanObj.sendMsg(msg);
    }

    private Define.p_role_change.Builder to_p_role_change(HumanObject humanObj) {
        Define.p_role_change.Builder dInfo = Define.p_role_change.newBuilder();
        for (RoleInfoKey key : RoleInfoKey.values()) {
            if (key == RoleInfoKey.ROLE_ATTR_POWER_INFO) {
                continue;
            }
            if (key.getKey() == RoleInfoKey.ROLE_ATTR_GUILD_ID.getKey() && humanObj.getHuman2().getGuildId() <= 0) {
                continue;
            }
            if (key.getValueType() == 1) {
                dInfo.addKs(to_p_key_string(humanObj, key.getKey()));
            } else {
                dInfo.addKv(to_p_key_value(humanObj, key.getKey()));
            }
        }
        Map<Integer, Integer> snNumMap = humanObj.operation.itemData.getItemSnNumMap();
        for (Map.Entry<Integer, Integer> entry : snNumMap.entrySet()) {
            int itemNum = entry.getValue();
            if (itemNum <= 0) {
                continue;
            }
            int itemSn = entry.getKey();
            if (!TokenItemType.isLimit(itemSn)) {
                continue;
            }
            dInfo.addKv(to_p_key_value(itemSn, itemNum));
        }
        for (int moneySn : TokenItemType.getTokenItemValues()) {
            if (!TokenItemType.isLimit(moneySn)) {
                continue;
            }
            long itemNum = MoneyManager.inst().getProduceReduce(humanObj, moneySn);
            dInfo.addKv(to_p_key_value(moneySn, itemNum));
            continue;
        }

        return dInfo;
    }

    public Define.p_key_value.Builder to_p_key_value(HumanObject humanObj, int key) {
        Define.p_key_value.Builder dInfo = Define.p_key_value.newBuilder();
        dInfo.setK(key);
        long value = 0;

        if (key == RoleInfoKey.ROLE_ATTR_GOLD.getKey()) {
            value = humanObj.currency.getCoin();
        } else if (key == RoleInfoKey.ROLE_ATTR_DIAMOND.getKey()) {
            value = humanObj.currency.getGold();
        } else if (key == RoleInfoKey.ROLE_RELIC_FRAG.getKey()) {

        } else if (key == RoleInfoKey.ROLE_NUTRIENTS.getKey()) {

        } else if (key == RoleInfoKey.ROLE_BUILDING_POINT.getKey()) {

        } else if (key == RoleInfoKey.ROLE_SKIN_CLIP.getKey()) {
        } else if (key == RoleInfoKey.ROLE_HONOR_SCORE.getKey()) {
        } else if (key == RoleInfoKey.ROLE_RETURN_COIN.getKey()) {
            value = humanObj.operation.itemData.getItemNum(key);
        } else if (key == RoleInfoKey.ROLE_SEED_GET.getKey()) {
        } else if (key == RoleInfoKey.ROLE_SEED_BUY.getKey()) {
        } else if (key == RoleInfoKey.ROLE_SEED_RARE.getKey()) {
        } else if (key == RoleInfoKey.ROLE_SEED_ACT.getKey()) {
        } else if (key == RoleInfoKey.ROLE_FERTILIZER_BUY.getKey()) {
        } else if (key == RoleInfoKey.ROLE_FERTILIZER_GET.getKey()) {
        } else if (key == RoleInfoKey.ROLE_FERTILIZER_HELP.getKey()) {
        } else if (key == RoleInfoKey.ROLE_PARKING_COIN.getKey()) {
        } else if (key == RoleInfoKey.ROLE_CHANGE_POINT.getKey()) {
        } else if (key == RoleInfoKey.ROLE_CHANGE_GUILD.getKey()) {
        } else if (key == RoleInfoKey.ROLE_FAKE_RECHARE.getKey()) {
        } else if (key == RoleInfoKey.ROLE_ATTR_LVL.getKey()) {
            value = humanObj.getHuman().getLevel();
        } else if (key == RoleInfoKey.ROLE_ATTR_EXP.getKey()) {
            value = humanObj.getHuman().getExpCur();
        } else if (key == RoleInfoKey.ROLE_ATTR_ID.getKey()) {
            value = humanObj.id;
        } else if (key == RoleInfoKey.ROLE_ATTR_VIP.getKey()) {
            value = humanObj.getHuman3().getVipLv();
        } else if (key == RoleInfoKey.ROLE_ATTR_SERVER.getKey()) {
            value = humanObj.getHuman().getServerId() % Utils.intValue(Config.GAME_SERVER_PREFIX);
        } else if (key == RoleInfoKey.ROLE_ATTR_CUR_POWER.getKey()) {
            value = new BigDecimal(humanObj.getHuman().getCombat()).longValue();
        } else if (key == RoleInfoKey.ROLE_ATTR_CUR_JOB.getKey()) {
            value = humanObj.operation.profession.getJobSn();
        } else if (key == RoleInfoKey.ROLE_ATTR_JOB_TIMES.getKey()) {
            value = humanObj.operation.profession.getJobChange();
        } else if (key == RoleInfoKey.ROLE_ATTR_STYLE.getKey()) {
            value = humanObj.getHuman().getJobModel();

        } else if (key == RoleInfoKey.ROLE_ATTR_RENAME.getKey()) {
            value = humanObj.getHumanExtInfo().getChangeNameTime();
        } else if (key == RoleInfoKey.ROLE_ATTR_GENDER.getKey()) {
            value = humanObj.getHuman2().getSex();
        } else if (key == RoleInfoKey.ROLE_ATTR_SKIN.getKey()) {
            value = humanObj.getHuman2().getCurrentSkin();
        } else if (key == RoleInfoKey.ROLE_ATTR_HEAD_ID.getKey()) {
            value = humanObj.getHuman().getHeadSn();
        } else if (key == RoleInfoKey.ROLE_ATTR_HEAD_FRAME_ID.getKey()) {
        } else if (key == RoleInfoKey.ROLE_ATTR_GUILD_ID.getKey()) {
            value = humanObj.getHuman2().getGuildId();
        } else if (key == RoleInfoKey.ROLE_ATTR_POWER_SHOW.getKey()) {
            value = new BigDecimal(humanObj.getHuman().getCombat()).longValue();
        } else if (key == RoleInfoKey.ROLE_ATTR_CREATE_DAY.getKey()) {
            value = humanObj.getHuman().getTimeCreate() / Time.SEC;
        } else if(key == RoleInfoKey.ROLE_ATTR_GUILD_LV.getKey()){
            value = humanObj.getHuman2().getGuildLv();
        } else if (key == RoleInfoKey.ROLE_ATTR_GUILD_NUM.getKey()) {
        } else if (key == RoleInfoKey.ROLE_ATTR_LOVER_ID.getKey()) {
        }
        dInfo.setV(value);
        return dInfo;
    }

    /**
     * 把副本消息ext字段的构造抽出来，统一调用，以后要改动，只改这里
     */
    public List<Define.p_key_value> getDungeonExtList(HumanObject humanObj, int type) {
        Human3 human = humanObj.getHuman3();
        List<Define.p_key_value> extList = new ArrayList<>();
        switch (type) {
            case InstanceConstants.COINCHAPTER_2: {
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_9, humanObj.operation.getRepMaxDiffculty(type)).build());
            }
            break;
            case InstanceConstants.DIAMONDCHAPTER_3: {
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_9, humanObj.operation.getRepMaxDiffculty(type)).build());
            }
            break;
            case InstanceConstants.LEAGUESOLOCHAPTER_6: {
                // 家族单人副本
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_1, humanObj.getGuildNextBossTime()).build());
            }
            break;
            case InstanceConstants.LEGACYTEAMCHAPTER_8: {
                // 组队副本
                Map<Integer, Integer> repTypeTenDiffMap = Utils.jsonToMapIntInt(human.getRepTypeTenDiffMap());
                int tenDiff = Utils.intValue(repTypeTenDiffMap.get(type));
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_14, tenDiff).build());
                int day = Utils.getDaysBetween(Port.getTime(), Util.getOpenServerTime(humanObj.getHuman().getServerId())) + 1;
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_5, day).build());
            }
            break;
            case InstanceConstants.MOUNTCHAPTER_9: {
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_9, humanObj.operation.getRepMaxDiffculty(type)).build());
            }
            break;
            case InstanceConstants.FATECHAPTER_11: {
                // 武魂副本
                int val = humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyFateGungeonReward.getType());
                int curLv = humanObj.operation.getRepMaxDiffculty(InstanceConstants.FATECHAPTER_11);
                if (curLv == 0 || val != 1) {
                    val = 0;
                }
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_6, val).build());
            }
            break;
            case InstanceConstants.ARTIFACTGEMCHAPTER_22: {
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_9, humanObj.operation.getRepMaxDiffculty(type)).build());
            }
            break;
            case InstanceConstants.DARKTRIALCHAPTER_28:
            case InstanceConstants.DARKTRIALCHAPTER_29:
            case InstanceConstants.DARKTRIALCHAPTER_30: {
                // 暗黑试炼
                int day = Utils.getDaysBetween(Port.getTime(), Util.getOpenServerTime(humanObj.getHuman().getServerId())) + 1;
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_5, day).build());

//                int lastDailyMaxLv = humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyDarkTrialMaxPassLevel.getType());
                int dailyMaxLv = humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyDarkTrialMaxLv.getType());
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_6, dailyMaxLv).build());

                int curLv = humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyDarkTrialChapter.getType()); // 今日打的进度
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_10, dailyMaxLv > curLv ? 0 : 1).build());

                int useNum = humanObj.getDailyResetTypeValue(DailyResetTypeKey.dailyDarkTrialResetNum.getType());
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_11, useNum).build());

                ConfChapterType conf = ConfChapterType.get(type);
                int open = conf != null ? (humanObj.isModUnlock(conf.open_id) ? 1 : 0) : 0;
                extList.add(HumanManager.inst().to_p_key_value(InstanceConstants.dungeonType_12, open).build());
            }
            break;
            default: {
            }
            break;
        }
        return extList;
    }

    public Define.p_key_value.Builder to_p_key_value(long key, long value) {
        Define.p_key_value.Builder dInfo = Define.p_key_value.newBuilder();
        dInfo.setK(key);
        dInfo.setV(value);
        return dInfo;
    }

    public Define.p_key_string.Builder to_p_key_string(long key, String str) {
        Define.p_key_string.Builder dInfo = Define.p_key_string.newBuilder();
        dInfo.setK(key);
        dInfo.setS(str);
        return dInfo;
    }

    private Define.p_key_string.Builder to_p_key_string(HumanObject humanObj, int key) {
        Define.p_key_string.Builder dInfo = Define.p_key_string.newBuilder();
        dInfo.setK(key);
        String value = "";
        if (key == RoleInfoKey.ROLE_ATTR_NAME.getKey()) {
            value = humanObj.name;
        } else if (key == RoleInfoKey.ROLE_ATTR_GUILD_NAME.getKey()) {
            value = humanObj.getHuman2().getGuildName();
        } else if (key == RoleInfoKey.ROLE_ATTR_SLAVE_NAME.getKey()) {

        } else if (key == RoleInfoKey.ROLE_ATTR_LOVER_NAME.getKey()) {

        } else if (key == RoleInfoKey.ROLE_ATTR_HEAD_URL.getKey()) {

        } else {
            Log.temp.error("===代码未实现， key={}", key);
        }
        dInfo.setS(value);
        return dInfo;
    }

    public void sendMsg_role_goods_refresh_list_s2c(HumanObject humanObj) {
        MsgRole.role_goods_refresh_list_s2c.Builder msg = MsgRole.role_goods_refresh_list_s2c.newBuilder();
        Map<Integer,Integer> goodsRefreshMap = Utils.jsonToMapIntInt(humanObj.operation.itemData.getItem().getItemRecoverMap());
        for (ConfGoodsRefresh conf : ConfGoodsRefresh.findAll()) {
            Define.p_goods_refresh.Builder dInfo = Define.p_goods_refresh.newBuilder();
            int nextTime = goodsRefreshMap.getOrDefault(conf.sn, 0);
            dInfo.setCfgId(conf.sn);
            int max = ItemManager.inst().getRefreshGoodMaxNum(humanObj,conf);
            dInfo.setMaxNum(max);
            dInfo.setNextTime(nextTime);
            msg.addGoodsRefreshList(dInfo);

            //镐子另外发
            if(conf.sn == ItemConstants.AUTO_MINE){
                MsgHome.home_mine_update_recover_s2c.Builder msg1 = MsgHome.home_mine_update_recover_s2c.newBuilder();
                msg1.setNextTime(nextTime);
                msg1.setMaxNum(max);
                humanObj.sendMsg(msg1);
            }
        }
        humanObj.sendMsg(msg);
    }

    /**
     * 玩家属性
     *
     * <AUTHOR>
     * @Date 2024/4/9
     * @Param
     */
    public void sendMsg_role_total_sp_info_s2c(HumanObject humanObj, int type) {
        MsgRole.role_total_sp_info_s2c.Builder msg = MsgRole.role_total_sp_info_s2c.newBuilder();
        msg.setType(type);// 属性界面1
        PropCalc propCalc = humanObj.getPropPlus();
        msg.addAllList(to_p_key_value_spList(propCalc, type));

        // 主动技能
        List<Define.p_active_skill> activeSkillList = SkillManager.inst().getAllActiveSkill(humanObj);
        List<Integer> activeSnList = new ArrayList<>();
        for(Define.p_active_skill dActive : activeSkillList){
            activeSnList.add(dActive.getSkillId());
        }
        // 被动技能
        List<Define.p_passive_skill> passiveSkillList = SkillManager.inst().getAllPassiveSkill(humanObj);
        int lineup = humanObj.getHuman2().getUsePetLineup();
        List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(lineup);
        if(petSnList == null){
            petSnList = new ArrayList<>();
        }
        List<Define.p_attr_obj> p_attr_objList = SkillManager.inst().to_p_attr_obj(passiveSkillList, petSnList,
                activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));
        msg.addAllAttrObj(p_attr_objList);
        humanObj.sendMsg(msg);
    }

    public void sendMsg_role_total_sp_update_s2c(HumanObject humanObj){
        MsgRole.role_total_sp_update_s2c.Builder msg = MsgRole.role_total_sp_update_s2c.newBuilder();
        PropCalc propCalc = humanObj.getPropPlus();
        msg.addAllList(to_p_key_value_spList(propCalc, 0));
        // 主动技能
        List<Define.p_active_skill> activeSkillList = SkillManager.inst().getAllActiveSkill(humanObj);
        List<Integer> activeSnList = new ArrayList<>();
        for(Define.p_active_skill dActive : activeSkillList){
            activeSnList.add(dActive.getSkillId());
        }
        // 被动技能
        List<Define.p_passive_skill> passiveSkillList = SkillManager.inst().getAllPassiveSkill(humanObj);
        int lineup = humanObj.getHuman2().getUsePetLineup();
        List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(lineup);
        if(petSnList == null){
            petSnList = new ArrayList<>();
        }
        List<Define.p_attr_obj> p_attr_objList = SkillManager.inst().to_p_attr_obj(passiveSkillList,
                petSnList, activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));
        msg.addAllAttrObj(p_attr_objList);
        humanObj.sendMsg(msg);
    }

    public PropCalc getPropPlus(UnitPropPlusMap unitPropPlusMap, List<EntityUnitPropPlus> excludeNameList){
        return getPropPlus(unitPropPlusMap, excludeNameList, null);
    }

    public PropCalc getPropPlus(UnitPropPlus unitPropPlus, List<EntityUnitPropPlus> excludeNameList) {
        UnitPropPlusMap unitPropPlusMap = new UnitPropPlusMap();
        unitPropPlusMap.init(unitPropPlus);
        return getPropPlus(unitPropPlusMap, excludeNameList);
    }

    public PropCalc getPropPlus(UnitPropPlusMap unitPropPlusMap, List<EntityUnitPropPlus> excludeNameList, List<PropCalc> propCalcList) {
        PropCalc data = new PropCalc();
        //遍历加成属性来累加数据
        for(EntityUnitPropPlus k : EntityUnitPropPlus.values()) {
            if(k== EntityUnitPropPlus.buff){
                continue;
            }
            if(excludeNameList.contains(k)){
                continue;
            }

            data.plus(unitPropPlusMap.dataMap.get(k.name()));
        }

        if(propCalcList != null){
            for (PropCalc propCalc : propCalcList) {
                data.plus(propCalc);
            }
        }

        Map<Integer, BigDecimal> dataMap = data.getDatas();

        // （2001（基础攻击加成）*（2002（全局攻击）/10000+1）
        BigDecimal attr_3001 = Utils.getBigDecimal3(dataMap.get(2001), dataMap.get(2002));
        dataMap.put(3001, attr_3001);

        //（1003（基础攻速）*（3003（攻速加成）/10000+1）
        BigDecimal attr_3002 = Utils.getBigDecimal3(dataMap.get(2003), dataMap.get(2004));
        dataMap.put(3002, attr_3002);

        //（2007(基础攻速加成）/10000+1）*（1+2021（当前攻速加成）/10000）
        BigDecimal attr_3003 = Utils.getBigDecimal3(dataMap.get(2007), dataMap.get(2021));
        dataMap.put(3003, attr_3003);

        //（2005(基础防御）/10000+1）*（1+2006（全局防御））/10000
        BigDecimal attr_3024 = Utils.getBigDecimal3(dataMap.get(2005), dataMap.get(2006));
        dataMap.put(3024, attr_3024);

        // 最终攻击1 = （1001（基础攻击）*（3001（攻击加成）/10000+1）
        BigDecimal total_att = Utils.getBigDecimal(dataMap.get(1001),dataMap.get(3001));
        dataMap.put(1, total_att);

        //（1002（基础生命）*（3002（生命加成）/10000+1）
        BigDecimal total_hp =  Utils.getBigDecimal(dataMap.get(1002), dataMap.get(3002));
        dataMap.put(2, total_hp);

        //（1003（基础攻速）*（3003（攻速加成）/10000+1）
        BigDecimal total_att_speed = Utils.getBigDecimal(dataMap.get(1003), dataMap.get(3003));
        dataMap.put(3, total_att_speed);

        //（1024（基础防御）*（3024（防御加成）/10000+1）
        BigDecimal total_def =  Utils.getBigDecimal(dataMap.get(1024), dataMap.get(3024));
        dataMap.put(24, total_def);
        return data;
    }

    public List<Define.p_key_value> to_p_key_value_spList(PropCalc propCalc, int type) {
        List<Define.p_key_value> result = new ArrayList<>();
        for (Map.Entry<Integer, BigDecimal> entry : propCalc.getDatas().entrySet()) {
            int key = entry.getKey();
            long value = getCalculationValue(key, propCalc.getDatas());
            if(value == -1){
                if(type == 0){
                    // 1001、1002、1003、1024转换成最终属性1、2、3、24
                    value = propCalc.getBigDecimal(getType(key)).longValue();
                } else {// 属性查看type==1
                    value = entry.getValue().longValue();
                }
            }
            // 注意这个要放value后面，因为把2000段的属性放到1000段，值还是取2000段的值
            key = getKey(key);
            result.add(to_p_key_value(key, value).build());
        }
        return result;
    }

    // 攻击生命攻速防御转换成最终攻击 最终生命 最终攻速 最终防御
    public int getType(int key){
        switch (key){
            case 1001:
                return 1;
            case 1002:
                return 2;
            case 1003:
                return 3;
            case 1024:
                return 24;
        }
        return key;
    }

    // key都要转换
    public int getKey(int key){
        switch (key){
            case 2024:
                return 1041;
            case 2026:
                return 1043;
            case 2027:
                return 1044;
        }
        return key;
    }

    public long getCalculationValue(int key,  Map<Integer, BigDecimal> dataMap){
        switch (key){
            case 1005:
                return Math.max(0, Utils.getBigDecimal(dataMap.get(1005), dataMap.get(2008), dataMap.get(2009)).longValue());
            case 1006:
                // 属性面板=1006（抗暴）*（2010（抗暴加成）/10000+1）*（1+2011（全局抗暴）/10000）
                return Math.max(0, Utils.getBigDecimal(dataMap.get(1006), dataMap.get(2010), dataMap.get(2011)).longValue());
            case 1007:
                // 属性面板=1007（忽视闪避）*（2012（基础命中加成）/10000+1）*（1+2013（当前命中加成）/10000）
                return Math.max(0, Utils.getBigDecimal(dataMap.get(1007), dataMap.get(2012), dataMap.get(2013)).longValue());
            case 1008:
                // 属性面板=1008（闪避）*（2014（基础闪避加成）/10000+1）*（1+2015（当前闪避加成）/10000）
                return Math.max(0, Utils.getBigDecimal(dataMap.get(1008), dataMap.get(2014), dataMap.get(2015)).longValue());
            case 1012:
                // 属性面板=（1012（回复）*（2019（生命回复加成）/10000+1）
                return Math.max(0, Utils.getBigDecimal2(dataMap.get(1012), dataMap.get(2019)).longValue());
            case 1013:
                // 属性面板=（1013（能量回复）*（2016（基础能量恢复加成）/10000+1）
                return Math.max(0, Utils.getBigDecimal2(dataMap.get(1013), dataMap.get(2016)).longValue());
            case 1021:
                // 属性面板=（1021（减伤）*（2025（基础减伤百分比加成）/10000+1）
                return Math.max(0, Utils.getBigDecimal2(dataMap.get(1021), dataMap.get(2025)).longValue());
            case 1030:
                // 属性面板=1030（普攻击晕别人时长）*（2029（击晕时长加成）/10000+1）
                return Math.max(0, Utils.getBigDecimal2(dataMap.get(1030), dataMap.get(2029)).longValue());
            case 1032:
                //属性面板=1032（连击系数）*（2017（连击伤害）/10000+1）*（1+2030（全局连击伤害）/10000）
                return Math.max(0, Utils.getBigDecimal(dataMap.get(1032), dataMap.get(2017), dataMap.get(2030)).longValue());
            case 1033:
                //属性面板=1033（反击系数）*（2018（反击伤害）/10000+1）*（1+2031（全局反击伤害）/10000）
                return Math.max(0, Utils.getBigDecimal(dataMap.get(1033), dataMap.get(2018), dataMap.get(2031)).longValue());
            case 1039:
                //属性面板=（1039（普攻系数）*（2022（普攻伤害）/10000+1）*（1+2023（全局普攻伤害）/10000）
                return Math.max(0, Utils.getBigDecimal(dataMap.get(1039), dataMap.get(2022), dataMap.get(2023)).longValue());
            case 1041:
                //属性面板=1041（主动技能buff持续时间）= 2024（主动技能buff持续时间加成)
                return dataMap.get(2024).longValue();
            case 1043:
                //属性面板1043=2026（主动技能伤害加成)
                return dataMap.get(2026).longValue();
            case 1044:
                //属性面板1044= 2027（技能属性加成)
                return dataMap.get(2027).longValue();
            case 1045:
                //属性面板=1045（技能伤害）*（2033（全局技能伤害）/10000+1）
                return Math.max(0, Utils.getBigDecimal2(dataMap.get(1045), dataMap.get(2033)).longValue());
            case 1046:
                //属性面板=1046（BOSS伤害）*（2028（对BOSS造成伤害加成）/10000+1）
                return Math.max(0, Utils.getBigDecimal2(dataMap.get(1046), dataMap.get(2028)).longValue());
            case 1047:
                //属性面板=1047（同伴伤害）*（2020（同伴伤害加成）/10000+1）
                return Math.max(0, Utils.getBigDecimal2(dataMap.get(1047), dataMap.get(2020)).longValue());
        }
        return -1;
    }

    


    public Define.p_attr_obj to_p_attr_obj(int type, int id, List<Define.p_key_value> attrList){
        Define.p_attr_obj.Builder dInfo = Define.p_attr_obj.newBuilder();
        dInfo.setType(type);// 1:伙伴;2:技能
        dInfo.setId(id);// 技能ID/伙伴ID
        dInfo.addAllAttrList(attrList);
        return dInfo.build();
    }

    public Define.p_attr_obj_list to_p_attr_obj_list(int type, int id, List<Define.p_key_value> attrList){
        Define.p_attr_obj_list.Builder dInfo = Define.p_attr_obj_list.newBuilder();
        dInfo.setObj(HumanManager.inst().to_p_key_value(type, id));// 1:伙伴;2:技能，//  技能ID/伙伴ID
        dInfo.addAllAttrList(attrList);
        return dInfo.build();
    }

    public List<Define.p_attr_obj> to_p_attr_obj_list(List<Define.p_attr_obj_list> list) {
        List<Define.p_attr_obj> result = new ArrayList<>();
        for (Define.p_attr_obj_list dInfo : list) {
            Define.p_attr_obj.Builder dObj = Define.p_attr_obj.newBuilder();
            dObj.setType((int)dInfo.getObj().getK());
            dObj.setId((int)dInfo.getObj().getV());
            dObj.addAllAttrList(dInfo.getAttrListList());
            result.add(dObj.build());
        }
        return result;
    }

    public void sendMsg_goods_change_s2c(HumanObject humanObj, List<Define.p_goods> dInfoList) {
        MsgGoods.goods_change_s2c.Builder msg = MsgGoods.goods_change_s2c.newBuilder();
        msg.setSource(0);
        msg.addAllGoodsList(dInfoList);
        humanObj.sendMsg(msg);
    }

    public void sendMsg_goods_change_s2c(HumanObject humanObj, Define.p_goods dInfo) {
        MsgGoods.goods_change_s2c.Builder msg = MsgGoods.goods_change_s2c.newBuilder();
        msg.setSource(0);
        msg.addGoodsList(dInfo);
        humanObj.sendMsg(msg);
    }

    /**
     * 重载sendMsg_role_info_change_s2c，调用处不需要再构造p_role_change消息
     */
    public void sendMsg_role_info_change_s2c(HumanObject humanObj, RoleInfoKey... keys) {
        Define.p_role_change.Builder dInfo = Define.p_role_change.newBuilder();
        for (RoleInfoKey key : keys) {
            // 闪钻付费打点过滤
            if(RoleInfoKey.resIdList.contains(key)){
                continue;
            }
            if (key.getValueType() == 1) {
                dInfo.addKs(to_p_key_string(humanObj, key.getKey()));
            } else {
                dInfo.addKv(to_p_key_value(humanObj, key.getKey()));
            }
        }
        MsgRole.role_info_change_s2c.Builder msg = MsgRole.role_info_change_s2c.newBuilder();
        msg.setChangeList(dInfo);
        humanObj.sendMsg(msg);
    }

    public void sendMsg_role_info_change_s2c(HumanObject humanObj, Define.p_role_change dInfo) {
        MsgRole.role_info_change_s2c.Builder msg = MsgRole.role_info_change_s2c.newBuilder();
        msg.setChangeList(dInfo);
        humanObj.sendMsg(msg);
    }

    public void updatePowerPar(HumanObject humanObj, EModule type, long powerPar) {
        Map<Integer, String> modPowerMap = Utils.jsonToMapIntString(humanObj.getHuman3().getModPowerJSON());
        modPowerMap.put(type.getType(), String.valueOf(powerPar));
        humanObj.getHuman3().setModPowerJSON(Utils.mapIntStrToJSON(modPowerMap));
    }

    public void addPowerPar(HumanObject humanObj, EModule type, long powerPar) {
        Map<Integer, String> modPowerMap = Utils.jsonToMapIntString(humanObj.getHuman3().getModPowerJSON());
        long power = Utils.longValue(modPowerMap.getOrDefault(type.getType(), "0"));
        modPowerMap.put(type.getType(), String.valueOf(power + powerPar));
        humanObj.getHuman3().setModPowerJSON(Utils.mapIntStrToJSON(modPowerMap));
    }

    public Define.p_client_data to_p_client_data(int id, List<Define.p_key_string3> dInfoList){
        Define.p_client_data.Builder dInfo = Define.p_client_data.newBuilder();
        dInfo.setSysId(id);
        dInfo.addAllData(dInfoList);
        return dInfo.build();
    }

    public Define.p_key_string3 to_p_key_string3(String k, String s){
        Define.p_key_string3.Builder dInfo = Define.p_key_string3.newBuilder();
        dInfo.setK(k);
        dInfo.setS(s);
        return dInfo.build();
    }

    public void _msg_client_data_c2s(HumanObject humanObj, MsgRole.client_data_c2s msg){
        ClientInfo clientInfo = humanObj.operation.clientInfo;
        JSONObject jo = Utils.toJSONObject(clientInfo.getClientJSON());
        String sysId = String.valueOf(msg.getSysId());
        int update = msg.getUpdate();
        List<Define.p_key_string3> dInfoList = msg.getDataList();
        JSONObject joInfo = new JSONObject();
        if(jo.containsKey(sysId)){
            joInfo = Utils.toJSONObject(jo.getString(sysId));
        }
        for(Define.p_key_string3 dInfo : dInfoList){
            joInfo.put(dInfo.getK(), dInfo.getS());
        }
        jo.put(sysId, joInfo.toJSONString());
        clientInfo.setClientJSON(jo.toJSONString());
        clientInfo.update();


        // 发什么回什么，不要什么都发
        MsgRole.client_data_s2c.Builder msgResult = MsgRole.client_data_s2c.newBuilder();
        Define.p_client_data.Builder dInfo = Define.p_client_data.newBuilder();
        dInfo.setSysId(Utils.intValue(sysId));
        for (String k : joInfo.keySet()) {
            Define.p_key_string3.Builder data = Define.p_key_string3.newBuilder();
            data.setK(k);
            data.setS(joInfo.getString(k));
            dInfo.addData(data);
        }
        msgResult.addList(dInfo);
        humanObj.sendMsg(msgResult);
    }

    @Listener(EventKey.PRODUCE_MONEY_CHANGE)
    public void _on_PRODUCE_MONEY_CHANGE(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int useType = Utils.getParamValue(param, "useType", 0);
        if(useType != ParamKey.useType_2){
            return;
        }
        int type = Utils.getParamValue(param, "type", 0);

        int num = Utils.intValue(Utils.getParamValue(param, "num", 0L));

        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_每日, TaskConditionTypeKey.TASK_TYPE_6, type, num);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_6, type, num);
    }

    @Listener(value = EventKey.ITEM_CHANGE_DEL)
    public void _on_ITEM_CHANGE_DEL(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int itemSn = Utils.getParamValue(param, "itemSn", 0);
        int itemNum = Utils.getParamValue(param, "itemNum", 0);

        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_每日, TaskConditionTypeKey.TASK_TYPE_6, itemSn, itemNum);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_6, itemSn, itemNum);
    }

    @Listener(EventKey.ITEM_CHANGE)
    public void _on_ITEM_CHANGE(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj == null) {
            return;
        }
        int sn = Utils.getParamValue(param, "itemSn", 0);
        if(ProduceManager.snList.contains(sn)){
            int value = humanObj.operation.itemData.getItemNum(sn);
            sendMsg_role_resource_change_s2c(humanObj, to_p_reward(sn, value));
        }
    }


    public void sendMsg_role_resource_change_s2c(HumanObject humanObj, Define.p_role_change.Builder dRole ){
        List<Define.p_reward> rewardsList = new ArrayList<>();
        for(Define.p_key_value kv : dRole.getKvList()){
            int sn = Utils.intValue(kv.getK());
            if(ProduceManager.snList.contains(sn)){
                long value = 0;
                if(TokenItemType.isMoney(sn)){
                    value = MoneyManager.inst().getProduceReduce(humanObj,sn);
                } else {
                    value = humanObj.operation.itemData.getItemNum(sn);
                }
                rewardsList.add(HumanManager.inst().to_p_reward(sn, value));
            }
        }
        if(!rewardsList.isEmpty()){
            sendMsg_role_resource_change_s2c(humanObj, rewardsList);
        }
    }

    public void sendMsg_role_resource_change_s2c(HumanObject humanObj, List<Define.p_reward> rewards){
        MsgRole.role_resource_change_s2c.Builder msg = MsgRole.role_resource_change_s2c.newBuilder();
        msg.setSource(0);
        msg.addAllChangeInfo(rewards);
        humanObj.sendMsg(msg);
    }

    public void sendMsg_role_resource_change_s2c(HumanObject humanObj, Define.p_reward reward){
        MsgRole.role_resource_change_s2c.Builder msg = MsgRole.role_resource_change_s2c.newBuilder();
        msg.setSource(0);
        msg.addChangeInfo(reward);
        humanObj.sendMsg(msg);
    }

    public void _msg_heart_beat_c2s(HumanObject humanObj, int svrTime) {
        MsgLogin.heart_beat_s2c.Builder msg = MsgLogin.heart_beat_s2c.newBuilder();
        msg.setSvrTime(Utils.getTimeSec());
        humanObj.sendMsg(msg);
    }

    public void role_rename_c2s(HumanObject humanObj, String name) {
        MsgRole.role_rename_s2c.Builder msg = MsgRole.role_rename_s2c.newBuilder();
        int[] cost = ConfGlobal.get(ConfGlobalKey.change_name_cost.SN).intArray;
        ReasonResult result = new ReasonResult(true,0);
        if(humanObj.getHumanExtInfo().getChangeNameTime()!=0){
            result = ProduceManager.inst().canCostProduce(humanObj, cost[0], cost[1]);
        }
        msg.setName(humanObj.getHuman().getName());
        if(!result.success){
            Inform.sendMsg_error(humanObj,12);
            return;
        }
        result = NameManager.inst().checkName(name);
        if(!result.success){
            msg.setCode(result.code);
            humanObj.sendMsg(msg);
            return;
        }
        //判断名字是否重复
        NameServiceProxy prx = NameServiceProxy.newInstance();
        prx.repeat(humanObj.getHuman().getServerId(), name);
        prx.listenResult(this::_result_change_name,"humanObj", humanObj, "name", name,  "item",cost[0], "num",cost[1],"msg",msg);
    }

    public void _result_change_name(Param results, Param context){
        // 上下文环境
        HumanObject humanObj = context.get("humanObj");
        String name = context.getString("name");
        int num=context.getInt("num");
        int item=context.get("item");
        MsgRole.role_rename_s2c.Builder msg = context.get("msg");
        // 查询结果
        if(results.getBoolean("repeat")){
            Inform.sendMsg_error(humanObj,51);
            return;
        }

        //扣钱，第一次改名免费
        if(humanObj.getHumanExtInfo().getChangeNameTime()!=0){
            ProduceManager.inst().costItem(humanObj, item, num, MoneyItemLogKey.改名);
        }

        //修改名字
        Human human = humanObj.getHuman();
        String oldName = human.getName();
        human.setName(name);
        human.update();

        HumanExtInfo humanExtInfo = humanObj.getHumanExtInfo();
        humanExtInfo.setChangeNameTime(humanExtInfo.getChangeNameTime() + 1);
        humanExtInfo.update();

        humanObj.name = name;
        humanObj.updateHumanData(true);

        //同步名字服务
        NameServiceProxy prx = NameServiceProxy.newInstance();
        prx.change(human.getServerId(), oldName, name);

        //返回消息
        msg.setCode(0);
        msg.setName(name);
        humanObj.sendMsg(msg);
        HumanManager.inst().sendMsg_role_info_change_s2c(humanObj, RoleInfoKey.ROLE_ATTR_NAME, RoleInfoKey.ROLE_ATTR_RENAME);

        //派发事件
        Event.fire(EventKey.HUMAN_CHANGE_NAME, "humanObj", humanObj);
    }

    public void role_setting_info_c2s(HumanObject humanObj) {
        sendMsg_role_setting_info_s2c(humanObj);
    }

    public void role_set_setting_c2s(HumanObject humanObj, int type, long value) {
        boolean isServerKey = type >= MIN_SET_TYPE;
        if (!isServerKey) {
            ConfSetting conf = ConfSetting.get(type);
            isServerKey = conf != null && conf.type == 1 && conf.isShow == 1;
        }
        if (!isServerKey) {
            return;
        }

        Map<Integer, Long> settingMap = Utils.jsonToMapIntLong(humanObj.getHumanExtInfo().getSettingMap());
        settingMap.put(type, value);
        humanObj.getHumanExtInfo().setSettingMap(Utils.mapIntLongToJSON(settingMap));
        sendMsg_role_setting_info_s2c(humanObj);
    }

    public Define.p_head.Builder to_p_head(Human human){
        Define.p_head.Builder head = Define.p_head.newBuilder();
        head.setId(human.getHeadSn());
        head.setFrameId(human.getCurrentHeadFrameSn());
        head.setUrl("");
        return head;
    }

    public Define.p_role_figure.Builder to_p_role_figure(Human human, Human2 human2){
        Define.p_role_figure.Builder dFigur = Define.p_role_figure.newBuilder();
        Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(human2.getEquipFigureMap());
        //遍历weapon到face的部位不存在或者value=0就去默认取
        for(int i = weapon; i <= face; ++i){
            int sn = figureMap.getOrDefault(i, EquipManager.FIGURE_DEFAULT);
            dFigur.addEquipList(HumanManager.inst().to_p_key_value(i,sn));
        }
        // 武魂
        dFigur.addEquipList(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_4, human2.getFateShow()).build());
        // 翅膀
        int wingSn = human2.getWingUse();
        dFigur.addEquipList(HumanManager.inst().to_p_key_value(HumanManager.wing, wingSn));

        dFigur.setHairFigure(human2.getHairColor());// 发色
        dFigur.setJobFigure(human.getJobModel());
        // 坐骑
        int mountSn = human2.getMountUse();
        dFigur.setMountFigure(mountSn);
        // 神器sn
        int artifactSn = human2.getArtifactUse();
        dFigur.setArtifactFigure(artifactSn);
        dFigur.setGender(1);// 无性别 默认1
        // 皮肤
        dFigur.addSkinList(HumanManager.inst().to_p_key_value(2, human2.getCurrentSkin()));
        // 头衔
        dFigur.setCurrentTitle(human.getCurrentTitleSn());
        return dFigur;
    }

    public void updateLvToRedis(HumanObject humanObj){
        Human human = humanObj.getHuman();

        RankManager.inst().rankUpdate(RankParamKey.rankTypeLevel_1001, human.getServerId(),
                String.valueOf(human.getLevel()), String.valueOf(humanObj.id));

    }


    /**
     * 游戏启动时
     * @param params
     * @throws Exception
     */
    @Listener(EventKey.GAME_STARTUP_FINISH)
    public void onGameStartupBefore(Param params) {
        try{
            Node node = params.get("node");
            if(NodeType.WORLD.isMatch(node.getNodeType())) {
                // 随机一个port处理
                Port port = node.getRandomPortInfo();
                port.addQueue(new PortPulseQueue() {
                    @Override
                    public void execute(Port port) {
                        // 游戏启动完成后续处理流程
                        gameStartupFinishFollowUp();

                    }
                });
            }
        }catch(Exception e){
            throw new SysException(e);
        }
    }

    /**
     * 游戏启动完成后续处理 （此刻能保证所有service启动都完成）
     * <AUTHOR>
     * @Date 2021/4/24
     * @Param
     */
    private void gameStartupFinishFollowUp(){

        // 去后台请求屏蔽字库
        sendHttpShield();
        // 去后台请求跑马灯
        sendHttpMarquee();

        if(C.GAME_SERVER_ID == 30059){
            HumanGlobalServiceProxy.newInstance().loadServerId(40059);
        } else {
            HumanGlobalServiceProxy.newInstance().loadServerId(C.GAME_SERVER_ID);
        }
//        sendHttpAdmin();

//		ActivityControlServiceProxy.newInstance().initOpenSn();

        if(!S.isGameLeagueOpen){
            return;
        }
    }

    /**
     * 去后台请求屏蔽字库
     * <AUTHOR>
     * @Date 2022/5/5
     * @Param
     */
    public void sendHttpShield(){
        // 启动完成后去后台加载一下屏蔽字（后台额外一份）
        String portHttpAsync = ConstPf.PORT_HTTP_ASYNC_PREFIX + new Random().nextInt(ConstPf.PORT_STARTUP_NUM_CHAT);
        String url = Utils.createStr("{}shieldWord/update", Config.GAME_GMT_IP);
        HttpAsyncSendServiceProxy asyncSendProxy = HttpAsyncSendServiceProxy.newInstance(ConstPf.NODE_ID, portHttpAsync, ConstPf.SERV_HTTP_SEND);
        asyncSendProxy.httpGetAsync(url, Utils.<String, String>ofMap(), false);
        Log.temp.info("===去后台请求屏蔽字库");
    }

    public void sendHttpAdmin(){

        String addr = Distr.getNodeAddr(Distr.NODE_ADMIN_SERVER);
        String ip = addr.replace("tcp://", "").split(":")[0];
        Log.temp.info("===ip={}，addr={}", ip, addr);
        if(ip.equals("127.0.0.1")){
            Log.temp.error("===地址出现错误={}, addr={}", ip, addr);
        }

        String addrWorld = Distr.getNodeAddr(Distr.NODE_DEFAULT);
        String ipWorld = addrWorld.replace("tcp://", "").split(":")[0];

        JSONObject jo = new JSONObject();
        jo.put("serverId", Config.GAME_SERVER_ID);
        jo.put("ip", ipWorld);
        jo.put("port", ConstPf.HTTP_PORT1);
        String portHttpAsync = ConstPf.PORT_HTTP_ASYNC_PREFIX + new Random().nextInt(ConstPf.PORT_STARTUP_NUM_CHAT);
        HttpAsyncSendServiceProxy asyncSendProxy = HttpAsyncSendServiceProxy.newInstance(ConstPf.NODE_ID, portHttpAsync, ConstPf.SERV_HTTP_SEND);
        asyncSendProxy.httpGetAsync(Utils.createStr("http://{}:{}{}", ip, ConstPf.HTTP_PORT1, HttpServerHandler.GAME_SERVER), jo, false);

        Log.temp.error("===通知admin注册ip地址url={}, jo={}", Utils.createStr("http://{}:{}{}", ip, ConstPf.HTTP_PORT1, HttpServerHandler.GAME_SERVER), jo);
        
    }

    /**
     * 去后台请求跑马灯
     * <AUTHOR>
     * @Date 2023/7/27
     * @Param
     */
    public void sendHttpMarquee(){
        String portHttpAsync = ConstPf.PORT_HTTP_ASYNC_PREFIX + new Random().nextInt(ConstPf.PORT_STARTUP_NUM_CHAT);
//		http://************:8081/game/resendNotice?server=2001
        String url = Utils.createStr("{}{}", ConstPf.HTTP_READONLY_URL, "game/resendNotice");
        JSONObject jo = new JSONObject();
        jo.put("server", Config.GAME_SERVER_ID);
        HttpAsyncSendServiceProxy asyncSendProxy = HttpAsyncSendServiceProxy.newInstance(ConstPf.NODE_ID, portHttpAsync, ConstPf.SERV_HTTP_SEND);
        asyncSendProxy.httpGetAsync(url, jo, false);
        Log.temp.info("===去后台请求跑马灯");
    }

    public void _msg_role_guide_c2s(HumanObject humanObj, int id){
        ClientInfo clientInfo = humanObj.operation.clientInfo;
        clientInfo.setGuideId(id);
        clientInfo.update();
        sendMsg_role_guide_s2c(humanObj);
    }

    public void role_others_c2s(HumanObject humanObj, long roleId, int source) {
        int serverId = Utils.getServerIdByHumanId(roleId);
        if(serverId != Config.SERVER_ID && !Utils.isDebugMode()){
            role_others_c2s_cross(humanObj, roleId, source);
            return;
        }
        // 本服的直接找到Proxy，和跨服调用相同接口，用humanBrief来统一处理
        GuildServiceProxy proxy = GuildServiceProxy.newInstance();
        proxy.getCrossHumanInfo(roleId, 0);
        proxy.listenResult(this::_result_getCrossHumanInfo, "humanObj", humanObj, "source", source);
    }

    public void role_others_c2s_cross(HumanObject humanObj, long roleId, int source) {
        try {
            CrossManager.getInstance().callCrossFunc(CrossType.cross_chaos_battle, Config.SERVER_ID, res -> {
                try {
                    if(res.failed()){
                        Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                        return;
                    }
                    CrossPoint result = res.result();
                    GuildLeagueServiceProxy proxy = GuildLeagueServiceProxy.newInstance(result.getNodeId(), D.SERV_GUILD_LEAGUE);
                    proxy.getCrossHumanInfo(roleId, CrossType.cross_chaos_battle.getType());
                    proxy.listenResult(this::_result_getCrossHumanInfo, "humanObj", humanObj, "source", source);
                } catch (Exception e) {
                    Log.temp.error("==跨服链接出问题 ", e);
                }
            });
        } catch (Exception e) {
            Log.temp.error("==跨服链接出问题 ", e);
        }
    }

    private void _result_getCrossHumanInfo(Param results, Param context){
        HumanObject humanObj = (HumanObject) context.get("humanObj");
        int source = (int) context.get("source");
        HumanBrief humanBrief = (HumanBrief) results.get("brief");
        if(humanBrief == null){
            return;
        }
        Equip equip = Utils.getParamValue(results, "equip", null);
        String guildName = Utils.getParamValue(results, "guildName", "");
        int guildLv = Utils.getParamValue(results, "guildLv", 0);
        int guildHumanNum = Utils.getParamValue(results, "guildHumanNum", 0);
        String guildFlagJSON = Utils.getParamValue(results, "guildFlagJSON", "");

        MsgRole.role_others_s2c.Builder msg = MsgRole.role_others_s2c.newBuilder();
        int friendType = FriendManager.inst().getFriendType(humanObj, humanBrief.getId());
        msg.addOtherRoleInfo(to_p_other_role_info(humanBrief, guildHumanNum, guildLv, guildName, guildFlagJSON, friendType, equip, source));
        humanObj.sendMsg(msg);
    }

    public Define.p_other_role_info to_p_other_role_info(HumanBrief brief, int memberNum, int guildLv, String guildName,
                                                         String flagJSON, int friendType, Equip equip, int source) {

        Define.p_other_role_info.Builder msg = Define.p_other_role_info.newBuilder();
        msg.setRoleId(brief.getId());
        Define.p_role_change.Builder dRoleInfo = Define.p_role_change.newBuilder();
        dRoleInfo.addKs(HumanManager.inst().to_p_key_string(RoleInfoKey.ROLE_ATTR_NAME.getKey(), brief.getName()));
        dRoleInfo.addKv(HumanManager.inst().to_p_key_value(RoleInfoKey.ROLE_ATTR_LVL.getKey(), brief.getLevel()));
        dRoleInfo.addKv(HumanManager.inst().to_p_key_value(RoleInfoKey.ROLE_ATTR_HEAD_ID.getKey(), brief.getHeadSn()));
        dRoleInfo.addKv(HumanManager.inst().to_p_key_value(RoleInfoKey.ROLE_ATTR_HEAD_FRAME_ID.getKey(), brief.getCurrentHeadFrameSn()));
        dRoleInfo.addKs(HumanManager.inst().to_p_key_string(RoleInfoKey.ROLE_ATTR_HEAD_URL.getKey(), ""));
        dRoleInfo.addKv(HumanManager.inst().to_p_key_value(RoleInfoKey.ROLE_ATTR_POWER_SHOW.getKey(), new BigDecimal(brief.getTopCombat()).longValue()));
        dRoleInfo.addKv(HumanManager.inst().to_p_key_value(RoleInfoKey.ROLE_ATTR_SERVER.getKey(), brief.getServerId() % Utils.intValue(Config.GAME_SERVER_PREFIX)));
        if (brief.getGuildId() > 0) {
            dRoleInfo.addKs(HumanManager.inst().to_p_key_string(RoleInfoKey.ROLE_ATTR_GUILD_NAME.getKey(), guildName));
            dRoleInfo.addKv(HumanManager.inst().to_p_key_value(RoleInfoKey.ROLE_ATTR_GUILD_LV.getKey(), guildLv));
            dRoleInfo.addKv(HumanManager.inst().to_p_key_value(RoleInfoKey.ROLE_ATTR_GUILD_NUM.getKey(), memberNum));
        }

        msg.setInfoList(dRoleInfo);

        List<Define.p_equip> equips = new ArrayList<>();
        if (equip != null) {
            for (int i = weapon; i <= EquipInfo.max_part; ++i){
                String jsonString = EquipManager.inst().getEquipPart(equip,i);
                if(!Utils.isNullOrEmptyJSONString(jsonString)){
                    EquipInfo equipInfo = new EquipInfo();
                    equipInfo.fromJsonString(jsonString);
                    Define.p_equip.Builder p_equip = equipInfo.toProtoBuilder(ParamKey.equipTab_1);
                    equips.add(p_equip.build());
                }
            }
        }
        msg.addAllOtherEquipList(equips);
        try {
            Define.p_role_figure.Builder figure = Define.p_role_figure.parseFrom(brief.getRoleFigure()).toBuilder();
            List<Define.p_key_value> extList = figure.getEquipListList();
            List<Define.p_key_value> extListNew = new ArrayList<>();
            Map<Integer, Integer> mapNew = new HashMap<>();
            mapNew.put(ParamKey.figureEquip_1, 0);
            mapNew.put(ParamKey.figureEquip_2, 0);
            mapNew.put(ParamKey.figureEquip_3, 0);
            mapNew.put(ParamKey.figureEquip_4, 0);
            mapNew.put(ParamKey.figureEquip_5, 0);
            mapNew.put(ParamKey.figureEquip_6, 0);
            for(Define.p_key_value kv : extList){
                mapNew.put(Utils.intValue(kv.getK()), Utils.intValue(kv.getV()));
            }
            figure.clearEquipList();
            for(Map.Entry<Integer, Integer> entry : mapNew.entrySet()){
                extListNew.add(HumanManager.inst().to_p_key_value(entry.getKey(), entry.getValue()).build());
            }
            figure.addAllEquipList(extListNew);
            msg.setFigure(figure);
        } catch (Exception e) {
            Log.temp.error("==to_p_other_role_info 外观数据解析出错 {}", e);
        }
        try {
            HumanBriefVO briefVO = new HumanBriefVO(brief);
            Define.p_battle_role battle = Define.p_battle_role.parseFrom(briefVO.getCurrentBattleRole());
            msg.addAllSpList(battle.getAttrListList());
            msg.addAllPetList(battle.getPetListList());

        } catch (Exception e) {
            Log.temp.error("==to_p_other_role_info 外观数据解析出错 {}", e);
        }
        if (Utils.isExitRedisKey("entity.init.Farm." + brief.getId())) {
            msg.addFunctionList(FuncOpenType.FUNC_FARM);
        }
        msg.setFriendType(0);
        if (!flagJSON.isEmpty()) {
            msg.addAllGuildFlag(GuildManager.inst().to_p_guild_flag(flagJSON));
        }

        msg.addExt(HumanManager.inst().to_p_key_value(ParamKey.extPosType_6, Utils.getServerIdTo(brief.getServerId())));
        msg.addExt(HumanManager.inst().to_p_key_value(ParamKey.extPosType_7, source));
        int angelStar = brief.getAngelStar();
        if(angelStar > 0){
            msg.addExt(HumanManager.inst().to_p_key_value(ParamKey.roleExt_18, angelStar/100));
            msg.addExt(HumanManager.inst().to_p_key_value(ParamKey.roleExt_19, angelStar%100));
        }
        msg.setFriendType(friendType);
        // 飞宠信息
        msg.addFlyPet(Define.p_key_value.newBuilder().setK(ParamKey.flyPet_id).setV(brief.getFlyPetId()));
        msg.addFlyPet(Define.p_key_value.newBuilder().setK(ParamKey.flyPet_sn).setV(brief.getFlyPetSn()));
        // 美观值
        int charmValue = brief.getCharmValue();
        int charmSn = CharmUtils.getCharmLevelSn(charmValue);
        msg.setCharmSn(charmSn);
        List<Integer> showMedalList = Utils.strToIntList(brief.getShowMedalList());
        msg.addAllShowMedalList(showMedalList);
        return msg.build();
    }

    public void handleRoleSPCompareC2S(HumanObject humanObj, long targetId, int source) {
        if(ArenaManager.inst().isRobot(targetId)){
            robot_role_sp_cmp_s2c(humanObj, targetId, source);
            return;
        }

        int serverId = Utils.getServerIdByHumanId(targetId);
        if(serverId != Config.SERVER_ID && serverId != humanObj.getHuman().getServerId()){
            // 跨服
            try {
                CrossManager.getInstance().callCrossFunc(CrossType.randomType(), Config.SERVER_ID, res -> {
                    try {
                        if(res.failed()){
                            Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                            return;
                        }
                        CrossPoint result = res.result();
                        GameSelectServiceProxy proxy = GameSelectServiceProxy.newInstance(result.getNodeId());
                        proxy.getCrossHumanInfoProp(targetId, source);
                        proxy.listenResult(this::_result_getCrossHumanInfoProp,"humanObj", humanObj, "targetId", targetId, "source", source);
                    } catch (Exception e) {
                        Log.temp.error("==跨服链接出问题 ", e);
                    }
                });
            } catch (Exception e) {
                Log.temp.error("==跨服链接出问题 ", e);
            }
        } else {
            // 本服
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            proxy.getHumanBrief2(targetId);
            proxy.listenResult(this::_result_getHumanBrief2, "humanObj", humanObj, "targetId", targetId, "source", source);
        }
    }

    private void _result_getHumanBrief2(Param results, Param context) {
        HumanObject humanObj = (HumanObject) context.get("humanObj");
        long targetId = (long) context.get("targetId");
        int source = (int) context.get("source");

        HumanBrief humanBrief = (HumanBrief) results.get("humanBrief");
        if(humanBrief == null){
            Log.temp.error("==获取玩家数据为空 roleId={}, source={}", targetId, source);
            return;
        }
        EntityManager.getEntityAsync(UnitPropPlus.class, targetId, (res)-> {
            if (res.failed()) {
                Log.friend.error("无法获取玩家UnitPropPlus数据humanID={}", targetId);
                return;
            }
            UnitPropPlus unitPropPlus = (UnitPropPlus) res.result();
            if(unitPropPlus == null){
                return;
            }
            HumanBriefVO humanBriefVO = new HumanBriefVO(humanBrief);
            byte[] battleRole = humanBriefVO.getCurrentBattleRole();
            try {
                Define.p_battle_role p_battle_role = Define.p_battle_role.parseFrom(battleRole);
                List<Define.p_attr_obj_list> p_attr_objList = p_battle_role.getAttrObjListList();
                Define.p_battle_role myBattleRole = humanObj.to_p_battle_role(humanObj.getHumanExtInfo().getPlan());
                List<Define.p_attr_obj_list> myAttrObjList = myBattleRole.getAttrObjListList();
                UnitPropPlusMap unitPropPlusMap = new UnitPropPlusMap();
                unitPropPlusMap.init(unitPropPlus);
                PropCalc targetProp = HumanManager.inst().getPropPlus(unitPropPlusMap, new ArrayList<>());
                PropCalc humanProp = humanObj.getPropPlus();
                List<Define.p_key_value> humanAttr = HumanManager.inst().to_p_key_value_spList(humanProp, 0);
                List<Define.p_key_value> targetAttr = HumanManager.inst().to_p_key_value_spList(targetProp, 0);
                List<Define.p_system_sp> humanSysAttr = humanObj.dataPers.unitPropPlus.to_p_system_spList();
                List<Define.p_system_sp> targetSysAttr = unitPropPlusMap.to_p_system_spList();
                MsgRole.role_sp_cmp_s2c.Builder msg = MsgRole.role_sp_cmp_s2c.newBuilder();
                msg.addAllMyAttr(humanAttr);
                msg.addAllTargetAttr(targetAttr);
                msg.addAllMySystemSp(humanSysAttr);
                msg.addAllTargetSystemSp(targetSysAttr);
                msg.addAllMyAttrObj(to_p_attr_obj_list(myAttrObjList));
                if(p_attr_objList != null && !p_attr_objList.isEmpty()){
                    msg.addAllTargetAttrObj(to_p_attr_obj_list(p_attr_objList));
                }
                humanObj.sendMsg(msg);
            }catch (Exception e){
                Log.game.error("humanId={} battleRole解析失败: {}", targetId, e.getMessage());
            }
        });
    }

    private void _result_getCrossHumanInfoProp(Param results, Param context){
        HumanObject humanObj = (HumanObject)context.get("humanObj");

        UnitPropPlus unitPropPlus = (UnitPropPlus)results.get(UnitPropPlus.tableName);
        if(unitPropPlus==null){
            Log.temp.error("==跨服获取数据出问题{} {}", results, context);
            return;
        }
        Define.p_battle_role myBattleRole = humanObj.to_p_battle_role(humanObj.getHumanExtInfo().getPlan());
        List<Define.p_attr_obj_list> myAttrObjList = myBattleRole.getAttrObjListList();
        List<Define.p_attr_obj_list> targetAttrObjList = results.get("attrObjList");
        UnitPropPlusMap unitPropPlusMap = new UnitPropPlusMap();
        unitPropPlusMap.init(unitPropPlus);
        PropCalc targetProp = HumanManager.inst().getPropPlus(unitPropPlusMap, new ArrayList<>());
        PropCalc humanProp = humanObj.getPropPlus();
        List<Define.p_key_value> humanAttr = HumanManager.inst().to_p_key_value_spList(humanProp, 0);
        List<Define.p_key_value> targetAttr = HumanManager.inst().to_p_key_value_spList(targetProp, 0);
        List<Define.p_system_sp> humanSysAttr = humanObj.dataPers.unitPropPlus.to_p_system_spList();
        List<Define.p_system_sp> targetSysAttr = unitPropPlusMap.to_p_system_spList();
        MsgRole.role_sp_cmp_s2c.Builder msg = MsgRole.role_sp_cmp_s2c.newBuilder();
        msg.addAllMyAttr(humanAttr);
        msg.addAllTargetAttr(targetAttr);
        msg.addAllMySystemSp(humanSysAttr);
        msg.addAllTargetSystemSp(targetSysAttr);
        msg.addAllMyAttrObj(to_p_attr_obj_list(myAttrObjList));
        if(targetAttrObjList != null && !targetAttrObjList.isEmpty()){
            msg.addAllTargetAttrObj(to_p_attr_obj_list(targetAttrObjList));
        }
        humanObj.sendMsg(msg);
    }

    private void robot_role_sp_cmp_s2c(HumanObject humanObj, long targetId, int source){
        if(source == ParamKey.sourceKey_1){ // 跨服
            JSONObject joRobot = humanObj.arenaRankRobotMap.get(Utils.intValue(targetId));
            if (joRobot != null && !joRobot.isEmpty()) {
                ConfCrossPvpRobot conf = ConfCrossPvpRobot.get(joRobot.getIntValue("pvpRobotSn"));

                PropCalc propCalcEquipAll = new PropCalc();
                JSONArray ja = Utils.toJSONArray(joRobot.getString("equipStr"));
                for (int i = 0; i < ja.size(); i++) {
                    JSONObject jo = ja.getJSONObject(i);
                    PropCalc propCalc = new PropCalc(jo.getString(ParamKey.baseAttrKey));
                    PropCalc randAttr = new PropCalc(jo.getString(ParamKey.randAttrKey));
                    propCalcEquipAll.plus(propCalc);
                    propCalcEquipAll.plus(randAttr);
                }
                UnitPropPlusMap unitPropPlusMap = new UnitPropPlusMap();
                unitPropPlusMap.unitPropPlus.setEquip(propCalcEquipAll.toJSONStr());
                // 属性
                int[][] attrArr = conf.attr;
                PropCalc propCalcAll = new PropCalc();
                for (int i = 0; i < attrArr.length; i++) {
                    int length = attrArr[i].length;
                    for (int m = 0; m < length; m += 2) {
                        int propSn = Utils.intValue(attrArr[i][m]);
                        int propValue = Utils.intValue(attrArr[i][m + 1]);
                        propCalcAll.plus(propSn, BigDecimal.valueOf(propValue));
                    }
                }
                unitPropPlusMap.unitPropPlus.setInitAttr(propCalcAll.toJSONStr());
                PropCalc targetProp = HumanManager.inst().getPropPlus(unitPropPlusMap, new ArrayList<>());
                PropCalc humanProp = humanObj.getPropPlus();
                List<Define.p_key_value> humanAttr = HumanManager.inst().to_p_key_value_spList(humanProp, 0);
                List<Define.p_key_value> targetAttr = HumanManager.inst().to_p_key_value_spList(targetProp, 0);
                List<Define.p_system_sp> humanSysAttr = humanObj.dataPers.unitPropPlus.to_p_system_spList();
                List<Define.p_system_sp> targetSysAttr = unitPropPlusMap.to_p_system_spList();
                MsgRole.role_sp_cmp_s2c.Builder msg = MsgRole.role_sp_cmp_s2c.newBuilder();
                msg.addAllMyAttr(humanAttr);
                msg.addAllTargetAttr(targetAttr);
                msg.addAllMySystemSp(humanSysAttr);
                msg.addAllTargetSystemSp(targetSysAttr);
                humanObj.sendMsg(msg);
            }
        } else {// 本服
            PropCalc propCalcEquipAll = new PropCalc();
            String json = Utils.getRedisStrValue(ArenaManager.inst().getRobotKey(humanObj, targetId));
            JSONObject jo = Utils.toJSONObject(json);
            String equipJson = jo.getString(ParamKey.equipKey);
            JSONArray ja = Utils.toJSONArray(equipJson);
            for (int i = 0; i < ja.size(); i++) {
                JSONObject eJo = ja.getJSONObject(i);
                PropCalc baseAttr = new PropCalc(eJo.getString(ParamKey.baseAttrKey));
                PropCalc randAttr = new PropCalc(eJo.getString(ParamKey.randAttrKey));
                propCalcEquipAll.plus(baseAttr);
                propCalcEquipAll.plus(randAttr);
            }
            PropCalc attr = new PropCalc(jo.getString(ParamKey.attrKey));
            UnitPropPlusMap unitPropPlusMap = new UnitPropPlusMap();
            unitPropPlusMap.unitPropPlus.setInitAttr(attr.toJSONStr());
            unitPropPlusMap.unitPropPlus.setEquip(propCalcEquipAll.toJSONStr());

            PropCalc targetProp = HumanManager.inst().getPropPlus(unitPropPlusMap, new ArrayList<>());
            PropCalc humanProp = humanObj.getPropPlus();
            List<Define.p_key_value> humanAttr = HumanManager.inst().to_p_key_value_spList(humanProp, 0);
            List<Define.p_key_value> targetAttr = HumanManager.inst().to_p_key_value_spList(targetProp, 0);
            List<Define.p_system_sp> humanSysAttr = humanObj.dataPers.unitPropPlus.to_p_system_spList();
            List<Define.p_system_sp> targetSysAttr = unitPropPlusMap.to_p_system_spList();
            MsgRole.role_sp_cmp_s2c.Builder msg = MsgRole.role_sp_cmp_s2c.newBuilder();
            msg.addAllMyAttr(humanAttr);
            msg.addAllTargetAttr(targetAttr);
            msg.addAllMySystemSp(humanSysAttr);
            msg.addAllTargetSystemSp(targetSysAttr);
            humanObj.sendMsg(msg);
        }
    }

    public Define.p_battle_role to_p_battle_role(Define.p_battle_role.Builder dInfo, Object... objs){
        if(objs != null && objs.length > 0){
            int objType = Utils.intValue(objs[0]);
            if(objType == ParamKey.objType_1){
                List<Define.p_key_value> attrListNew = new ArrayList<>();
                for(Define.p_key_value dAttr : dInfo.getAttrListList()){
                    boolean isUp = false;
                    for(int i = 1; i < objs.length; i+=2){
                        int propSn = Utils.intValue(objs[i]);
                        int propValue = Utils.intValue(objs[i + 1]);
                        if(dAttr.getK() == propSn){
                            isUp = true;
                            attrListNew.add(HumanManager.inst().to_p_key_value(propSn, propValue).build());
                            break;
                        }
                    }
                    if(!isUp){
                        attrListNew.add(dAttr);
                    }
                }
                dInfo.clearAttrList();
                dInfo.addAllAttrList(attrListNew);
            }else if(objType == ParamKey.objType_2){

            }
        }
        return dInfo.build();
    }

    /**
     * 用Human和Human2构造p_battle_role（处理蘑菇协议用的就是p_battle_role, 但其实只需要其中的外观数据的情况）
     */
    public Define.p_battle_role to_p_battle_role(Human human, Human2 human2) {
        // 设置装备
        Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(human2.getEquipFigureMap());
        List<Define.p_key_value> equipList = new ArrayList<>();
        equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_1, figureMap.getOrDefault(EquipInfo.weapon, EquipManager.FIGURE_DEFAULT)).build());
        equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_2, figureMap.getOrDefault(EquipInfo.hat, EquipManager.FIGURE_DEFAULT)).build());
        equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_3, figureMap.getOrDefault(EquipInfo.face, EquipManager.FIGURE_DEFAULT)).build());// 面饰
        equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_4, human2.getFateShow()).build());// 邪眼
        equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_5, human2.getWingUse()).build());// 翅膀

        // 设置皮肤
        List<Define.p_key_value> skinList = new ArrayList<>();
        skinList.add(HumanManager.inst().to_p_key_value(2, human2.getCurrentSkin()).build());

        // 外观
        Define.p_role_figure.Builder dFigure = Define.p_role_figure.newBuilder();
        dFigure.addAllEquipList(equipList);
        dFigure.setHairFigure(human2.getHairColor());// 发色
        dFigure.setJobFigure(human.getJobModel());// 职业模型
        dFigure.setMountFigure(human2.getMountUse());// 坐骑
        dFigure.setArtifactFigure(human2.getArtifactUse());// 神器sn
        dFigure.setGender(1);// 无性别 默认1
        dFigure.addAllSkinList(skinList);// 皮肤
        dFigure.setCurrentTitle(human.getCurrentTitleSn());// 头衔

        // 头衔
        Define.p_head.Builder head = Define.p_head.newBuilder();
        head.setId(human.getHeadSn());
        head.setFrameId(human.getCurrentHeadFrameSn());
        head.setUrl("");

        Define.p_battle_role.Builder dInfo = Define.p_battle_role.newBuilder();
        dInfo.setId(human.getId());
        dInfo.setName(human.getName());
        dInfo.setLev(human.getLevel());
        dInfo.setManualOperator(Utils.getTimeSec());
        dInfo.setFigure(dFigure);
        dInfo.setHead(head);
        dInfo.addAllExt(to_p_key_value_AngelSkill(human2));
        return dInfo.build();
    }
    public List<Define.p_key_value> to_p_key_value_AngelSkill(Human2 human2){
        List<Define.p_key_value> list = new ArrayList<>();
        list.add(to_p_key_value(ParamKey.extPosType_9, getAngelSkill1(human2)).build());
        list.add(to_p_key_value(ParamKey.extPosType_10, human2.getAngelSkill2()).build());
        list.add(to_p_key_value(ParamKey.extPosType_11, human2.getAngelSkill3()).build());
        return list;
    }
    public int getAngelSkill1(Human2 human2){
        int angelStar = human2.getAngelStar1();
        if(angelStar == 0){
            return 0;
        }
        int Sn = angelStar / 100;
        int star = angelStar % 100;
        ConfAngelStar_0 conf = ConfAngelStar_0.get(Sn, star);
        if(conf == null || conf.battle_skill1 == null || conf.battle_skill1.length == 0){
            return 0;
        }
        return conf.battle_skill1[0][0];
    }

    public long[] getFlyPetIdSn(Human2 human2){
        long[] flyPetIdSn = new long[2];
        Map<Integer, FlyPetInfo> petMap = FlyPetManager.inst().getPlanPetBriefMap(human2);
        FlyPetInfo petInfo = petMap.get(human2.getUseFlyPetLineup());
        if (petInfo != null) {
            flyPetIdSn[0] = petInfo.id;
            flyPetIdSn[1] = petInfo.sn;
            return flyPetIdSn;
        }
        return flyPetIdSn;
    }

    public void _msg_ad_wheel_info_c2s(HumanObject humanObj) {
        MsgAd.ad_wheel_info_s2c.Builder msg = MsgAd.ad_wheel_info_s2c.newBuilder();
        msg.setCd(humanObj.getHumanExtInfo().getWheelNextTime());
        int maxNum = ConfGlobal.get(ConfGlobalKey.daily_turntable_times.SN).value;
        msg.setNum(maxNum-humanObj.getHumanExtInfo().getWheelNum());
        humanObj.sendMsg(msg);
    }

    public void _msg_ad_wheel_spin_c2s(HumanObject humanObj) {
        if(!humanObj.isModUnlock(32)){
            return;
        }
        if (humanObj.getHumanExtInfo().getWheelNum() >= ConfGlobal.get(ConfGlobalKey.daily_turntable_times.SN).value) {
            return;
        }
        if(humanObj.getHumanExtInfo().getWheelNextTime() > (int)(Port.getTime()/Time.SEC)){
            return;
        }
        humanObj.getHumanExtInfo().setWheelNum(humanObj.getHumanExtInfo().getWheelNum() + 1);
        humanObj.getHumanExtInfo().setWheelNextTime((int)(Port.getTime()/Time.SEC) + ConfGlobal.get(ConfGlobalKey.box_delay_time.SN).value);
//        humanObj.getHumanExtInfo().update();
        Collection<ConfTurntable> confList = ConfTurntable.findAll();
        if(confList.isEmpty()){
            Log.game.error("===ConfTurntable表为空");
            return;
        }
        int i = 0;
        int[] weights = new int[confList.size()];
        for (ConfTurntable confTurntable : confList) {
            weights[i] = confTurntable.weight;
            i++;
        }
        int index = Utils.randomByWeight(weights);
        ConfTurntable conf = confList.toArray(new ConfTurntable[0])[index];
        ProduceManager.inst().produceAdd(humanObj, conf.reward, MoneyItemLogKey.转盘);
        _msg_ad_wheel_info_c2s(humanObj);
        MsgAd.ad_wheel_spin_s2c.Builder msg = MsgAd.ad_wheel_spin_s2c.newBuilder();
        msg.setId(conf.sn);
        humanObj.sendMsg(msg);
    }

    public void role_seven_login_info_c2s(HumanObject humanObj) {
        List<Integer> loginList = Utils.strToIntList(humanObj.getHumanExtInfo().getLogin7Day());
        MsgRole.role_seven_login_info_s2c.Builder msg = MsgRole.role_seven_login_info_s2c.newBuilder();
        msg.setDay(loginList.size());
        for (int i = 0; i < loginList.size(); i++) {
            if(loginList.get(i) == 2){
                msg.addGetDay(i+1);
            }
        }
        msg.setIfDayFirst(loginList.size() == 1 ? 1 : 0);
        humanObj.sendMsg(msg);
    }

    public void role_seven_login_reward_c2s(HumanObject humanObj, int day) {
        if(!humanObj.isModUnlock(FuncOpenType.FUNC_SEVEN_DAY)){
            return;
        }
        List<Integer> loginList = Utils.strToIntList(humanObj.getHumanExtInfo().getLogin7Day());
        if(loginList.size() < day){
            return;
        }
        if(loginList.get(day-1) == 2){
            return;
        }
        ConfSevenlogin conf = ConfSevenlogin.get(day);
        if(conf == null){
            return;
        }
        loginList.set(day-1, 2);
        humanObj.getHumanExtInfo().setLogin7Day(Utils.listToString(loginList));
//        humanObj.getHumanExtInfo().update();
        ProduceManager.inst().produceAdd(humanObj,conf.goods, MoneyItemLogKey.七日登录);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj,InstanceConstants.showType_0,conf.goods);
        MsgRole.role_seven_login_reward_s2c.Builder msg = MsgRole.role_seven_login_reward_s2c.newBuilder();
        for (int i = 0; i < loginList.size(); i++) {
            if(loginList.get(i) == 2){
                msg.addGetDay(i+1);
            }
        }
        humanObj.sendMsg(msg);
    }

    public String getServerId(long roleId, String serverId){
        Human human = (Human)EntityManager.getEntity(Human.class, roleId);
        if(human == null){
            Log.temp.error("角色不存在！roleId={}", roleId);
            return "";
        } else {
            int serverTagId = Util.getServerTagId(human.getServerId());
            if(serverTagId == 0){
                Log.temp.error("角色不在当前服！roleId={}, serverId={}, serverTagId={}", roleId, human.getServerId(), serverTagId);
            } else {
                if(serverTagId > 0 && serverTagId != Utils.intValue(serverId)){
                    Log.temp.error("角色不在当前服！roleId={}, serverId={}, serverTagId={}, serverId={}", roleId, human.getServerId(), serverTagId, serverId);
                    serverId = String.valueOf(serverTagId);
                }
            }
        }

        return serverId;
    }

    /**
     * 更新玩家红点记录
     * @param humanObj      玩家对象
     * @param system        系统模块
     * @param moudel        系统对应的功能
     * @param redNum        红点数量
     */
    public void updateSystemModuleRedNum(HumanObject humanObj, int system, int moudel, int redNum) {
        HumanExtInfo humanExtInfo = humanObj.getHumanExtInfo();
        Map<Integer, Map<Integer, Integer>> redMap = Utils.jsonToIntMapIntInt(humanExtInfo.getModuleRedPoinJSON());
        Map<Integer, Integer> systemRedMap = redMap.computeIfAbsent(system, k -> new HashMap<>());
        systemRedMap.put(moudel, redNum);
        humanExtInfo.setModuleRedPoinJSON(Utils.mapIntMapIntIntToJSON(redMap));
        humanExtInfo.update();

        MsgRole.role_red_point_change_s2c.Builder msg = MsgRole.role_red_point_change_s2c.newBuilder();
        msg.setRedPointInfo(to_p_red_point(systemRedMap, system, new int[] { moudel }));
        humanObj.sendMsg(msg);
    }


    public void sendMsg_role_red_point_s2c(HumanObject humanObj) {
        MsgRole.role_red_point_s2c.Builder msg = MsgRole.role_red_point_s2c.newBuilder();
        Map<Integer, Map<Integer, Integer>> redMap = Utils.jsonToIntMapIntInt(humanObj.getHumanExtInfo().getModuleRedPoinJSON());
        for (Integer system : redMap.keySet()) {
            if (!redMap.containsKey(system)) {
                continue;
            }
            Map<Integer, Integer> redPointNumMap = redMap.get(system);
            int[] modules = redPointNumMap.keySet().stream().mapToInt(Integer::intValue).toArray();
            if (modules.length <= 0) {
                continue;
            }
            msg.addRedPointList(to_p_red_point(redPointNumMap, system, modules));
        }
        humanObj.sendMsg(msg);
    }

    public Define.p_red_point to_p_red_point(Map<Integer, Integer> redPointNumMap, int system, int[] modules) {
        Define.p_red_point.Builder pRedPoint = Define.p_red_point.newBuilder();
        pRedPoint.setSystem(system);
        for (int i = 0; i < modules.length; i++) {
            int module = modules[i];
            pRedPoint.addDetailList(to_p_red_point_detail(module, redPointNumMap.getOrDefault(module, 0)));
        }
        return pRedPoint.build();
    }

    private Define.p_red_point_detail to_p_red_point_detail(int module, int num) {
        Define.p_red_point_detail.Builder pRedPointDetail = Define.p_red_point_detail.newBuilder();
        pRedPointDetail.setModule(module);
        pRedPointDetail.setNum(num);
        return pRedPointDetail.build();
    }

    /** 返回全服邮件领取标记 **/
    public static String getFillMailReceivedKey(long humanId){
        return RedisKeys.humanIdFillMailIdList + humanId;
    }

    public void _msg_solo_video_c2s(HumanObject humanObj, long vid, int source) {
        ArenaManager.inst().sendHistoryVideo(humanObj, vid, ParamKey.historyType_solo);
    }

    public void checkCrossSyns(HumanObject humanObj, String... fields){
        if(!HumanManager.inst().isSyncCrossHumanBrief(humanObj)){
            return;
        }
        HumanBrief brief = getHumanBrief(humanObj, true);
        if(brief == null){
            return;
        }
        String redisKey = "HumanBrief." + brief.getId();
        JsonObject obj = new JsonObject();
        if(humanObj.isCombatUp){
            humanObj.isCombatUp = false;
            // topCombat改为玩家当前战力
            obj.put(HumanBrief.K.topCombat, humanObj.getHuman().getCombat());
        }
        obj.put(HumanBrief.K.id, humanObj.id);
        for(String field : fields){
            switch (field){
                case HumanBrief.K.name:
                    obj.put(HumanBrief.K.name, brief.getName());
                    break;
                case HumanBrief.K.level:
                    obj.put(HumanBrief.K.level, brief.getLevel());
                    break;
                case HumanBrief.K.serverId:
                    obj.put(HumanBrief.K.serverId, brief.getServerId());
                    break;
                case HumanBrief.K.headSn:
                    obj.put(HumanBrief.K.headSn, brief.getHeadSn());
                    break;
                case HumanBrief.K.currentHeadFrameSn:
                    obj.put(HumanBrief.K.currentHeadFrameSn, brief.getCurrentHeadFrameSn());
                    break;
                case HumanBrief.K.jobSn:
                    obj.put(HumanBrief.K.jobSn, brief.getJobSn());
                    break;
                case HumanBrief.K.currentTitleSn:
                    obj.put(HumanBrief.K.currentTitleSn, brief.getCurrentTitleSn());
                    obj.put(HumanBrief.K.roleFigure, brief.getRoleFigure());
                    break;
                case HumanBrief.K.guildId:
                    obj.put(HumanBrief.K.guildId, brief.getGuildId());
                    break;
                case HumanBrief.K.guildName:
                    obj.put(HumanBrief.K.guildName, brief.getGuildName());
                    break;
                case HumanBrief.K.topCombat:
                    obj.put(HumanBrief.K.topCombat, brief.getTopCombat());
                    obj.put(HumanBrief.K.battleRole, brief.getBattleRole());
                    break;
                case HumanBrief.K.battleRole:
                    obj.put(HumanBrief.K.battleRole, brief.getBattleRole());
                    break;
                case HumanBrief.K.roleFigure:
                    obj.put(HumanBrief.K.roleFigure, brief.getRoleFigure());
                    break;
                case HumanBrief.K.battleRole2:
                    obj.put(HumanBrief.K.battleRole2, brief.getBattleRole2());
                    break;
                case HumanBrief.K.battleRole3:
                    obj.put(HumanBrief.K.battleRole3, brief.getBattleRole3());
                    break;
                case HumanBrief.K.battleRole4:
                    obj.put(HumanBrief.K.battleRole4, brief.getBattleRole4());
                    break;
                case HumanBrief.K.battleRole5:
                    obj.put(HumanBrief.K.battleRole5, brief.getBattleRole5());
                    break;
                default:
                    Log.temp.error("===未知字段：{}", field);
                    break;
            }
        }
        CrossRedis.setHashJsonObject(redisKey, obj, h -> {
            if (h.succeeded()) {
                Boolean result = h.result();
                if(!result){
                    LogCore.core.error("===result={}, key={}, json={}", result, redisKey, obj);
                }
            }
        });
    }

    public HumanBriefVO syncHumanBriefLeague(HumanObject humanObj){
        if(!humanObj.isModUnlock(64)){
			return null;
		}
        HumanBriefVO vo = new HumanBriefVO(humanObj);
        return vo;
    }

    public void syncHumanBriefLeague(HumanBriefVO vo){
        if(vo == null){
            return;
        }
        try {
            CrossManager.getInstance().callCrossFunc(CrossType.cross_chaos_battle, Config.SERVER_ID, res -> {
                try {
                    if(res.failed()){
                        Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                        return;
                    }
                    CrossPoint result = res.result();
                    GuildLeagueServiceProxy proxy = GuildLeagueServiceProxy.newInstance(result.getNodeId(), D.SERV_GUILD_LEAGUE);
                    proxy.updateHumanBrief(vo);
                } catch (Exception e) {
                    Log.temp.error("==跨服链接出问题 ", e);
                }
            });
        } catch (Exception e) {
            Log.temp.error("==跨服链接出问题 ", e);
        }
    }


    public void loadHumanIdSyncCross(List<Long> idList){
        loadHumanIdSyncCross(idList, res->{});
    }

    public void loadHumanIdSyncCross(List<Long> idList, Handler<AsyncResult<List<HumanBrief>>> onComplete){
        loadHumanIdSyncCross(idList, true, true, onComplete);
    }

    public void loadHumanIdSyncCross(List<Long> idList, boolean isSyncCross, boolean isPersist){
        loadHumanIdSyncCross(idList, isSyncCross, isPersist, res->{});
    }

    public void loadHumanIdSyncCross(List<Long> idList, boolean isSyncCross, boolean isPersist, Handler<AsyncResult<List<HumanBrief>>> onComplete) {
        Port port = Port.getCurrent();
        if(idList == null || idList.isEmpty()){
            AsyncActionResult.success(port, onComplete, null);
            return;
        }
        try {
            HumanData.getList(idList, HumanManager.inst().humanClasses, ret2 -> {
                if (ret2.failed()) {
                    AsyncActionResult.fail(port, onComplete, ret2.cause());
                    return;
                }
                List<HumanData> dataList = ret2.result();
                List<HumanBrief> humanBriefList = new ArrayList<>();
                for (HumanData humanData : dataList) {
                    if (humanData == null || humanData.human == null || humanData.human2 == null) {
                        continue;
                    }
                    try {
                        HumanBrief brief = createHumanBrief(humanData, isPersist);
                        humanBriefList.add(brief);
                    } catch (Exception e) {
                        Log.temp.info("==同步数据出问题 {}", e);
                    }
                }
                if (!isSyncCross) {
                    AsyncActionResult.success(port, onComplete, humanBriefList);
                    return;
                }
                if(humanBriefList.isEmpty()){
                    AsyncActionResult.success(port, onComplete, humanBriefList);
                    return;
                }
                try {
                    CrossManager.getInstance().callCrossFunc(CrossType.cross_chaos_battle, Config.SERVER_ID, res -> {
                        try {
                            if (res.failed()) {
                                Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                                return;
                            }
                            CrossPoint result = res.result();
                            String worldNodeId = result.getNodeId();
                            RemoteNode rn = Port.getCurrent().getNode().getRemoteNode(worldNodeId);
                            if(rn == null) {
                                Log.crossWar.warn("===GuildLeagueServiceProxy updateHumanBriefList. worldNodeId={}未连接", worldNodeId);
                                return;
                            }
                            GuildLeagueServiceProxy proxy = GuildLeagueServiceProxy.newInstance(worldNodeId, D.SERV_GUILD_LEAGUE);
                            proxy.updateHumanBriefList(humanBriefList);
                        } catch (Exception e) {
                            Log.temp.error("==跨服链接出问题 ", e);
                        }
                    });
                    List<Request> reqList = new ArrayList<>();
                    for (HumanBrief brief : humanBriefList) {
                        List<Object> list = new ArrayList<>();
                        list.add(HumanBrief.getRedisKeyStr(brief.getId()));
                        list.addAll(brief.getKeyValueList());
                        Request req = Request.cmd(Command.HSET, list.toArray());
                        reqList.add(req);
                        Request reqTtl = Request.cmd(Command.EXPIRE, HumanBrief.getRedisKeyStr(brief.getId()), Time.DAY_7_SEC);
                        reqList.add(reqTtl);
                    }
                    RedisTools.batch(CrossRedis.redis, reqList, h -> {
                        if(h.failed()){
                            Log.temp.error("==同步数据出问题 {} {}", reqList, h.cause());
                            return;
                        }
                    });
                    AsyncActionResult.success(port, onComplete, humanBriefList);
                } catch (Exception e) {
                    Log.temp.error("====跨服获取数据出问题", e);
                }
            });
        } catch (Exception e) {
            Log.temp.info("==同步数据出问题 {}", e);
        }
    }

    public HumanBrief createHumanBrief(HumanData humanData, boolean isPersist){
        HumanBrief brief = new HumanBrief();
        brief.setId(humanData.human.getId());
        brief.setJobSn(humanData.profession.getJobSn());
        brief.setLevel(humanData.human.getLevel());
        brief.setHeadSn(humanData.human.getHeadSn());
        brief.setServerId(humanData.human.getServerId());
        brief.setCurrentTitleSn(humanData.human.getCurrentTitleSn());
        brief.setCurrentHeadFrameSn(humanData.human.getCurrentHeadFrameSn());
        brief.setWingSkillSnLv(humanData.human2.getWingSkillSnLv());
        brief.setMountSkillSnLv(humanData.human2.getMountSkillSnLv());
        brief.setArtifactSkillSnLv(humanData.human2.getArtifactSkillSnLv());

        TeamMember teamMember = new TeamMember(humanData);
        brief.setBattleRole(teamMember.p_battle.toByteArray());
        brief.setRoleFigure(teamMember.dFigure.toByteArray());

        brief.setGuildId(humanData.human2.getGuildId());

        brief.setTopCombat(humanData.human.getCombat());
        brief.setGuildName(Utils.removeEmojis(humanData.human2.getGuildName()));
        brief.setName(Utils.removeEmojis(humanData.human.getName()));
        if(humanData.charm != null){
            brief.setCharmValue(humanData.charm.getCharmValue());
            brief.setShowMedalList(humanData.charm.getShowMedalList());
        }
        if(isPersist){
            brief.persist();
        }
        return brief;
    }

    /**
     * 伙伴属性公式
     * @param propCalc
     * @param petSnList
     * @return
     */
    public List<Define.p_key_value> petAttList(PropCalc propCalc, List<Integer> petSnList){
        List<Define.p_key_value> attList = new ArrayList<>();
        for (Map.Entry<Integer, BigDecimal> entry : propCalc.getDatas().entrySet()) {
            int key = entry.getKey();
            // 1001、1002、1003、1024转换成最终属性1、2、3、24
            ConfAttribute conf = ConfAttribute.get(key);
            if(conf == null){
                Log.temp.error("==配置属性不存在 key={}", key);
                continue;
            }
            if(conf.type != ParamKey.attrType_4){
                continue;
            }
            attList.add(HumanManager.inst().to_p_key_value(conf.group, entry.getValue().longValue()).build());
        }

        return attList;
    }

    public void syncHumanBriefCross(HumanBriefVO vo, CrossType crossType){
        if(vo == null){
            return;
        }
        if(crossType == null){
            return;
        }
        if(crossType == CrossType.cross_arena_rank){
            syncHumanBriefArena(vo);
        } else if(crossType == CrossType.cross_arena_region_rank){
            syncHumanBriefArenaRanked(vo);
        } else if(crossType == CrossType.cross_chaos_battle){
            syncHumanBriefLeague(vo);
        }

    }

    public void syncHumanBriefArena(HumanBriefVO vo){
        if(vo == null){
            return;
        }
        try {
            CrossManager.getInstance().callCrossFunc(CrossType.cross_arena_rank, Config.SERVER_ID, res -> {
                try {
                    if(res.failed()){
                        Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                        return;
                    }
                    CrossPoint result = res.result();
                    ArenaCrossServiceProxy proxy = ArenaCrossServiceProxy.newInstance(result.getNodeId());
                    proxy.updateHumanBrief(vo);
                } catch (Exception e) {
                    Log.temp.error("==跨服链接出问题 ", e);
                }
            });
        } catch (Exception e) {
            Log.temp.error("==跨服链接出问题 ", e);
        }
    }


    public void syncHumanBriefArenaRanked(HumanBriefVO vo){
        if(vo == null){
            return;
        }
        try {
            CrossManager.getInstance().callCrossFunc(CrossType.cross_arena_region_rank, Config.SERVER_ID, res -> {
                try {
                    if(res.failed()){
                        Log.temp.error("==跨服获取数据出问题 {}", res.cause());
                        return;
                    }
                    CrossPoint result = res.result();
                    ArenaRankedServiceProxy proxy = ArenaRankedServiceProxy.newInstance(result.getNodeId());
                    proxy.updateHumanBrief(vo);
                } catch (Exception e) {
                    Log.temp.error("==跨服链接出问题 ", e);
                }
            });
        } catch (Exception e) {
            Log.temp.error("==跨服链接出问题 ", e);
        }
    }

    /**
     * 根据玩家ID列表获取有效的HumanBrief列表
     */
    private void getValidHumanBriefs(List<Long> humanIdList, Handler<AsyncResult<List<HumanBrief>>> onComplete) {
        Port port = Port.getCurrent();
        if(humanIdList == null || humanIdList.isEmpty()) {
            AsyncActionResult.success(port, onComplete, new ArrayList<>());
            return;
        }

        // 先批量获取HumanBrief
        EntityManager.batchGetEntity(HumanBrief.class, humanIdList, res -> {
            if(res.failed()) {
                AsyncActionResult.fail(port, onComplete, res.cause());
                return;
            }

            List<HumanBrief> validBriefs = new ArrayList<>();
            List<Long> missingIds = new ArrayList<>();

            // 检查每个HumanBrief是否有效
            List<HumanBrief> briefs = res.result();
            for (HumanBrief brief : briefs) {
                if (brief != null && brief.getRoleFigure().length > 0 && brief.getBattleRole().length > 0) {
                    validBriefs.add(brief);
                }
            }
            for (Long id : humanIdList) {
                boolean isExist = false;
                for (HumanBrief brief : validBriefs) {
                    if (brief.getId() == id) {
                        isExist = true;
                        break;
                    }
                }
                if (!isExist) {
                    missingIds.add(id);
                }
            }

            if(missingIds.isEmpty()) {
                AsyncActionResult.success(port, onComplete, validBriefs);
                return;
            }

            // 对缺失的ID批量获取HumanData
            HumanData.getList(missingIds, HumanManager.inst().humanClasses, dataRes -> {
                if(dataRes.failed()) {
                    AsyncActionResult.success(port, onComplete, validBriefs);
                    return;
                }

                // 从HumanData创建HumanBrief
                for(HumanData data : dataRes.result()) {
                    if(data != null && data.human != null && data.human2 != null) {
                        try {
                            HumanBrief brief = HumanManager.inst().createHumanBrief(data, true);
                            if(brief != null) {
                                validBriefs.add(brief);
                            }
                        } catch(Exception e) {
                            Log.temp.error("创建HumanBrief失败, humanId={}", data.human.getId(), e);
                        }
                    }
                }

                AsyncActionResult.success(port, onComplete, validBriefs);
            });
        });
    }

    /**
     * 根据玩家排名获取前后范围的随机玩家简要信息
     * @param humanId 玩家对象
     * @param num 需要获取的玩家数量
     * @param rankMin 向前取多少名
     * @param rankMax 向后取多少名
     * @param onComplete 完成回调
     */
    public void getHumanBriefByRankRange(long humanId, int num, int rankMin, int rankMax,
                                         Handler<AsyncResult<List<HumanBrief>>> onComplete) {
        String key = RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeLevel_1001, Utils.getServerIdByHumanId(humanId));

        Port port = Port.getCurrent();
        // 获取玩家排名
        RedisTools.getMyRank(EntityManager.redisClient, key, String.valueOf(humanId), rankRes -> {
            if(!rankRes.succeeded()) {
                AsyncActionResult.fail(port, onComplete, rankRes.cause());
                return;
            }

            int myRank = Utils.intValue(rankRes.result());

            // 计算排名范围
            int startRank = Math.max(0, myRank - rankMin);
            int endRank = myRank + rankMax;

            // 获取范围内玩家ID
            RedisTools.getRankListByIndex(EntityManager.redisClient, key, startRank, endRank, false, ret -> {
                if(!ret.succeeded()) {
                    AsyncActionResult.fail(port, onComplete, ret.cause());
                    return;
                }

                // 收集玩家ID并排除自己
                List<Long> humanIdList = new ArrayList<>();
                JsonArray json = ret.result();
                for(int i = 0; i < json.size(); i++) {
                    long id = Utils.longValue(json.getValue(i));
                    if(id != humanId) {
                        humanIdList.add(id);
                    }
                }

                if(humanIdList.isEmpty()) {
                    AsyncActionResult.success(port, onComplete, new ArrayList<>());
                    return;
                }

                // 随机打乱并限制数量
                Collections.shuffle(humanIdList);
                humanIdList = humanIdList.subList(0, Math.min(num, humanIdList.size()));

                // 获取有效的HumanBrief列表
                getValidHumanBriefs(humanIdList, onComplete);
            });
        });
    }

    /**
     * 检查并删除过期皮肤
     */
    public void checkAndDelOutTimeSkin(HumanObject humanObj) {
        String skinListJson = humanObj.getHumanExtInfo().getSkinList();
        if (Utils.isNullOrEmptyJSONString(skinListJson) || "[\"[9,2,1]\"]".equals(skinListJson)) {
            return;// 只有默认皮肤的时候也不检测了
        }
        // 1.收集过期皮肤
        long currTime = Port.getTime();
        List<SkinVo> delSkinVoList = new ArrayList<>();
        List<SkinVo> skinVoList = SkinVo.fromJSONArrayStrToList(skinListJson);
        for (SkinVo skinVo : skinVoList) {
            if (skinVo.endTime == 0) {
                // 永久皮肤过滤掉
                continue;
            }
            if (currTime >= skinVo.endTime) {
                delSkinVoList.add(skinVo);
            }
        }
        if (delSkinVoList.isEmpty()) {
        	return;
        }
        // 2.删除过期皮肤，判断是否穿戴过期皮肤，如果是，则将其卸下
        skinVoList.removeAll(delSkinVoList);
        humanObj.getHumanExtInfo().setSkinList(SkinVo.listToJSONArrayStr(skinVoList));
        for (SkinVo skinVo : delSkinVoList) {
            int skinSn = skinVo.sn;
            ConfSkin confSkin = ConfSkin.get(skinSn);
            if (confSkin == null) {
                Log.game.error("===皮肤不存在ConfSkin，skinId={}", skinSn);
                continue;
            }
            if (confSkin.type == 1 && skinSn == humanObj.getHuman2().getHairColor()) {
                humanObj.getHuman2().setHairColor(0);
                MsgRole.role_change_skin_s2c.Builder msg = MsgRole.role_change_skin_s2c.newBuilder();
                msg.setFigure(to_p_key_value(1, 0));
                humanObj.sendMsg(msg.build());
            } else if (confSkin.type == 2 && skinSn == humanObj.getHuman2().getCurrentSkin()) {
                humanObj.getHuman2().setCurrentSkin(0);
                MsgRole.role_change_skin_s2c.Builder msg = MsgRole.role_change_skin_s2c.newBuilder();
                msg.setFigure(to_p_key_value(2, 0));
                humanObj.sendMsg(msg.build());
            }
        }
        handleRoleSkinListC2S(humanObj);
        // 3.更新战力
        updateSkinPorpCalcPower(humanObj);
        HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure);
    }

    public void _msg_server_merge_list_c2s(HumanObject humanObj) {
        List<Integer> serverIdList = Util.getMergeServerIdList(Config.SERVER_ID);
        int serverPrefix = NumberUtils.isNumber(Config.GAME_SERVER_PREFIX) ? Integer.parseInt(Config.GAME_SERVER_PREFIX) : 0;
        MsgSystem.server_merge_list_s2c.Builder msg = MsgSystem.server_merge_list_s2c.newBuilder();
        for (Integer serverId : serverIdList) {
            int serverIdShort = serverId - serverPrefix;
            msg.addServerList(serverIdShort);
        }
        int originalServerId = humanObj.getHuman().getServerId();
        long timeNow = Port.getTime();
        ServerMerge serverMerge = null;
        Map<Integer, ServerMerge> confMap = GlobalConfVal.getServerMergeMap();
        for (ServerMerge merge : confMap.values()) {
            if (merge.validTime > timeNow || merge.invalidTime < timeNow) {
                continue;
            }
            if (!merge.serverSets.contains(Utils.getServerIdTo(originalServerId))) {
                continue;
            }
            serverMerge = merge;
        }
        if (serverMerge != null) {
            msg.addAllActMergeIdList(serverMerge.activityTypeSets);
            msg.setActCloseTime1(serverMerge.mergeStartTime);
            msg.setActCloseTime2(serverMerge.mergeEndTime);
        }
        humanObj.sendMsg(msg);
    }

    public void _msg_server_cross_join_c2s(HumanObject humanObj, int crossTypeValue) {
        CrossType crossType = CrossType.valueOf(crossTypeValue);
        if (crossType == null) {
            Log.game.error("跨服类型不存在, value={}", crossTypeValue);
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }
        // 跨服活动
        CrossManager.getInstance().callCrossFunc(crossType, Config.SERVER_ID, res -> {
            if (res.failed()) {
                Log.temp.error("==跨服获取数据出问题", res.cause());
                return;
            }
            CrossPoint result = res.result();
            int group = Utils.intValue(result.getGroupId());
            ConfServerGroup conf = ConfServerGroup.get(group);
            if (conf == null) {
                Log.temp.error("==ConfServerGroup找不到组配置 {}", group);
                return;
            }
            MsgSystem.server_cross_join_s2c.Builder msg = MsgSystem.server_cross_join_s2c.newBuilder();
            msg.addAllServerList(StrUtil.parseRangeCfg(conf.sever_range));
            humanObj.sendMsg(msg);
        });
    }

    @Listener(EventKey.UPDATE_NEWS)
    public void listenUpdateNews(Param param) {
        HumanObject humanObj = param.get("humanObj");
        int newsCondition = param.get("newsCondition");
        int value = param.get("value");

        Map<Integer, Long> settingMap = Utils.jsonToMapIntLong(humanObj.getHumanExtInfo().getSettingMap());
        boolean isHide = settingMap.getOrDefault(HumanSettingConstants.BroadCastHideName, 0L) == 1L;
        GameServiceProxy.newInstance().checkAndSendNews(humanObj.getHumanId(), humanObj.name, Util.getServerIdByHumanId(humanObj.getHumanId()), newsCondition, value, isHide);
    }
}