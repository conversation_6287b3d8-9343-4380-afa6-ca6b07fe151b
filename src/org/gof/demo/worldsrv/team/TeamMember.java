package org.gof.demo.worldsrv.team;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Param;
import org.gof.core.support.ParamKey;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.angel.AngelManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.equip.EquipInfo;
import org.gof.demo.worldsrv.equip.EquipManager;
import org.gof.demo.worldsrv.flyPet.FlyPetManager;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.PlanVo;
import org.gof.demo.worldsrv.human.RoleInfoKey;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.pet.PetManager;
import org.gof.demo.worldsrv.relic.RelicTabVo;
import org.gof.demo.worldsrv.relic.ReliceData;
import org.gof.demo.worldsrv.support.StringZipUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.gof.demo.worldsrv.equip.EquipInfo.weapon;

public class TeamMember implements ISerilizable {
    public long humanId;
    public Human human;
    public Human2 human2;
    public Profession profession;
    public Artifact atf;
    public Mount mount;
    public Wing wing;
    public Relic relic;
    public Fate fate;
    public Equip equip;
    public UnitPropPlus unitPropPlus;
    public Charm charm;

    public boolean isRobot = false;// 默认是机器人，false是机器人
    public boolean isCopy = true;
    public boolean isReady;
    public String json;
    public int nowDiff;// 当前难度
    public Param param;
    public List<Define.p_role_change> infoList = new ArrayList<>();

    public Define.p_battle_role p_battle = null;
    public List<Define.p_active_skill> activeSkills = new ArrayList<>();
    public List<Define.p_passive_skill> passiveSkills = new ArrayList<>();

    public List<Define.p_equip> equips = new ArrayList<>();
    public Define.p_role_figure dFigure = null;
    public List<Integer> functionList = new ArrayList<>();
    public List<Define.p_role_pet> petList = new ArrayList<>();
    public List<Define.p_key_value> equipList = new ArrayList<>();
    public List<Define.p_key_value> skinList = new ArrayList<>();
    public List<Define.p_key_value> attrList = new ArrayList<>();
    public List<Define.p_key_value> extList = new ArrayList<>();
    public List<Define.p_role_pet> usePetList = new ArrayList<>();

    public TeamMember() {

    }

    public Define.p_battle_role to_p_battle_role(List<Define.p_key_value> extList, Object... objs){
        Define.p_battle_role.Builder dInfo = Define.p_battle_role.newBuilder();
        dInfo.setId(human.getId());
        dInfo.setName(human.getName());
        dInfo.setLev(human.getLevel());
        dInfo.setJob(profession.getJobSn());
        Define.p_role_skill.Builder dSkill = Define.p_role_skill.newBuilder();
        dSkill.addAllActiveSkill(activeSkills);
        dSkill.addAllPassiveSkill(passiveSkills);
        dInfo.setRoleSkill(dSkill);

        dInfo.addAllPetList(petList);

        if(objs != null && objs.length > 0){
            objsUpdate(objs);
        }

        dInfo.addAllAttrList(attrList);

        List<Integer> activeSnList = new ArrayList<>();
        for(Define.p_active_skill dActive : activeSkills){
            activeSnList.add(dActive.getSkillId());
        }
        List<Integer> petSnList = new ArrayList<>();
        for(Define.p_role_pet dPet : petList){
            petSnList.add(dPet.getPetId());
        }

        PropCalc propCalc = new PropCalc();
        for(Define.p_key_value keyValue : attrList){
            propCalc.put(Utils.intValue(keyValue.getK()), BigDecimal.valueOf(keyValue.getV()));
        }
        List<Define.p_attr_obj_list> p_attr_objListAll = SkillManager.inst().to_p_attr_obj_list(passiveSkills,
                petSnList, activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));
		dInfo.addAllAttrObjList(p_attr_objListAll);// p_attr_obj_list

        Define.p_role_figure.Builder dFigur = Define.p_role_figure.newBuilder();
        dFigur.addAllEquipList(equipList);

        dFigur.setHairFigure(human2.getHairColor());// 发色
        dFigur.setJobFigure(human.getJobModel());
        // 坐骑
        dFigur.setMountFigure(human2.getMountUse());
        // 神器sn
        dFigur.setArtifactFigure(human2.getArtifactUse());
        dFigur.setGender(1);// 无性别 默认1
        // 皮肤
        dFigur.addAllSkinList(skinList);
        // 头衔
        dFigur.setCurrentTitle(human.getCurrentTitleSn());

        dInfo.setFigure(dFigur);

        dInfo.setManualOperator(Utils.getTimeSec());
//		dInfo.addAllOperators();//p_battle_operator
		dInfo.addAllExt(extList); //p_key_value
        dInfo.addAllExt(HumanManager.inst().to_p_key_value_AngelSkill(human2));

        Define.p_head.Builder head = Define.p_head.newBuilder();
        long headId = human.getHeadSn();
        head.setId(headId);

        head.setFrameId(human.getCurrentHeadFrameSn());
        head.setUrl("");
        dInfo.setHead(head);
        dInfo.setLeftHp(100);
        return dInfo.build();
    }

    private void objsUpdate(Object... objs){
        int objType = Utils.intValue(objs[0]);
        switch (objType){
            case ParamKey.objType_1:
                List<Define.p_key_value> attrListNew = new ArrayList<>();
                for(Define.p_key_value dInfo : attrList){
                    boolean isUp = false;
                    for(int i = 1; i < objs.length; i+=2){
                        int propSn = Utils.intValue(objs[i]);
                        int propValue = Utils.intValue(objs[i + 1]);
                        if(dInfo.getK() == propSn){
                            isUp = true;
                            attrListNew.add(HumanManager.inst().to_p_key_value(propSn, propValue).build());
                            break;
                        }
                    }
                    if(!isUp){
                        attrListNew.add(dInfo);
                    }
                }
                attrList = attrListNew;
                break;
            case ParamKey.objType_2:
                break;
            case ParamKey.objType_3:
                break;
            default:
                break;
        }
    }

    public TeamMember(HumanObject humanObj){
        this(humanObj, humanObj.getHumanExtInfo().getPlan());
    }

    public TeamMember(HumanObject humanObj, int planType) {
        human = humanObj.getHuman();
        human2 = humanObj.getHuman2();
        profession = humanObj.operation.profession;
        atf = humanObj.artifact.atf;
        mount = humanObj.operation.mount;
        wing = humanObj.operation.wing;
        relic = humanObj.relic.relic;
        fate = humanObj.fate.fate;
        equip = humanObj.operation.equipsMap.get(human2.getEquipTab());
        unitPropPlus = humanObj.dataPers.unitPropPlus.unitPropPlus;
        charm = humanObj.operation.charmData.charm;
        humanId = humanObj.id;
        isRobot = false;
        isCopy = false;
        activeSkills.clear();
        passiveSkills.clear();
        equips.clear();
        functionList.clear();
        petList.clear();
        equipList.clear();
        skinList.clear();
        attrList.clear();
        extList.clear();
        usePetList.clear();

        PlanVo planVo = humanObj.operation.planVoMap.get(planType);
        if(planVo == null){
            planVo = new PlanVo(planType);
        }
        // 主动技能
        this.activeSkills.addAll(SkillManager.inst().getAllActiveSkill(humanObj, planVo.getTab(PlanVo.TAB_SKILL)));
        // 被动技能
        this.passiveSkills.addAll(SkillManager.inst().getAllPassiveSkill(humanObj, planType));

        // TODO 差好多战斗数据

        Define.p_battle_role.Builder dInfo = Define.p_battle_role.newBuilder();
        dInfo.setId(human.getId());
        dInfo.setName(human.getName());
        dInfo.setLev(human.getLevel());
        dInfo.setJob(profession.getJobSn());
        Define.p_role_skill.Builder dSkill = Define.p_role_skill.newBuilder();
        dSkill.addAllActiveSkill(activeSkills);
        dSkill.addAllPassiveSkill(passiveSkills);
        dInfo.setRoleSkill(dSkill);

        int petTab = planVo.getTab(PlanVo.TAB_PET);

        this.usePetList.addAll(PetManager.inst().getP_role_petList(humanObj, petTab));
        int flyPetFightSn = FlyPetManager.inst().getFlyPetFightSn(human2, planType);
        if (flyPetFightSn != 0) {
            dInfo.addExt(Define.p_key_value.newBuilder().setK(ParamKey.extType_6).setV(flyPetFightSn).build());
        }
        int angelTab = planVo.getTab(PlanVo.TAB_ANGEL);
        this.extList.addAll(AngelManager.inst().to_p_key_value_AngelSkill(humanObj, angelTab));
        dInfo.addAllExt(this.extList);

        List<Integer> petSnList = new ArrayList<>();
        int petSn = human2.getTeamPetSn();
        if (petSn > 0) {
            Define.p_role_pet pRolePet = PetManager.inst().to_p_role_pet(humanObj, petSn, petTab);
            petList.add(pRolePet.toBuilder().setPetPos(1).build());
            petSnList.add(petSn);
        }
        if (petSn == 0 && humanObj.operation.petData.petSnLvMap.size() > 0 ) {//上阵默认宠物
            petSn = new ArrayList<>(humanObj.operation.petData.petSnLvMap.keySet()).get(0);
            Define.p_role_pet pRolePet = PetManager.inst().to_p_role_pet(humanObj, petSn, petTab);
            petList.add(pRolePet.toBuilder().setPetPos(1).build());
            petSnList.add(petSn);
        }
        dInfo.addAllPetList(petList);

        PropCalc propCalc;
        if(planType == humanObj.getHumanExtInfo().getPlan()){
            propCalc = humanObj.getPropPlus();
        }else {
            propCalc = humanObj.getPropPlus(planVo);
        }
        for (Map.Entry<Integer, BigDecimal> entry : propCalc.getDatas().entrySet()) {
            int key = entry.getKey();
            // 1001、1002、1003、1024转换成最终属性1、2、3、24
            int newKey = HumanManager.inst().getType(key);
            long value = HumanManager.inst().getCalculationValue(key, propCalc.getDatas());
            if(value == -1){
                // 1001、1002、1003、1024转换成最终属性1、2、3、24
                value = propCalc.getBigDecimal(newKey).longValue();
            }
            Define.p_key_value.Builder dattr = HumanManager.inst().to_p_key_value(key, value);
            attrList.add(dattr.build());
            dInfo.addAttrList(dattr);
        }
        List<Integer> activeSnList = new ArrayList<>();
        for(Define.p_active_skill dActive : activeSkills){
            activeSnList.add(dActive.getSkillId());
        }

        List<Define.p_attr_obj_list> p_attr_objListAll = SkillManager.inst().to_p_attr_obj_list(passiveSkills,
                petSnList, activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));

		dInfo.addAllAttrObjList(p_attr_objListAll);// p_attr_obj_list
        // TODO

        dInfo.setFigure(humanObj.to_p_role_figure(planType));
        dInfo.setManualOperator(Utils.getTimeSec());
//		dInfo.addAllOperators();//p_battle_operator
//		dInfo.addAllExt(); //p_key_value
        Define.p_head.Builder head = Define.p_head.newBuilder();
        head.setId(human.getHeadSn());
        head.setFrameId(human.getCurrentHeadFrameSn());
        head.setUrl("");
        dInfo.setHead(head);
        dInfo.setLeftHp(100);
        this.p_battle = dInfo.build();

        int tab = planVo.getTab(PlanVo.TAB_EQUIP);
        Equip equip = humanObj.operation.equipsMap.get(tab);
        if(equip != null){
            equips.clear();
            for (int i = weapon; i <= EquipInfo.max_part; ++i){
                String jsonString = EquipManager.inst().getEquipPart(equip,i);
                if(!Utils.isNullOrEmptyJSONString(jsonString)){
                    EquipInfo equipInfo = new EquipInfo();
                    equipInfo.fromJsonString(jsonString);
                    Define.p_equip.Builder p_equip = equipInfo.toProtoBuilder(tab);
                    equips.add(p_equip.build());
                }
            }
        }
        equipList();

        // 皮肤
        skinList.add(HumanManager.inst().to_p_key_value(2, human2.getCurrentSkin()).build());

        dFigure = humanObj.to_p_role_figure(planType);
        dInfo.setFigure(dFigure);
        functionList.addAll(humanObj.funcOpenList);
    }

    private void equipList(){
        Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(human2.getEquipFigureMap());
        //遍历weapon到face的部位不存在或者value=0就去默认取
        int weapon = figureMap.getOrDefault(EquipInfo.weapon, EquipManager.FIGURE_DEFAULT);
        equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_1, weapon).build());

        int hat = figureMap.getOrDefault(EquipInfo.hat, EquipManager.FIGURE_DEFAULT);
        equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_2, hat).build());

        int face = figureMap.getOrDefault(EquipInfo.face, EquipManager.FIGURE_DEFAULT);
        equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_3, face).build());

        equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_4, human2.getFateShow()).build());
        // 翅膀
        equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_5, human2.getWingUse()).build());
    }

    public TeamMember(HumanData humanData) {
        this(humanData, null);
    }

    public TeamMember(HumanData humanData, PropCalc propBonus) {
        this.human = humanData.human;
        this.human2 = humanData.human2;
        this.profession = humanData.profession;
        this.unitPropPlus = humanData.unitPropPlus;
        this.equip = humanData.equip;
        this.atf = humanData.artifact;
        this.mount = humanData.mount;
        this.relic = humanData.relic;
        this.charm =  humanData.charm;

        this.humanId = human.getId();
        this.isRobot = false;
        this.isReady = true;

        // 初始化列表
        this.activeSkills = new ArrayList<>();
        this.passiveSkills = new ArrayList<>();
        this.petList = new ArrayList<>();
        this.usePetList = new ArrayList<>();
        this.equipList = new ArrayList<>();
        this.equips = new ArrayList<>();
        this.skinList = new ArrayList<>();
        this.attrList = new ArrayList<>();
        this.extList = new ArrayList<>();

        // 设置技能
        this.activeSkills.addAll(SkillManager.inst().getAllActiveSkill(humanData.profession,humanData.human2));
        this.passiveSkills.addAll(SkillManager.inst().getAllPassiveSkill(humanData.human2,profession,atf,mount,relic));

        Define.p_battle_role.Builder dInfo = Define.p_battle_role.newBuilder();
        dInfo.setId(human.getId());
        dInfo.setName(human.getName());
        dInfo.setLev(human.getLevel());
        dInfo.setJob(profession.getJobSn());
        Define.p_role_skill.Builder dSkill = Define.p_role_skill.newBuilder();
        dSkill.addAllActiveSkill(activeSkills);
        dSkill.addAllPassiveSkill(passiveSkills);
        dInfo.setRoleSkill(dSkill);

        // 设置宠物
        this.usePetList.addAll(PetManager.inst().getP_role_petList(human2.getPetSnLvMap(), human2.getPetLineupJSON(), human2.getUsePetLineup(), human2.getPetSkinLineupJSON(), human2.getPetSkinSnLvMap()));
        int flyPetFightSn = FlyPetManager.inst().getFlyPetFightSn(human2, human2.getUseFlyPetLineup());
        if (flyPetFightSn != 0) {
            dInfo.addExt(Define.p_key_value.newBuilder().setK(ParamKey.extType_6).setV(flyPetFightSn).build());
        }
        dInfo.addAllExt(HumanManager.inst().to_p_key_value_AngelSkill(human2));
        int petSn = human2.getTeamPetSn();
        List<Integer> petSnList = new ArrayList<>();
        for (Define.p_role_pet pet : usePetList){
            if(pet.getPetId() == petSn){
                this.petList.add(pet);
            }
            petSnList.add(pet.getPetId());
        }
        dInfo.addAllPetList(usePetList);

        PropCalc propCalc = HumanManager.inst().getPropPlus(unitPropPlus, new ArrayList<>());
        if(propBonus != null && !propBonus.getDatas().isEmpty()){
            propCalc = propCalc.mul(propBonus);
            // 重新计算最终属性加成
            propCalc = PropManager.inst().recalculatePropPlus(propCalc);
        }
        for (Map.Entry<Integer, BigDecimal> entry : propCalc.getDatas().entrySet()) {
            int key = entry.getKey();
            // 1001、1002、1003、1024转换成最终属性1、2、3、24
            int newKey = HumanManager.inst().getType(key);
            long value = HumanManager.inst().getCalculationValue(key, propCalc.getDatas());
            if(value == -1){
                // 1001、1002、1003、1024转换成最终属性1、2、3、24
                value = propCalc.getBigDecimal(newKey).longValue();
            }
            Define.p_key_value.Builder dattr = HumanManager.inst().to_p_key_value(key, value);
            attrList.add(dattr.build());
            dInfo.addAttrList(dattr);
        }
        List<Integer> activeSnList = new ArrayList<>();
        for(Define.p_active_skill dActive : activeSkills){
            activeSnList.add(dActive.getSkillId());
        }

        List<Define.p_attr_obj_list> p_attr_objListAll = SkillManager.inst().to_p_attr_obj_list(passiveSkills,
                petSnList, activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));
        dInfo.addAllAttrObjList(p_attr_objListAll);// p_attr_obj_list
        // TODO

        Define.p_role_figure.Builder figureBuilder = HumanManager.inst().to_p_role_figure(human,human2);
        if(relic == null){
            figureBuilder.addEquipList(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_6, 0));
        }else {
            ReliceData relicData = new ReliceData();
            relicData.planMapFromJsonString(relic.getTabMap());
            figureBuilder.addEquipList(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_6, relicData.getMaxLocation(relic.getTabCur())));
        }
        dFigure = figureBuilder.build();
        dInfo.setFigure(dFigure);
        dInfo.setManualOperator(Utils.getTimeSec());
//		dInfo.addAllOperators();//p_battle_operator
//		dInfo.addAllExt(); //p_key_value
        Define.p_head.Builder head = Define.p_head.newBuilder();
        head.setId(human.getHeadSn());
        head.setFrameId(human.getCurrentHeadFrameSn());
        head.setUrl("");
        dInfo.setHead(head);
        dInfo.setLeftHp(100);
        this.extList.addAll(HumanManager.inst().to_p_key_value_AngelSkill(human2));
        dInfo.addAllExt(HumanManager.inst().to_p_key_value_AngelSkill(human2));
        this.p_battle = dInfo.build();

        int tab = human2.getEquipTab();
        Equip equip = this.equip;
        if(equip != null){
            equips.clear();
            for (int i = weapon; i <= EquipInfo.max_part; ++i){
                String jsonString = EquipManager.inst().getEquipPart(equip,i);
                if(!Utils.isNullOrEmptyJSONString(jsonString)){
                    EquipInfo equipInfo = new EquipInfo();
                    equipInfo.fromJsonString(jsonString);
                    Define.p_equip.Builder p_equip = equipInfo.toProtoBuilder(tab);
                    equips.add(p_equip.build());
                }
            }
        }
        // 设置功能列表
        this.functionList = Utils.strToIntList(human2.getFunctionList());

        equipList();

        // 设置皮肤和属性
        skinList.add(HumanManager.inst().to_p_key_value(2, human2.getCurrentSkin()).build());
        dInfo.setManualOperator(Utils.getTimeSec());
        this.p_battle = dInfo.build();
    }

    public Define.p_battle_role toArena_p_battle_role(){
        return toArena_p_battle_role_builder().build();
    }
    public Define.p_battle_role.Builder toArena_p_battle_role_builder(){
        Define.p_battle_role.Builder dInfo = this.p_battle.toBuilder();
        dInfo.clearPetList();
        dInfo.clearAttrObjList();
        dInfo.addAllPetList(usePetList);
        List<Integer> activeSnList = new ArrayList<>();
        for(Define.p_active_skill dActive : activeSkills){
            activeSnList.add(dActive.getSkillId());
        }
        List<Integer> petSnList = new ArrayList<>();
        for(Define.p_role_pet dPet : usePetList){
            petSnList.add(dPet.getPetId());
        }
        PropCalc propCalc = new PropCalc();
        for(Define.p_key_value keyValue : attrList){
            propCalc.put(Utils.intValue(keyValue.getK()), BigDecimal.valueOf(keyValue.getV()));
        }
        List<Define.p_attr_obj_list> p_attr_objListAll = SkillManager.inst().to_p_attr_obj_list(passiveSkills,
                petSnList, activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));
        dInfo.addAllAttrObjList(p_attr_objListAll);// p_attr_obj_list
        return dInfo;
    }

    public TeamMember(String json) {
        if(json == null || json.isEmpty()){
            return;
        }
        isReady = true;
        json = StringZipUtils.unzip(json);

        JSONObject jo = Utils.toJSONObject(json);

        JSONObject joParam = jo.getJSONObject("param");
        param = new Param();
        if(joParam != null){
            for(String key : joParam.keySet()){
                param.put(key, joParam.get(key));
            }
        }
        human = new Human();
        human2 = new Human2();
        profession = new Profession();
        human.setServerId(Utils.intValue(param.get("zo")));
        human.setId(Utils.longValue(param.get("id")));
        humanId = jo.getLongValue("id");
        human.setLevel(param.get("lv"));
        human.setName(param.getString("na"));
        human.setCombat(param.get("combat").toString());
        profession.setJobSn(Utils.intValue(param.get("js")));
        activeSkills.clear();
        JSONArray activeJA = Utils.toJSONArray(jo.getString("active"));
        for(int i=0; i < activeJA.size(); i++){
            Define.p_active_skill.Builder dSkill = Define.p_active_skill.newBuilder();
            JSONObject tempJo = activeJA.getJSONObject(i);
            dSkill.setPosId(tempJo.getIntValue("pos"));
            dSkill.setSkillId(tempJo.getIntValue("id"));
            dSkill.setSkillLv(tempJo.getIntValue("lv"));
            dSkill.setDelayTime(tempJo.getIntValue("ti"));
            activeSkills.add(dSkill.build());
        }
        passiveSkills.clear();
        JSONArray passiveJA = Utils.toJSONArray(jo.getString("passive"));
        for(int i=0; i < passiveJA.size(); i++){
            Define.p_passive_skill.Builder dSkill = Define.p_passive_skill.newBuilder();
            JSONObject tempJo = passiveJA.getJSONObject(i);
            dSkill.setSkillId(tempJo.getIntValue("id"));
            dSkill.setSkillLv(tempJo.getIntValue("lv"));
            passiveSkills.add(dSkill.build());
        }
        petList.clear();
        JSONArray petStrListJA = Utils.toJSONArray(jo.getString("pet"));
        for(int i=0; i < petStrListJA.size(); i++){
            JSONObject tempJo = petStrListJA.getJSONObject(i);
            int petSn = tempJo.getIntValue("id");
            int petLv = tempJo.getIntValue("lv");
            int petPos = tempJo.getIntValue("pos");
            int skinSn = tempJo.getIntValue("skinSn");
            int skinLv = tempJo.getIntValue("skinLv");
            petList.add(PetManager.inst().to_p_role_pet(petPos, petSn, petLv, skinSn, skinLv));
        }

        usePetList.clear();
        JSONArray petStrListJAUse = Utils.toJSONArray(jo.getString("usePet"));
        for(int i=0; i < petStrListJAUse.size(); i++){
            JSONObject tempJo = petStrListJAUse.getJSONObject(i);
            int petSn = tempJo.getIntValue("id");
            int petLv = tempJo.getIntValue("lv");
            int petPos = tempJo.getIntValue("pos");
            int skinSn = tempJo.getIntValue("skinSn");
            int skinLv = tempJo.getIntValue("skinLv");
            usePetList.add(PetManager.inst().to_p_role_pet(petPos, petSn, petLv, skinSn, skinLv));
        }

        functionList = (List<Integer>) jo.get("funL");

        equipList.clear();
        equipList.addAll(to_p_key_value_List(Utils.toJSONArray(jo.getString("equipList"))));
        JSONArray equipsJa= Utils.toJSONArray(jo.getString("equips"));
        for(int i=0; i < equipsJa.size(); i++){
            JSONObject tempJo = equipsJa.getJSONObject(i);
            Define.p_equip.Builder dEquip = Define.p_equip.newBuilder();
            dEquip.setEquipId(tempJo.getLongValue("id"));
            dEquip.setConfigId(tempJo.getIntValue("cid"));
            dEquip.setEquipLv(tempJo.getIntValue("lv"));
            dEquip.setLocation(tempJo.getIntValue("lo"));
            dEquip.setTab(tempJo.getIntValue("tab"));
            dEquip.addAllBaseAttr(to_p_key_value_List(Utils.toJSONArray(tempJo.getString("ba"))));
            dEquip.addAllRandAttr(to_p_key_value_List(Utils.toJSONArray(tempJo.getString("ra"))));
            equips.add(dEquip.build());
        }

        skinList.clear();
        skinList.addAll(to_p_key_value_List(Utils.toJSONArray(jo.getString("skinL"))));

        attrList.clear();
        attrList.addAll(to_p_key_value_List(Utils.toJSONArray(jo.getString("attL"))));

        extList.clear();
        extList.addAll(to_p_key_value_List(Utils.toJSONArray(jo.getString("extL"))));

        Define.p_battle_role.Builder dInfo = Define.p_battle_role.newBuilder();
        dInfo.setId(human.getId());
        dInfo.setName(human.getName());
        dInfo.setLev(human.getLevel());
        dInfo.setJob(profession.getJobSn());

        dInfo.addAllPetList(petList);

        dInfo.addAllAttrList(attrList);

        List<Integer> activeSnList = new ArrayList<>();
        for(Define.p_active_skill dActive : activeSkills){
            activeSnList.add(dActive.getSkillId());
        }
        List<Integer> petSnList = new ArrayList<>();
        for(Define.p_role_pet dPet : petList){
            petSnList.add(dPet.getPetId());
        }

        PropCalc propCalc = new PropCalc();
        for(Define.p_key_value keyValue : attrList){
            propCalc.put(Utils.intValue(keyValue.getK()), BigDecimal.valueOf(keyValue.getV()));
        }
        List<Define.p_attr_obj_list> p_attr_objListAll = SkillManager.inst().to_p_attr_obj_list(passiveSkills,
                petSnList, activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));
        dInfo.addAllAttrObjList(p_attr_objListAll);// p_attr_obj_list

        Define.p_role_figure.Builder dFigur = Define.p_role_figure.newBuilder();
        dFigur.addAllEquipList(equipList);

        dFigur.setHairFigure(param.get("hf"));// 发色
        dFigur.setJobFigure(param.get("jf"));
        // 坐骑
        dFigur.setMountFigure(param.get("mf"));
        // 神器sn
        dFigur.setArtifactFigure(param.get("af"));
        dFigur.setGender(1);// 无性别 默认1
        // 皮肤
        dFigur.addAllSkinList(skinList);
        // 头衔
        dFigur.setCurrentTitle(param.get("ct"));
        dInfo.setFigure(dFigur.build());

        dInfo.setManualOperator(Utils.getTimeSec());
//		dInfo.addAllOperators();//p_battle_operator
		dInfo.addAllExt(extList); //p_key_value
        Define.p_head.Builder head = Define.p_head.newBuilder();
        long headId = 0;
        if(param.containsKey("hs")){
            Object obj = param.get("hs");
            headId= Utils.longValue(obj);
        }
        head.setId(headId);

        long frameId = 0;
        if(param.containsKey("fi")){
            Object obj = param.get("fi");
            frameId= Utils.longValue(obj);
        }
        head.setFrameId(frameId);
        head.setUrl(param.get("url"));
        dInfo.setHead(head);
        dInfo.setLeftHp(param.get("lh"));

        Define.p_role_skill.Builder dSkill = Define.p_role_skill.newBuilder();
        if(!activeSkills.isEmpty()){
            dSkill.addAllActiveSkill(activeSkills);
        }
        if(!passiveSkills.isEmpty()){
            dSkill.addAllPassiveSkill(passiveSkills);
        }
        dInfo.setRoleSkill(dSkill);
        if(dFigure == null){
            dFigure = dFigur.build();
        }

        this.p_battle = dInfo.build();
    }


    public String teamMemberToJSON(){
        JSONObject jo = new JSONObject();
//        jo.put("p_battle", new ArrayList<Define.p_battle_role>().add(p_battle));
//        jo.put("p_other", new ArrayList<>().add(to_p_other_role_info()));

        jo.put("active", toActiveSkill());
        jo.put("passive", toPassiveSkill());
        jo.put("pet", toPetList());
        jo.put("usePet", toUsePetList());
        jo.put("param", getParam().toJsonString());
        jo.put("funL", functionList);
        jo.put("equipList", to_p_key_value(equipList));
        jo.put("equips", to_equip());
        jo.put("skinL", to_p_key_value(skinList));
        jo.put("attL", to_p_key_value(attrList));
        jo.put("extL", to_p_key_value(extList));
        return jo.toJSONString();
    }

    private Param getParam(){
        Param param = new Param();
        param.put("id", humanId);
        param.put("lv", human.getLevel());
        param.put("na", human.getName());
        param.put("ms", human.getModelSn());
        param.put("js", profession.getJobSn());
        param.put("ch", human.getHpCur());
        param.put("mh", human.getHpMax());
        param.put("zo", human.getServerId());
        param.put("hf", human2.getHairColor());
        param.put("jf", human.getJobModel());
        param.put("mf", human2.getMountUse());
        param.put("af", human2.getArtifactUse());
        param.put("g", 1); // 无性别 默认1
        param.put("ct", human.getCurrentTitleSn());
        param.put("hs", human.getHeadSn());
        param.put("fi", human.getCurrentHeadFrameSn());
        param.put("url", "");
        param.put("lh", 100);
        param.put("combat", new BigDecimal(human.getCombat()).longValue());
        // 皮肤
        param.put("skin", human2.getCurrentSkin());
        // 聊天气泡
        param.put("bu", 1);

        return param;
    }

    public JSONArray to_equip(){
        JSONArray ja = new JSONArray();
        for(Define.p_equip dInfo : equips){
            JSONObject jo = new JSONObject();
            jo.put("id", dInfo.getEquipId());
            jo.put("cid", dInfo.getConfigId());
            jo.put("lv", dInfo.getEquipLv());
            jo.put("lo", dInfo.getLocation());
            jo.put("tab", dInfo.getTab());
            jo.put("ba", to_p_key_value(dInfo.getBaseAttrList()));
            jo.put("ra", to_p_key_value(dInfo.getRandAttrList()));
            ja.add(jo);
        }
        return ja;
    }

    public JSONArray to_p_key_value(List<Define.p_key_value> list){
        JSONArray ja = new JSONArray();
        for(Define.p_key_value dInfo : list){
            JSONObject jo = new JSONObject();
            jo.put("k", dInfo.getK());
            jo.put("v", dInfo.getV());
            ja.add(jo);
        }
        return ja;
    }

    public List<Define.p_key_value> to_p_key_value_List(JSONArray ja){
        List<Define.p_key_value> dInfoList = new ArrayList<>();
        for(int i=0; i < ja.size(); i++){
            JSONObject jo = ja.getJSONObject(i);
            dInfoList.add(HumanManager.inst().to_p_key_value(jo.getLongValue("k"), jo.getLongValue("v")).build());
        }
        return dInfoList;
    }

    public JSONArray toPetList(){
        JSONArray ja = new JSONArray();
        for(Define.p_role_pet dPet : petList){
            JSONObject jo = new JSONObject();
            jo.put("id", dPet.getPetId());
            jo.put("lv", dPet.getPetLev());
            jo.put("pos", dPet.getPetPos());
            jo.put("skin", dPet.getSkinId());
            jo.put("skinLv", dPet.getSkinLev());
            ja.add(jo);
        }
        return ja;
    }
    public JSONArray toUsePetList(){
        JSONArray ja = new JSONArray();
        for(Define.p_role_pet dPet : usePetList){
            JSONObject jo = new JSONObject();
            jo.put("id", dPet.getPetId());
            jo.put("lv", dPet.getPetLev());
            jo.put("pos", dPet.getPetPos());
            jo.put("skin", dPet.getSkinId());
            jo.put("skinLv", dPet.getSkinLev());
            ja.add(jo);
        }
        return ja;
    }

    public JSONArray toActiveSkill(){
        JSONArray ja = new JSONArray();
        for(Define.p_active_skill dSkill : activeSkills){
            JSONObject jo = new JSONObject();
            jo.put("pos", dSkill.getPosId());
            jo.put("id", dSkill.getSkillId());
            jo.put("lv", dSkill.getSkillLv());
            jo.put("ti", dSkill.getDelayTime());
            ja.add(jo);
        }
        return ja;
    }

    public JSONArray toPassiveSkill(){
        JSONArray ja = new JSONArray();
        for(Define.p_passive_skill dSkill : passiveSkills){
            JSONObject jo = new JSONObject();
            jo.put("id", dSkill.getSkillId());
            jo.put("lv", dSkill.getSkillLv());
            ja.add(jo);
        }
        return ja;
    }

    public Define.p_other_role_info to_p_other_role_info() {
        Define.p_other_role_info.Builder msg = Define.p_other_role_info.newBuilder();
        msg.setRoleId(human.getId());
        Define.p_role_change.Builder dRoleInfo = Define.p_role_change.newBuilder();
        dRoleInfo.addKs(HumanManager.inst().to_p_key_string(RoleInfoKey.ROLE_ATTR_NAME.getKey(), human.getName()));
        dRoleInfo.addKv(HumanManager.inst().to_p_key_value(RoleInfoKey.ROLE_ATTR_LVL.getKey(), human.getLevel()));
        msg.setInfoList(dRoleInfo);
        msg.addAllOtherEquipList(equips);
        if(dFigure != null){
            msg.setFigure(dFigure);
        } else {
            Define.p_role_figure.Builder dFigur = Define.p_role_figure.newBuilder();
            dFigur.addAllEquipList(equipList);

            dFigur.setHairFigure(human2.getHairColor());// 发色
            dFigur.setJobFigure(human.getJobModel());
            // 坐骑
            dFigur.setMountFigure(human2.getMountUse());
            // 神器sn
            dFigur.setArtifactFigure(human2.getArtifactUse());
            dFigur.setGender(1);// 无性别 默认1
            // 皮肤
            dFigur.addAllSkinList(skinList);
            // 头衔
            dFigur.setCurrentTitle(human.getCurrentTitleSn());

            dFigure = dFigur.build();
            msg.setFigure(dFigure);
        }
        msg.addAllPetList(petList);

        msg.addExt(HumanManager.inst().to_p_key_value(1, isReady ? 1 : 0)); //  repeated p_key_value ext = 7;
        msg.addAllFunctionList(functionList);
        msg.setFriendType(0);
        return msg.build();
    }

    public Define.p_battle_role to_p_battle_role(){
       return to_p_battle_role_builder().build();
    }
    public Define.p_battle_role.Builder to_p_battle_role_builder(){
        Define.p_battle_role.Builder dInfo = Define.p_battle_role.newBuilder();
        dInfo.setId(human.getId());
        dInfo.setName(human.getName());
        dInfo.setLev(human.getLevel());
        dInfo.setJob(profession.getJobSn());
        Define.p_role_skill.Builder dSkill = Define.p_role_skill.newBuilder();
        dSkill.addAllActiveSkill(activeSkills);
        dSkill.addAllPassiveSkill(passiveSkills);
        dInfo.setRoleSkill(dSkill);

        dInfo.addAllPetList(petList);

        dInfo.addAllAttrList(attrList);

        List<Integer> activeSnList = new ArrayList<>();
        for(Define.p_active_skill dActive : activeSkills){
            activeSnList.add(dActive.getSkillId());
        }
        List<Integer> petSnList = new ArrayList<>();
        for(Define.p_role_pet dPet : petList){
            petSnList.add(dPet.getPetId());
        }

        PropCalc propCalc = new PropCalc();
        for(Define.p_key_value keyValue : attrList){
            propCalc.put(Utils.intValue(keyValue.getK()), BigDecimal.valueOf(keyValue.getV()));
        }
        List<Define.p_attr_obj_list> p_attr_objListAll = SkillManager.inst().to_p_attr_obj_list(passiveSkills,
                petSnList, activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));

        dInfo.addAllAttrObjList(p_attr_objListAll);// p_attr_obj_list

        Define.p_role_figure.Builder dFigur = Define.p_role_figure.newBuilder();
        dFigur.addAllEquipList(equipList);

        dFigur.setHairFigure(human2.getHairColor());// 发色
        dFigur.setJobFigure(human.getJobModel());
        // 坐骑
        dFigur.setMountFigure(human2.getMountUse());
        // 神器sn
        dFigur.setArtifactFigure(human2.getArtifactUse());
        dFigur.setGender(1);// 无性别 默认1
        // 皮肤
        dFigur.addAllSkinList(skinList);
        // 头衔
        dFigur.setCurrentTitle(human.getCurrentTitleSn());

        dInfo.setFigure(dFigur);

        dInfo.setManualOperator(Utils.getTimeSec());
//		dInfo.addAllOperators();//p_battle_operator
		dInfo.addAllExt(extList); //p_key_value
        Define.p_head.Builder head = Define.p_head.newBuilder();
        head.setId(human.getHeadSn());

        long frameId = human.getCurrentHeadFrameSn();
        head.setFrameId(frameId);
        head.setUrl("");
        dInfo.setHead(head);
        dInfo.setLeftHp(100);

        return dInfo;
    }

    public Define.p_base_fighter to_p_base_fighter() {
        Define.p_base_fighter.Builder dInfo = Define.p_base_fighter.newBuilder();
        dInfo.setId(human.getId());
        dInfo.setName(human.getName());
        dInfo.setLev(human.getLevel());
        dInfo.setJob(profession.getJobSn());
        dInfo.setPower(new BigDecimal(human.getCombat()).longValue());
        dInfo.setFigure(dFigure);

        Define.p_role_skill.Builder dSkill = Define.p_role_skill.newBuilder();
        dSkill.addAllActiveSkill(activeSkills);
        dSkill.addAllPassiveSkill(passiveSkills);
        dInfo.setRoleSkill(dSkill);

        dInfo.addAllPetList(usePetList);
        dInfo.addAllAttrList(attrList);

        List<Integer> activeSnList = new ArrayList<>();
        for(Define.p_active_skill dActive : activeSkills){
            activeSnList.add(dActive.getSkillId());
        }
        List<Integer> petSnList = new ArrayList<>();
        for(Define.p_role_pet dPet : usePetList){
            petSnList.add(dPet.getPetId());
        }
        PropCalc propCalc = new PropCalc();
        for(Define.p_key_value keyValue : attrList){
            propCalc.put(Utils.intValue(keyValue.getK()), BigDecimal.valueOf(keyValue.getV()));
        }
        List<Define.p_attr_obj_list> p_attr_objListAll = SkillManager.inst().to_p_attr_obj_list(passiveSkills,
                petSnList, activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));
        dInfo.addAllAttrObjList(p_attr_objListAll);// p_attr_obj_list
        dInfo.addAllExt(this.p_battle.getExtList());
        return dInfo.build();
    }

    public Define.p_role_change to_p_role_change(){
        Define.p_role_change.Builder dRoleInfo = Define.p_role_change.newBuilder();
        dRoleInfo.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_ID.getKey()).setV(human.getId()));
        dRoleInfo.addKs(Define.p_key_string.newBuilder().setK(RoleInfoKey.ROLE_ATTR_NAME.getKey()).setS(human.getName()));
        dRoleInfo.addKs(Define.p_key_string.newBuilder().setK(RoleInfoKey.ROLE_ATTR_POWER_SHOW.getKey()).setS(human.getCombat()));
        dRoleInfo.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_CUR_JOB.getKey()).setV(profession.getJobSn()));
        dRoleInfo.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_LVL.getKey()).setV(human.getLevel()));
        dRoleInfo.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_GENDER.getKey()).setV(0));
        dRoleInfo.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_HEAD_ID.getKey()).setV(human.getHeadSn()));
        dRoleInfo.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_HEAD_FRAME_ID.getKey()).setV(human.getCurrentHeadFrameSn()));
        dRoleInfo.addKs(Define.p_key_string.newBuilder().setK(RoleInfoKey.ROLE_ATTR_HEAD_URL.getKey()).setS(""));
        dRoleInfo.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_SERVER.getKey()).setV(Utils.getServerIdTo(human.getServerId())));
        return dRoleInfo.build();
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        humanId = in.read();
        human = in.read();
        human2 = in.read();
        profession = in.read();
        atf = in.read();
        mount = in.read();
        wing = in.read();
        relic = in.read();
        fate = in.read();
        equip = in.read();
        unitPropPlus = in.read();
        isRobot = in.read();
        isCopy = in.read();
        isReady = in.read();
        json = in.read();
        nowDiff = in.read();
        param = in.read();
        infoList = in.read();
        p_battle = in.read();
        activeSkills = in.read();
        passiveSkills = in.read();
        equips = in.read();
        dFigure = in.read();
        functionList = in.read();
        petList = in.read();
        equipList = in.read();
        skinList = in.read();
        attrList = in.read();
        extList = in.read();
        usePetList = in.read();
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(humanId);
        out.write(human);
        out.write(human2);
        out.write(profession);
        out.write(atf);
        out.write(mount);
        out.write(wing);
        out.write(relic);
        out.write(fate);
        out.write(equip);
        out.write(unitPropPlus);
        out.write(isRobot);
        out.write(isCopy);
        out.write(isReady);
        out.write(json);
        out.write(nowDiff);
        out.write(param);
        out.write(infoList);
        out.write(p_battle);
        out.write(activeSkills);
        out.write(passiveSkills);
        out.write(equips);
        out.write(dFigure);
        out.write(functionList);
        out.write(petList);
        out.write(equipList);
        out.write(skinList);
        out.write(attrList);
        out.write(extList);
        out.write(usePetList);
    }

    public Define.p_farm_battle_role to_p_farm_battle_role(){
        Define.p_farm_battle_role.Builder dInfo = Define.p_farm_battle_role.newBuilder();
        dInfo.setId(human.getId());
        dInfo.setName(human.getName());
        dInfo.setLev(human.getLevel());
        dInfo.setJob(profession.getJobSn());
        dInfo.setPower(new BigDecimal(human.getCombat()).longValue());
        dInfo.setFigure(dFigure);
        if(p_battle != null){
            p_battle = toArena_p_battle_role();
            dInfo.setRoleSkill(p_battle.getRoleSkill());
            dInfo.addAllPetList(p_battle.getPetListList());
            dInfo.addAllAttrObjList(p_battle.getAttrObjListList());
        }
        dInfo.addAllEquipList(equips);
        dInfo.addAllAttrList(attrList);
        return dInfo.build();
    }
}
